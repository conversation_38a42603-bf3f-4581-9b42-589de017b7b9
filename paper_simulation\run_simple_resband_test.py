#!/usr/bin/env python3
"""
简单的ResBand测试
"""

def run_simple_resband_test():
    """运行简单的ResBand测试"""
    print("🎰 运行简单ResBand测试")
    print("=" * 50)
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建简化的实验配置
        experiment = ResBandVerificationExperiment("simple_test_output")
        
        # 修改配置为更小的测试
        experiment.config.update({
            "training_episodes": 10,  # 减少到10个episodes
            "evaluation_episodes": 5,  # 减少到5个评估episodes
            "max_steps_per_episode": 50,  # 减少到50步
            "episodes_per_stage": 3,  # 每个阶段只有3个episodes
            "comparison_methods": ["resband_adaptive", "fixed_coarse"]  # 只测试2种方法
        })
        
        print(f"📋 简化配置:")
        print(f"   - 训练episodes: {experiment.config['training_episodes']}")
        print(f"   - 每阶段episodes: {experiment.config['episodes_per_stage']}")
        print(f"   - 对比方法: {experiment.config['comparison_methods']}")
        
        # 运行实验
        print("\n🚀 开始简化实验...")
        results = experiment.run_experiment()
        
        if results:
            print("\n🎉 简化实验完成！")
            print("📊 结果摘要:")
            
            for method in experiment.config["comparison_methods"]:
                if method in results.get("performance_comparison", {}):
                    perf = results["performance_comparison"][method]["final_performance"]
                    print(f"   {method}:")
                    print(f"     - 平均奖励: {perf.get('avg_reward', 0):.2f}")
                    print(f"     - 成功率: {perf.get('success_rate', 0):.2%}")
        else:
            print("❌ 实验被取消或失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化实验失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = run_simple_resband_test()
    
    if success:
        print("\n✅ 简化测试成功！")
        print("🚀 现在可以运行完整实验:")
        print("   python experiments/individual/experiment_2_resband_verification.py --auto")
    else:
        print("\n❌ 简化测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
