#!/usr/bin/env python3
"""
调试状态累积问题
检查环境状态是否在episodes之间正确重置
"""

def debug_state_accumulation():
    """调试状态累积问题"""
    print("🔍 调试状态累积问题")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        
        print(f"✅ 环境创建成功")
        print(f"📋 物理配置:")
        print(f"   时间步长: {env.physics_config['time_step']}秒")
        print(f"   巡航速度: {env.physics_config['V_cruise']} m/s")
        print(f"   预期每步移动: {env.physics_config['V_cruise'] * env.physics_config['time_step']:.1f}米")
        
        # 测试多个episodes
        for episode in range(3):
            print(f"\n🎯 Episode {episode + 1}")
            print("=" * 40)
            
            # 重置环境
            print(f"重置前环境状态: {env.state}")
            state = env.reset()
            print(f"重置后环境状态: {env.state}")
            print(f"返回的状态: {state}")
            print(f"step_count: {env.step_count}")
            
            # 执行一步
            action = np.array([2.0, 5.0, 0.1])
            print(f"\n执行动作: {action}")
            print(f"执行前状态: {env.state}")
            
            next_state, reward, done, info = env.step(action)
            
            print(f"执行后状态: {env.state}")
            print(f"返回的next_state: {next_state}")
            print(f"step_count: {env.step_count}")
            
            # 计算移动距离
            movement = np.linalg.norm(next_state[:3] - state[:3])
            print(f"移动距离: {movement:.3f}米")
            print(f"奖励: {reward:.3f}")
            print(f"完成: {done}")
            print(f"信息: {info}")
            
            # 分析异常移动
            if movement > 10:  # 如果移动超过10米，说明有问题
                print(f"\n🚨 异常移动检测:")
                print(f"   预期移动: ~2.5米")
                print(f"   实际移动: {movement:.1f}米")
                print(f"   异常倍数: {movement/2.5:.1f}x")
                
                # 检查速度和角度
                V, gamma, psi = env.state[3], env.state[4], env.state[5]
                print(f"   当前速度: {V:.1f} m/s")
                print(f"   倾斜角: {gamma:.3f} rad ({np.degrees(gamma):.1f}°)")
                print(f"   偏航角: {psi:.3f} rad ({np.degrees(psi):.1f}°)")
                
                # 计算理论移动距离
                dt = env.physics_config["time_step"]
                x_dot = V * np.cos(gamma) * np.cos(psi)
                y_dot = V * np.cos(gamma) * np.sin(psi)
                z_dot = V * np.sin(gamma)
                theoretical_movement = np.sqrt(x_dot**2 + y_dot**2 + z_dot**2) * dt
                print(f"   理论移动: {theoretical_movement:.3f}米")
                
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_physics_simulation():
    """调试物理模拟"""
    print("\n🔬 调试物理模拟")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        
        # 重置环境
        state = env.reset()
        print(f"初始状态: {state}")
        print(f"起点: {env.start_pos}")
        print(f"终点: {env.target_pos}")
        
        # 手动调用物理模拟
        print(f"\n🔬 手动调用物理模拟:")
        print(f"调用前状态: {env.state}")
        
        # 使用小的动作
        a_T, a_N, mu = 0.0, 0.0, 0.0  # 零动作
        env._update_six_dof_dynamics(a_T, a_N, mu)
        
        print(f"调用后状态: {env.state}")
        
        # 计算移动距离
        movement = np.linalg.norm(env.state[:3] - state[:3])
        print(f"零动作移动距离: {movement:.6f}米")
        
        # 如果零动作都有移动，说明有问题
        if movement > 0.001:
            print(f"🚨 零动作不应该有移动！")
            return False
        
        # 测试正常动作
        env.state = state.copy()  # 重置状态
        a_T, a_N, mu = 2.0, 5.0, 0.1
        print(f"\n测试动作: a_T={a_T}, a_N={a_N}, mu={mu}")
        print(f"执行前状态: {env.state}")
        
        env._update_six_dof_dynamics(a_T, a_N, mu)
        
        print(f"执行后状态: {env.state}")
        movement = np.linalg.norm(env.state[:3] - state[:3])
        print(f"移动距离: {movement:.3f}米")
        
        # 检查是否合理
        dt = env.physics_config["time_step"]
        V = state[3]  # 初始速度
        expected_movement = V * dt  # 大约的预期移动
        print(f"预期移动: ~{expected_movement:.1f}米")
        
        if movement > expected_movement * 3:  # 如果移动超过预期3倍
            print(f"🚨 移动距离异常！")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 物理模拟调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 状态累积问题专项调试")
    
    # 调试状态累积
    accumulation_success = debug_state_accumulation()
    
    # 调试物理模拟
    physics_success = debug_physics_simulation()
    
    if accumulation_success and physics_success:
        print("\n" + "=" * 80)
        print("🎯 状态累积调试完成！")
        print("环境状态管理正常")
    else:
        print("\n❌ 发现状态累积问题")
        print("需要修复环境状态管理")

if __name__ == "__main__":
    main()
