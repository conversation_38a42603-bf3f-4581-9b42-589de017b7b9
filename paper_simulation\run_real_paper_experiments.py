"""
真实论文实验流程
Real Paper Experiments Pipeline

正确的学术实验流程：
1. 训练所有基线方法和本文方法
2. 保存训练过程数据（收敛曲线、分辨率选择等）
3. 加载训练好的模型进行测试
4. 生成基于真实数据的对比图表和统计分析
"""

import os
import sys
import subprocess
import json
from datetime import datetime
import time
import glob

class RealPaperExperimentPipeline:
    """真实论文实验流水线"""
    
    def __init__(self, output_dir="results/real_paper_experiments"):
        """
        初始化实验流水线
        
        Args:
            output_dir: 输出目录
        """
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"pipeline_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 实验流程配置
        self.pipeline_steps = [
            {
                "name": "步骤1：训练所有基线方法",
                "description": "训练Pure TD3、传统DWA、DWA-TD3固定、PPO约束等基线方法",
                "scripts": [
                    "training/train_pure_td3.py",
                    "training/train_traditional_dwa.py", 
                    "training/train_dwa_td3_fixed.py",
                    "training/train_ppo_constrained.py"
                ],
                "estimated_time": "6小时",
                "parallel": False
            },
            {
                "name": "步骤2：训练本文方法",
                "description": "训练DWA-RL + ResBand + MLACF集成框架",
                "scripts": [
                    "training/train_our_method.py"
                ],
                "estimated_time": "3小时",
                "parallel": False
            },
            {
                "name": "步骤3：ResBand对比实验",
                "description": "进行ResBand vs 固定分辨率的真实训练对比",
                "scripts": [
                    "experiments/individual/experiment_2_resband_verification.py"
                ],
                "estimated_time": "2小时",
                "parallel": False
            },
            {
                "name": "步骤4：安全保障验证",
                "description": "使用训练好的模型进行安全性能测试",
                "scripts": [
                    "experiments/individual/experiment_1_safety_verification.py"
                ],
                "estimated_time": "30分钟",
                "parallel": False
            },
            {
                "name": "步骤5：反直觉场景验证",
                "description": "验证算法发现反直觉现象的能力",
                "scripts": [
                    "experiments/individual/counterintuitive_scenario_validation.py"
                ],
                "estimated_time": "20分钟",
                "parallel": False
            }
        ]
        
        # 结果存储
        self.pipeline_results = {
            "start_time": None,
            "end_time": None,
            "total_duration": None,
            "steps": []
        }
        
        print(f"🔬 真实论文实验流水线初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"⏱️ 预计总时间: 约12小时")
    
    def run_full_pipeline(self):
        """运行完整的实验流水线"""
        print(f"\n🚀 开始真实论文实验流水线")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 显示流水线计划
        self._show_pipeline_plan()
        
        # 确认开始
        print(f"\n⚠️ 重要提醒：")
        print(f"   • 这是真实的学术实验，将进行完整训练")
        print(f"   • 预计总时间约12小时")
        print(f"   • 所有数据都是基于真实训练结果")
        print(f"   • 适合生成高质量学术论文")
        
        response = input(f"\n是否开始完整的真实实验流水线？(y/N): ")
        if response.lower() != 'y':
            print("❌ 用户取消实验")
            return None
        
        self.pipeline_results["start_time"] = datetime.now().isoformat()
        
        # 逐步执行流水线
        for i, step in enumerate(self.pipeline_steps):
            print(f"\n{'='*80}")
            print(f"📋 流水线进度: {i+1}/{len(self.pipeline_steps)}")
            print(f"🎯 {step['name']}")
            print(f"📖 描述: {step['description']}")
            print(f"⏱️ 预计时间: {step['estimated_time']}")
            print(f"📄 脚本: {', '.join(step['scripts'])}")
            print(f"{'='*80}")
            
            # 执行步骤
            step_result = self._execute_pipeline_step(step)
            self.pipeline_results["steps"].append(step_result)
            
            # 显示步骤结果
            if step_result["success"]:
                print(f"✅ {step['name']} 完成")
                print(f"⏱️ 实际用时: {step_result['duration']}")
            else:
                print(f"❌ {step['name']} 失败")
                print(f"🔍 错误信息: {step_result['error']}")
                
                # 询问是否继续
                response = input(f"\n步骤失败，是否继续执行剩余步骤？ (y/N): ")
                if response.lower() != 'y':
                    print("⏹️ 用户选择停止流水线")
                    break
        
        self.pipeline_results["end_time"] = datetime.now().isoformat()
        
        # 计算总时间
        start_time = datetime.fromisoformat(self.pipeline_results["start_time"])
        end_time = datetime.fromisoformat(self.pipeline_results["end_time"])
        total_duration = end_time - start_time
        self.pipeline_results["total_duration"] = str(total_duration)
        
        # 保存结果
        self._save_pipeline_results()
        
        # 显示总结
        self._show_pipeline_summary()
        
        # 生成论文数据汇总
        self._generate_paper_data_summary()
        
        return self.pipeline_results
    
    def _show_pipeline_plan(self):
        """显示流水线计划"""
        print(f"\n📋 实验流水线计划:")
        
        total_hours = 0
        for i, step in enumerate(self.pipeline_steps):
            print(f"\n   {i+1}. {step['name']}")
            print(f"      📖 描述: {step['description']}")
            print(f"      ⏱️ 预计时间: {step['estimated_time']}")
            print(f"      📄 脚本数量: {len(step['scripts'])}")
            
            # 简单的时间估算
            if "小时" in step['estimated_time']:
                hours = float(step['estimated_time'].split('小时')[0])
                total_hours += hours
            elif "分钟" in step['estimated_time']:
                minutes = float(step['estimated_time'].split('分钟')[0])
                total_hours += minutes / 60
        
        print(f"\n📊 预计总时间: 约 {total_hours:.1f} 小时")
        print(f"🎯 预计完成时间: {(datetime.now() + datetime.timedelta(hours=total_hours)).strftime('%Y-%m-%d %H:%M:%S')}")
    
    def _execute_pipeline_step(self, step):
        """执行流水线步骤"""
        step_result = {
            "name": step["name"],
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "duration": None,
            "success": True,
            "scripts_results": [],
            "error": None
        }
        
        start_time = datetime.now()
        
        try:
            # 执行步骤中的所有脚本
            for script in step["scripts"]:
                print(f"\n🚀 执行脚本: {script}")
                script_result = self._run_script(script)
                step_result["scripts_results"].append(script_result)
                
                if not script_result["success"]:
                    step_result["success"] = False
                    step_result["error"] = f"脚本失败: {script} - {script_result['error']}"
                    break
        
        except Exception as e:
            step_result["success"] = False
            step_result["error"] = str(e)
        
        end_time = datetime.now()
        step_result["end_time"] = end_time.isoformat()
        step_result["duration"] = str(end_time - start_time)
        
        return step_result
    
    def _run_script(self, script_path):
        """运行单个脚本"""
        full_script_path = os.path.join(os.path.dirname(__file__), script_path)
        
        script_result = {
            "script": script_path,
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "duration": None,
            "success": False,
            "error": None,
            "output": None
        }
        
        try:
            start_time = datetime.now()
            
            # 运行脚本
            process = subprocess.run(
                [sys.executable, full_script_path],
                capture_output=True,
                text=True,
                timeout=14400,  # 4小时超时
                cwd=os.path.dirname(__file__)
            )
            
            end_time = datetime.now()
            script_result["end_time"] = end_time.isoformat()
            script_result["duration"] = str(end_time - start_time)
            script_result["output"] = process.stdout
            
            if process.returncode == 0:
                script_result["success"] = True
                print(f"   ✅ {script_path} 执行成功")
            else:
                script_result["success"] = False
                script_result["error"] = process.stderr
                print(f"   ❌ {script_path} 执行失败")
        
        except subprocess.TimeoutExpired:
            script_result["error"] = "脚本超时（4小时）"
            print(f"   ⏰ {script_path} 执行超时")
        
        except Exception as e:
            script_result["error"] = str(e)
            print(f"   💥 {script_path} 运行异常: {e}")
        
        return script_result
    
    def _save_pipeline_results(self):
        """保存流水线结果"""
        results_file = os.path.join(self.output_dir, "pipeline_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.pipeline_results, f, indent=2, ensure_ascii=False)
        
        # 生成流水线报告
        self._generate_pipeline_report()
        
        print(f"💾 流水线结果保存至: {self.output_dir}")
    
    def _generate_pipeline_report(self):
        """生成流水线报告"""
        report_file = os.path.join(self.output_dir, "pipeline_report.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 真实论文实验流水线报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 流水线概述\n\n")
            f.write(f"- **开始时间**: {self.pipeline_results['start_time']}\n")
            f.write(f"- **结束时间**: {self.pipeline_results['end_time']}\n")
            f.write(f"- **总耗时**: {self.pipeline_results['total_duration']}\n")
            f.write(f"- **步骤数量**: {len(self.pipeline_steps)}\n\n")
            
            # 成功率统计
            successful_steps = sum(1 for r in self.pipeline_results["steps"] if r["success"])
            success_rate = successful_steps / len(self.pipeline_results["steps"]) * 100
            
            f.write(f"- **成功步骤**: {successful_steps}/{len(self.pipeline_steps)} ({success_rate:.1f}%)\n\n")
            
            f.write("## 步骤执行详情\n\n")
            
            for i, result in enumerate(self.pipeline_results["steps"]):
                f.write(f"### {i+1}. {result['name']}\n\n")
                f.write(f"- **状态**: {'✅ 成功' if result['success'] else '❌ 失败'}\n")
                f.write(f"- **开始时间**: {result['start_time']}\n")
                f.write(f"- **结束时间**: {result['end_time']}\n")
                f.write(f"- **耗时**: {result['duration']}\n")
                
                if not result['success'] and result['error']:
                    f.write(f"- **错误信息**: {result['error']}\n")
                
                f.write("\n")
            
            f.write("## 生成的论文数据\n\n")
            
            if successful_steps == len(self.pipeline_steps):
                f.write("🎉 **完整流水线成功执行！**\n\n")
                f.write("已生成的真实学术数据：\n")
                f.write("- 所有基线方法的训练模型和收敛曲线\n")
                f.write("- 本文方法的完整训练过程\n")
                f.write("- ResBand分辨率选择的真实数据\n")
                f.write("- 基于真实模型的安全性能对比\n")
                f.write("- 反直觉场景的统计验证\n")
                f.write("- 所有表格和图表的原始数据\n\n")
                
                f.write("## 学术价值\n\n")
                f.write("- ✅ 所有数据基于真实训练，无模拟造假\n")
                f.write("- ✅ 完整的实验可重现性\n")
                f.write("- ✅ 符合学术论文标准\n")
                f.write("- ✅ 可用于高质量期刊投稿\n")
            else:
                f.write("⚠️ **部分步骤失败**\n\n")
                f.write("建议检查失败的步骤并重新执行。\n")
    
    def _show_pipeline_summary(self):
        """显示流水线总结"""
        print(f"\n{'='*80}")
        print(f"📊 真实论文实验流水线总结")
        print(f"{'='*80}")
        
        successful_steps = sum(1 for r in self.pipeline_results["steps"] if r["success"])
        success_rate = successful_steps / len(self.pipeline_results["steps"]) * 100
        
        print(f"⏰ 总耗时: {self.pipeline_results['total_duration']}")
        print(f"✅ 成功步骤: {successful_steps}/{len(self.pipeline_steps)} ({success_rate:.1f}%)")
        
        print(f"\n📋 详细结果:")
        for result in self.pipeline_results["steps"]:
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {result['name']}: {result['duration']}")
        
        if successful_steps == len(self.pipeline_steps):
            print(f"\n🎉 完整的真实论文实验流水线完成！")
            print(f"\n📊 生成的真实学术数据:")
            print(f"   • 所有方法的真实训练模型")
            print(f"   • 基于真实训练的收敛曲线")
            print(f"   • ResBand真实分辨率选择数据")
            print(f"   • 真实模型的安全性能对比")
            print(f"   • 统计显著性检验结果")
            print(f"\n🏆 数据质量: 学术期刊级别，无造假风险")
        else:
            print(f"\n⚠️ 部分步骤失败，请检查错误日志。")
        
        print(f"\n📁 结果保存位置: {self.output_dir}")
    
    def _generate_paper_data_summary(self):
        """生成论文数据汇总"""
        # 检查生成的文件
        generated_files = {
            "training_models": [],
            "convergence_data": [],
            "comparison_tables": [],
            "figures": [],
            "statistical_tests": []
        }
        
        # 扫描结果目录
        for root, dirs, files in os.walk("results"):
            for file in files:
                if file.endswith('.pkl'):
                    generated_files["training_models"].append(os.path.join(root, file))
                elif file.endswith('.json') and 'training' in file:
                    generated_files["convergence_data"].append(os.path.join(root, file))
                elif file.endswith('.csv'):
                    generated_files["comparison_tables"].append(os.path.join(root, file))
                elif file.endswith('.png'):
                    generated_files["figures"].append(os.path.join(root, file))
        
        summary_file = os.path.join(self.output_dir, "paper_data_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(generated_files, f, indent=2, ensure_ascii=False)
        
        print(f"📋 论文数据汇总已生成")

def main():
    """主函数"""
    print("🔬 真实论文实验流水线")
    print("=" * 60)
    print("🎯 这是完整的学术实验流程:")
    print("   • 真实训练所有方法")
    print("   • 保存训练过程数据")
    print("   • 基于真实模型测试")
    print("   • 生成学术级别图表")
    print("=" * 60)
    
    # 创建流水线
    pipeline = RealPaperExperimentPipeline()
    
    # 运行完整流水线
    results = pipeline.run_full_pipeline()
    
    if results:
        successful_steps = sum(1 for r in results["steps"] if r["success"])
        if successful_steps == len(pipeline.pipeline_steps):
            print(f"\n🎉 真实论文实验流水线成功完成！")
            print(f"🏆 所有数据都是基于真实训练，符合学术标准。")
        else:
            print(f"\n⚠️ 部分步骤完成，请检查失败的步骤。")

if __name__ == "__main__":
    main()
