"""
论文实验启动脚本
Paper Experiments Launcher

一键启动所有论文实验的主入口
"""

import os
import sys
import time
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(__file__))

def main():
    """主函数"""
    print("🚀 论文仿真实验系统")
    print("=" * 60)
    print("📋 选择运行模式:")
    print("   1. 快速验证 - 验证系统功能 (5分钟)")
    print("   2. 完整训练 - 训练所有算法 (10小时)")
    print("   3. 对比实验 - 使用预训练模型对比 (1小时)")
    print("   4. 反直觉验证 - 验证反直觉场景发现 (30分钟)")
    print("   5. 生成图表 - 生成论文图表 (10分钟)")
    print("=" * 60)
    
    # 确认执行
    response = input("🤔 是否开始执行所有实验? (y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("❌ 实验已取消")
        return
    
    print(f"\n⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    start_time = time.time()
    
    try:
        # 导入并运行实验
        from experiments.run_all_experiments import PaperExperimentRunner
        
        runner = PaperExperimentRunner()
        runner.run_all_experiments()
        
        # 计算总时间
        total_time = time.time() - start_time
        print(f"\n✅ 所有实验完成!")
        print(f"⏱️ 总耗时: {total_time/3600:.2f}小时")
        print(f"📁 结果保存在: {runner.experiment_dir}")
        
        # 显示关键结果
        print(f"\n📊 关键结果预览:")
        if "experiment_2_resband_algorithm" in runner.experiment_results:
            resband_result = runner.experiment_results["experiment_2_resband_algorithm"]
            if "resband" in resband_result:
                print(f"   ResBand成功率: {resband_result['resband']['performance'].get('success_rate', 0):.2f}")
        
        if "pseudo_complexity_identification" in runner.experiment_results:
            pseudo_result = runner.experiment_results["pseudo_complexity_identification"]
            if "summary" in pseudo_result:
                print(f"   伪复杂性识别准确率: {pseudo_result['summary'].get('identification_accuracy', 0):.2f}")
        
        print(f"\n📝 详细报告请查看:")
        print(f"   - 实验报告: {runner.experiment_dir}/experiment_report.md")
        print(f"   - 统计分析: {runner.experiment_dir}/statistical_report.md")
        print(f"   - 原始数据: {runner.experiment_dir}/data/")
        
    except Exception as e:
        print(f"❌ 实验执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
