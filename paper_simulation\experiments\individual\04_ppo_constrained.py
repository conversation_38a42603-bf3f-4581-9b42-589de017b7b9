#!/usr/bin/env python3
"""
独立实验4: PPO约束算法训练
基线对比方法，使用PPO+约束优化

运行方式:
1. 新训练: python 04_ppo_constrained.py
2. 恢复训练: python 04_ppo_constrained.py --resume
3. 从指定检查点恢复: python 04_ppo_constrained.py --resume --checkpoint /path/to/checkpoint

特点:
- PPO强化学习 + 约束优化
- 用于对比验证不同强化学习算法的效果
- 支持完整的中断恢复功能
"""

import os
import sys
import argparse
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import time
import json
from pathlib import Path
from datetime import datetime
from collections import deque

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 添加巡飞简化ver路径以使用真实实现
loitering_path = project_root / "巡飞简化ver"
sys.path.append(str(loitering_path))

from paper_simulation.experiments.utils.base_trainer import BaseTrainer

# 导入巡飞简化ver的真实实现 - PPO约束需要环境和自定义PPO控制器
from loitering_munition_environment import LoiteringMunitionEnvironment
from environment_config import (
    get_environment_config, get_training_stage_config,
    get_loitering_munition_config, get_training_config
)

class ConstrainedPPOController:
    """约束PPO控制器 - 基于TD3框架修改为PPO+约束优化"""
    
    def __init__(self, config):
        self.config = config
        self.state_dim = config['state_dim']
        self.action_dim = config['action_dim']
        self.hidden_sizes = config.get('hidden_sizes', [256, 256])
        self.lr = config.get('learning_rate', 3e-4)
        self.gamma = config.get('gamma', 0.99)
        self.eps_clip = config.get('eps_clip', 0.2)
        self.k_epochs = config.get('k_epochs', 4)
        self.constraint_penalty = config.get('constraint_penalty', 10.0)
        
        # 创建网络
        self.actor = self._build_actor()
        self.critic = self._build_critic()
        self.constraint_critic = self._build_constraint_critic()
        
        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=self.lr)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=self.lr)
        self.constraint_optimizer = optim.Adam(self.constraint_critic.parameters(), lr=self.lr)
        
        # 经验缓冲
        self.memory = []
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 移动到设备
        self.actor.to(self.device)
        self.critic.to(self.device)
        self.constraint_critic.to(self.device)
    
    def _build_actor(self):
        """构建Actor网络"""
        layers = []
        input_dim = self.state_dim
        
        for hidden_size in self.hidden_sizes:
            layers.extend([
                nn.Linear(input_dim, hidden_size),
                nn.ReLU()
            ])
            input_dim = hidden_size
        
        # 输出层
        layers.append(nn.Linear(input_dim, self.action_dim))
        layers.append(nn.Tanh())  # 动作范围 [-1, 1]
        
        return nn.Sequential(*layers)
    
    def _build_critic(self):
        """构建Critic网络（价值函数）"""
        layers = []
        input_dim = self.state_dim
        
        for hidden_size in self.hidden_sizes:
            layers.extend([
                nn.Linear(input_dim, hidden_size),
                nn.ReLU()
            ])
            input_dim = hidden_size
        
        layers.append(nn.Linear(input_dim, 1))
        
        return nn.Sequential(*layers)
    
    def _build_constraint_critic(self):
        """构建约束Critic网络（约束价值函数）"""
        layers = []
        input_dim = self.state_dim
        
        for hidden_size in self.hidden_sizes:
            layers.extend([
                nn.Linear(input_dim, hidden_size),
                nn.ReLU()
            ])
            input_dim = hidden_size
        
        layers.append(nn.Linear(input_dim, 1))
        
        return nn.Sequential(*layers)
    
    def select_action(self, state):
        """选择动作"""
        state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            action = self.actor(state)
            # 添加探索噪声
            noise = torch.normal(0, 0.1, size=action.shape).to(self.device)
            action = torch.clamp(action + noise, -1, 1)
        
        return action.cpu().numpy().flatten()
    
    def store_transition(self, state, action, reward, next_state, done, constraint_cost=0):
        """存储经验"""
        self.memory.append({
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done,
            'constraint_cost': constraint_cost
        })
    
    def can_update(self):
        """检查是否可以更新"""
        return len(self.memory) >= 2048  # PPO通常使用较大的batch
    
    def update(self):
        """PPO约束优化更新"""
        if not self.can_update():
            return
        
        # 准备数据
        states = torch.FloatTensor([t['state'] for t in self.memory]).to(self.device)
        actions = torch.FloatTensor([t['action'] for t in self.memory]).to(self.device)
        rewards = torch.FloatTensor([t['reward'] for t in self.memory]).to(self.device)
        next_states = torch.FloatTensor([t['next_state'] for t in self.memory]).to(self.device)
        dones = torch.BoolTensor([t['done'] for t in self.memory]).to(self.device)
        constraint_costs = torch.FloatTensor([t['constraint_cost'] for t in self.memory]).to(self.device)
        
        # 计算优势函数
        with torch.no_grad():
            values = self.critic(states).squeeze()
            next_values = self.critic(next_states).squeeze()
            
            # 计算TD目标
            td_targets = rewards + self.gamma * next_values * (~dones)
            advantages = td_targets - values
            
            # 约束优势
            constraint_values = self.constraint_critic(states).squeeze()
            next_constraint_values = self.constraint_critic(next_states).squeeze()
            constraint_targets = constraint_costs + self.gamma * next_constraint_values * (~dones)
            constraint_advantages = constraint_targets - constraint_values
        
        # PPO更新
        for _ in range(self.k_epochs):
            # Actor更新
            current_actions = self.actor(states)
            
            # 计算策略损失（包含约束惩罚）
            action_loss = F.mse_loss(current_actions, actions)
            constraint_penalty = self.constraint_penalty * torch.mean(constraint_advantages.detach() * action_loss)
            
            actor_loss = action_loss + constraint_penalty
            
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()
            
            # Critic更新
            current_values = self.critic(states).squeeze()
            critic_loss = F.mse_loss(current_values, td_targets.detach())
            
            self.critic_optimizer.zero_grad()
            critic_loss.backward()
            self.critic_optimizer.step()
            
            # 约束Critic更新
            current_constraint_values = self.constraint_critic(states).squeeze()
            constraint_critic_loss = F.mse_loss(current_constraint_values, constraint_targets.detach())
            
            self.constraint_optimizer.zero_grad()
            constraint_critic_loss.backward()
            self.constraint_optimizer.step()
        
        # 清空经验缓冲
        self.memory.clear()
    
    def get_state(self):
        """获取模型状态"""
        return {
            'actor': self.actor.state_dict(),
            'critic': self.critic.state_dict(),
            'constraint_critic': self.constraint_critic.state_dict(),
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic_optimizer': self.critic_optimizer.state_dict(),
            'constraint_optimizer': self.constraint_optimizer.state_dict()
        }
    
    def load_state(self, state_dict):
        """加载模型状态"""
        self.actor.load_state_dict(state_dict['actor'])
        self.critic.load_state_dict(state_dict['critic'])
        self.constraint_critic.load_state_dict(state_dict['constraint_critic'])
        self.actor_optimizer.load_state_dict(state_dict['actor_optimizer'])
        self.critic_optimizer.load_state_dict(state_dict['critic_optimizer'])
        self.constraint_optimizer.load_state_dict(state_dict['constraint_optimizer'])

class PPOConstrained_Trainer(BaseTrainer):
    """PPO约束算法训练器 - 基于巡飞简化ver的环境"""
    
    def __init__(self):
        super().__init__("baseline_comparison", "ppo_constrained")
        
        # 设置随机种子
        np.random.seed(42)
        torch.manual_seed(42)
        
        # 训练配置
        self.config = {
            "random_episodes": 150,    # 随机场景探索
            "fixed_episodes": 100,     # 固定场景强化
            "total_episodes": 250,     # 总训练episodes
            "save_interval": 50,       # 检查点保存间隔
            "stage": 1,                # 训练阶段
            "visualization_interval": 10
        }
        
        # 获取巡飞简化ver的配置
        self.lm_config = get_loitering_munition_config()
        self.training_config = get_training_config()
        
        # PPO配置
        self.ppo_config = {
            'state_dim': 15,  # 巡飞弹状态维度
            'action_dim': 3,  # [a_T, a_N, μ]
            'hidden_sizes': [256, 256],
            'learning_rate': 3e-4,
            'gamma': 0.99,
            'eps_clip': 0.2,
            'k_epochs': 4,
            'constraint_penalty': 10.0
        }
        
        # 性能统计
        self.training_results = {
            "start_time": datetime.now().isoformat(),
            "config": self.config,
            "stage_results": {}
        }
        
        self.logger.info("🎯 PPO约束算法训练器初始化完成")
        self.logger.info(f"📋 训练配置: {self.config}")
        self.logger.info("✅ 核心技术组件:")
        self.logger.info("  • LoiteringMunitionEnvironment: 真实的巡飞弹环境")
        self.logger.info("  • ConstrainedPPOController: PPO+约束优化")
        self.logger.info("⚠️  注意: 使用约束优化减少违反，但可能收敛较慢")
