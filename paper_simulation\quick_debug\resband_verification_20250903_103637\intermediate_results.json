{"convergence_analysis": {"resband_adaptive": {"episodes": [0, 1, 2], "rewards": [0, 1445.0259592730356, 1.1289336160198626], "success_rates": []}}, "performance_comparison": {"resband_adaptive": {"final_performance": {"avg_reward": 4330.155722427207, "success_rate": 0.0, "avg_steps": 1001.0, "constraint_violations": 0}, "learning_efficiency": {"convergence_episode": 3, "learning_rate": 0}}}, "efficiency_analysis": {"resband_adaptive": {"computation_times": [0.0, 4120.553731918335, 3.462553024291992], "memory_usage": [], "avg_computation_time": 1374.6720949808757}}, "resolution_selection": {"resband_adaptive": {"selections": [], "performance_by_resolution": {}}}, "statistical_tests": {}}