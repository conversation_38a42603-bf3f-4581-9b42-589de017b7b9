#!/usr/bin/env python3
"""
测试ResBand分辨率选择跟踪
验证分辨率选择数据是否正确记录
"""

import numpy as np

def test_resband_selection_tracking():
    """测试ResBand分辨率选择跟踪"""
    print("🔧 测试ResBand分辨率选择跟踪")
    print("=" * 50)
    
    try:
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        from environments.paper_environment import PaperSimulationEnvironment
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment('stage3_dynamic')
        config = {
            'state_dim': 6,
            'action_dim': 3,
            'dwa_enabled': True,
            'resband_enabled': True,
            'mlacf_enabled': True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = 'resband_adaptive'
        
        print(f"✅ 环境和智能体创建成功")
        print(f"ResBand启用: {hasattr(agent, 'resband')}")
        
        if hasattr(agent, 'resband'):
            print(f"ResBand类型: {type(agent.resband)}")
            print(f"初始臂: {getattr(agent.resband, 'current_arm', 'N/A')}")
        
        # 模拟一个简短的episode，记录分辨率选择
        state = env.reset()
        selections = []
        
        print(f"\n📊 测试10步的分辨率选择:")
        print("步骤 | 选择的臂 | 分辨率配置")
        print("-" * 40)
        
        for step in range(10):
            # 执行一步
            step_info = agent.train_step(state, training_progress=step/100)
            
            # 检查分辨率选择
            if hasattr(agent, 'resband') and hasattr(agent.resband, 'last_selected_arm'):
                selected_arm = agent.resband.last_selected_arm
                selections.append(selected_arm)
                
                # 获取分辨率配置
                if hasattr(agent, 'current_resolution_config'):
                    config_name = agent.current_resolution_config.get('name', 'Unknown')
                else:
                    config_name = 'N/A'
                
                print(f"{step+1:4d} | {selected_arm:8d} | {config_name}")
            else:
                print(f"{step+1:4d} | {'N/A':8s} | N/A")
                selections.append(-1)  # 标记为无效
            
            state = step_info['next_state']
            
            if step_info['done']:
                print(f"     Episode在第{step+1}步结束")
                break
        
        # 分析选择结果
        print(f"\n📈 分辨率选择分析:")
        print(f"总步数: {len(selections)}")
        
        if selections and max(selections) >= 0:
            from collections import Counter
            selection_counts = Counter([s for s in selections if s >= 0])
            
            print(f"选择分布:")
            for arm, count in selection_counts.items():
                arm_name = {0: '粗分辨率', 1: '中等分辨率', 2: '精细分辨率'}.get(arm, f'臂{arm}')
                print(f"  {arm_name}: {count}次")
            
            print(f"✅ 分辨率选择跟踪正常")
            return True
        else:
            print(f"❌ 分辨率选择跟踪失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_episode_selection_recording():
    """测试episode级别的分辨率选择记录"""
    print(f"\n🔧 测试Episode级别的分辨率选择记录")
    print("=" * 50)
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建实验实例
        experiment = ResBandVerificationExperiment()
        
        # 创建环境和智能体
        from environments.paper_environment import PaperSimulationEnvironment
        env = PaperSimulationEnvironment('stage1_simple')
        agent = experiment._create_agent('resband_adaptive', env)
        
        print(f"✅ 实验环境创建成功")
        
        # 测试单个episode的记录
        print(f"\n📊 测试单个Episode的分辨率选择记录:")
        
        episode_info = experiment._train_episode(agent, env, 0, 'resband_adaptive')
        
        print(f"Episode结果: {episode_info}")
        
        if 'resolution_selection' in episode_info:
            print(f"✅ 分辨率选择已记录: {episode_info['resolution_selection']}")
            return True
        else:
            print(f"❌ 分辨率选择未记录")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 ResBand分辨率选择跟踪诊断")
    
    # 测试1: 基础分辨率选择跟踪
    success1 = test_resband_selection_tracking()
    
    # 测试2: Episode级别记录
    success2 = test_episode_selection_recording()
    
    print(f"\n" + "="*60)
    print(f"🎉 诊断结果:")
    print(f"   基础分辨率选择跟踪: {'✅ 正常' if success1 else '❌ 失败'}")
    print(f"   Episode级别记录: {'✅ 正常' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print(f"   🎯 ResBand分辨率选择跟踪完全正常")
        print(f"   💡 建议: 重新运行实验，应该能正确生成分辨率选择可视化")
    else:
        print(f"   ⚠️ 需要修复分辨率选择跟踪问题")

if __name__ == "__main__":
    main()
