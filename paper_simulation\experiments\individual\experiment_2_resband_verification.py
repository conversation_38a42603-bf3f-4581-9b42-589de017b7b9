"""
实验2：ResBand算法验证
Experiment 2: ResBand Algorithm Verification

验证ResBand分辨率自适应选择算法的有效性
对比固定分辨率、启发式调度等方法
评估收敛速度、最终性能、计算效率
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pandas as pd
import time

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from environments.paper_environment import PaperSimulationEnvironment
from algorithms.simplified_resband import EnhancedResBand
from algorithms.lightweight_dwa_rl import LightweightDWARL

class ResBandVerificationExperiment:
    """ResBand算法验证实验"""
    
    def __init__(self, output_dir="results/experiment_2_resband"):
        """
        初始化实验
        
        Args:
            output_dir: 输出目录
        """
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"resband_verification_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 实验配置 - 完全匹配巡飞简化ver的设置
        self.config = {
            "training_episodes": 200,  # 每个阶段200episodes
            "evaluation_episodes": 50,
            "max_steps_per_episode": 2000,  # 每个episode最大2000步
            "test_scenarios": ["stage1_simple", "stage2_complex", "stage3_dynamic"],
            "progressive_training": True,  # 启用渐进式训练
            "track_resolution_selection": True,  # 跟踪分辨率选择
            "episodes_per_stage": 200,     # 每个阶段200个episodes
            "comparison_methods": [
                "resband_adaptive",      # ResBand自适应分辨率
                "fixed_coarse",          # 固定粗分辨率（三个阶段都用粗分辨率）
                "fixed_fine",            # 固定细分辨率（三个阶段都用细分辨率）
                "heuristic_schedule"     # 启发式分辨率调度
            ],
            # 巡飞弹物理参数 - 匹配巡飞简化ver
            "bounds": [2000, 2000, 2000],  # 环境边界
            "start_pos": [200.0, 200.0, 200.0],  # 起点（左下角）
            "goal_pos": [1800.0, 1800.0, 1800.0],  # 终点（右上角）
            "dt": 0.1,  # 时间步长
            "V_cruise": 25.0,  # 巡航速度
            "a_T_max": 8.0,   # 最大切向加速度
            "a_N_max": 39.24, # 最大法向加速度（4g）
            "gamma_max": 1.047  # 最大航迹倾斜角（60°）
        }
        
        # 中止后重启支持
        self.checkpoint_file = os.path.join(self.output_dir, "experiment_checkpoint.json")
        self.completed_methods = self._load_checkpoint()

        # 结果存储
        self.results = {
            "convergence_analysis": {},    # 收敛速度分析
            "performance_comparison": {},  # 性能对比
            "efficiency_analysis": {},     # 计算效率分析
            "resolution_selection": {},    # 分辨率选择分析
            "statistical_tests": {}       # 统计检验
        }
        
        print(f"🎰 ResBand算法验证实验初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📋 实验配置（匹配巡飞简化ver）:")
        print(f"   - 起点: {self.config['start_pos']}")
        print(f"   - 终点: {self.config['goal_pos']}")
        print(f"   - 训练episodes: {self.config['training_episodes']}")
        print(f"   - 每阶段episodes: {self.config['episodes_per_stage']}")
        print(f"   - 最大步数: {self.config['max_steps_per_episode']}")
        print(f"   - 对比方法: {self.config['comparison_methods']}")
        print(f"   - 预计时间: 约2-3小时（4种方法 × 3阶段 × 200episodes）")

        if self.completed_methods:
            print(f"🔄 检测到之前的进度，已完成方法: {self.completed_methods}")

    def _load_checkpoint(self):
        """加载检查点"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r') as f:
                    checkpoint = json.load(f)
                return checkpoint.get("completed_methods", [])
            except Exception as e:
                print(f"⚠️ 加载检查点失败: {e}")
                return []
        return []

    def _save_checkpoint(self, completed_method):
        """保存检查点"""
        if completed_method not in self.completed_methods:
            self.completed_methods.append(completed_method)

        checkpoint = {
            "completed_methods": self.completed_methods,
            "timestamp": datetime.now().isoformat(),
            "config": self.config
        }

        try:
            with open(self.checkpoint_file, 'w') as f:
                json.dump(checkpoint, f, indent=2)
            print(f"💾 保存检查点: {completed_method}")
        except Exception as e:
            print(f"⚠️ 保存检查点失败: {e}")

    def _save_intermediate_results(self):
        """保存中间结果"""
        intermediate_file = os.path.join(self.output_dir, "intermediate_results.json")
        try:
            with open(intermediate_file, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            print(f"💾 保存中间结果")
        except Exception as e:
            print(f"⚠️ 保存中间结果失败: {e}")

    def run_experiment(self):
        """运行完整的ResBand验证实验"""
        print("\n🚀 开始ResBand算法验证实验")
        print("=" * 60)
        print("⚠️ 注意：此实验将进行真实训练，预计需要2-3小时")

        # 确认是否继续（支持非交互模式）
        if len(sys.argv) > 1 and sys.argv[1] == '--auto':
            print("🤖 自动模式：跳过用户确认")
        else:
            response = input("是否继续进行真实训练实验？(y/N): ")
            if response.lower() != 'y':
                print("❌ 用户取消实验")
                return None

        # 对每种方法进行训练和测试（支持中止后重启）
        for method in self.config["comparison_methods"]:
            if method in self.completed_methods:
                print(f"\n✅ 跳过已完成的方法: {method}")
                continue

            print(f"\n🔬 训练和测试方法: {method}")

            try:
                method_results = self._test_method_performance(method)

                self.results["convergence_analysis"][method] = method_results["convergence"]
                self.results["performance_comparison"][method] = method_results["performance"]
                self.results["efficiency_analysis"][method] = method_results["efficiency"]

                if method == "resband_adaptive":
                    self.results["resolution_selection"][method] = method_results["resolution_data"]

                # 保存检查点
                self._save_checkpoint(method)

                # 保存中间结果
                self._save_intermediate_results()

            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断实验，已保存当前进度")
                print(f"📋 已完成方法: {self.completed_methods}")
                print(f"🔄 重新运行脚本可从中断处继续")
                return
            except Exception as e:
                print(f"\n❌ 方法 {method} 测试失败: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        # 生成对比分析
        self._generate_comparison_analysis()
        
        # 生成图表
        self._generate_convergence_plots()
        self._generate_performance_comparison()
        self._generate_resolution_selection_plots()  # 修复方法名
        self._generate_efficiency_comparison()
        
        # 保存结果
        self._save_results()
        
        print(f"\n✅ ResBand算法验证实验完成")
        print(f"📊 结果保存至: {self.output_dir}")
        
        return self.results
    
    def _test_method_performance(self, method):
        """测试单个方法的性能 - 支持渐进式训练"""
        method_results = {
            "convergence": {"episodes": [], "rewards": [], "success_rates": []},
            "performance": {"final_performance": {}, "learning_efficiency": {}},
            "efficiency": {"computation_times": [], "memory_usage": []},
            "resolution_data": {"selections": [], "performance_by_resolution": {}},
            "stage_performance": {}  # 各阶段性能
        }

        if self.config["progressive_training"]:
            # 渐进式训练：在不同场景中训练
            return self._progressive_training(method, method_results)
        else:
            # 单场景训练（原有方式）
            env = PaperSimulationEnvironment("stage2_complex")
            agent = self._create_agent(method, env)
            return self._single_stage_training(method, agent, env, method_results)

    def _progressive_training(self, method, method_results):
        """渐进式训练：简单→复杂→动态场景"""
        print(f"     🏋️ 渐进式训练 ({len(self.config['test_scenarios'])}个阶段)")

        all_training_rewards = []
        all_training_success_rates = []
        all_computation_times = []
        all_resband_selections = []

        # 创建智能体（在第一个场景中创建，后续复用）
        first_env = PaperSimulationEnvironment(self.config["test_scenarios"][0])
        agent = self._create_agent(method, first_env)

        total_episodes = 0

        # 渐进式训练各阶段
        for stage_idx, scenario in enumerate(self.config["test_scenarios"]):
            print(f"       阶段 {stage_idx + 1}: {scenario}")

            # 创建该阶段的环境
            env = PaperSimulationEnvironment(scenario)

            # 重置环境确保状态正确
            env.reset()

            # 该阶段的训练episodes
            stage_episodes = self.config["episodes_per_stage"]

            stage_rewards = []
            stage_success_rates = []
            stage_computation_times = []
            stage_resband_selections = []

            for episode in range(stage_episodes):
                start_time = time.time()

                print(f"         开始Episode {episode + 1}/{stage_episodes}...")

                # 训练一个episode
                episode_info = self._train_episode(agent, env, total_episodes + episode, method)

                end_time = time.time()
                computation_time = (end_time - start_time) * 1000  # 毫秒

                stage_rewards.append(episode_info["reward"])
                stage_computation_times.append(computation_time)

                print(f"         Episode {episode + 1} 完成: 奖励={episode_info['reward']:.1f}, "
                      f"步数={episode_info['steps']}, 时间={computation_time:.1f}ms")

                # 记录ResBand的分辨率选择
                if method == "resband_adaptive" and "resolution_selection" in episode_info:
                    stage_resband_selections.append(episode_info["resolution_selection"])

                # 定期评估
                if (episode + 1) % 25 == 0:
                    eval_success_rate = self._evaluate_success_rate(agent, env)
                    stage_success_rates.append(eval_success_rate)

                    print(f"         Episode {episode + 1}/{stage_episodes}: "
                          f"奖励={episode_info['reward']:.1f}, 成功率={eval_success_rate:.2%}")

            # 记录阶段结果
            method_results["stage_performance"][scenario] = {
                "avg_reward": np.mean(stage_rewards),
                "final_success_rate": stage_success_rates[-1] if stage_success_rates else 0,
                "avg_computation_time": np.mean(stage_computation_times)
            }

            # 累积到总结果
            all_training_rewards.extend(stage_rewards)
            all_training_success_rates.extend(stage_success_rates)
            all_computation_times.extend(stage_computation_times)
            all_resband_selections.extend(stage_resband_selections)

            total_episodes += stage_episodes

            print(f"       ✅ 阶段 {stage_idx + 1} 完成: 平均奖励={np.mean(stage_rewards):.1f}")

        # 记录总体收敛数据
        method_results["convergence"]["episodes"] = list(range(len(all_training_rewards)))
        method_results["convergence"]["rewards"] = all_training_rewards
        method_results["convergence"]["success_rates"] = all_training_success_rates

        # 记录效率数据
        method_results["efficiency"]["computation_times"] = all_computation_times
        method_results["efficiency"]["avg_computation_time"] = np.mean(all_computation_times)

        # 记录ResBand选择数据（同时保存到convergence和resolution_data中）
        if all_resband_selections:
            method_results["resolution_data"]["selections"] = all_resband_selections
            method_results["convergence"]["resolution_selections"] = all_resband_selections

        # 最终性能评估（在最复杂的场景中）
        final_env = PaperSimulationEnvironment(self.config["test_scenarios"][-1])
        print(f"     📊 最终性能评估 (在{self.config['test_scenarios'][-1]}中)")
        final_performance = self._evaluate_final_performance(agent, final_env)
        method_results["performance"]["final_performance"] = final_performance

        # 学习效率分析
        method_results["performance"]["learning_efficiency"] = self._analyze_learning_efficiency(all_training_rewards)

        return method_results

    def _single_stage_training(self, method, agent, env, method_results):
        """单阶段训练（兼容性方法）"""
        print(f"     🏋️ 单阶段训练 ({self.config['training_episodes']} episodes)")

        training_rewards = []
        training_success_rates = []
        computation_times = []
        resband_selections = []

        for episode in range(self.config["training_episodes"]):
            start_time = time.time()

            # 训练一个episode
            episode_info = self._train_episode(agent, env, episode, method)

            end_time = time.time()
            computation_times.append((end_time - start_time) * 1000)  # 毫秒

            training_rewards.append(episode_info["reward"])

            # 记录ResBand的分辨率选择
            if method == "resband_adaptive" and "resolution_selection" in episode_info:
                resband_selections.append(episode_info["resolution_selection"])

            # 定期评估
            if (episode + 1) % 25 == 0:
                eval_success_rate = self._evaluate_success_rate(agent, env)
                training_success_rates.append(eval_success_rate)

                print(f"       Episode {episode + 1}: 奖励={episode_info['reward']:.1f}, "
                      f"成功率={eval_success_rate:.2%}")

        # 记录结果
        method_results["convergence"]["episodes"] = list(range(len(training_rewards)))
        method_results["convergence"]["rewards"] = training_rewards
        method_results["convergence"]["success_rates"] = training_success_rates
        method_results["efficiency"]["computation_times"] = computation_times
        method_results["efficiency"]["avg_computation_time"] = np.mean(computation_times)

        # 记录ResBand选择数据（同时保存到convergence和resolution_data中）
        if resband_selections:
            method_results["resolution_data"]["selections"] = resband_selections
            method_results["convergence"]["resolution_selections"] = resband_selections

        # 最终性能评估
        final_performance = self._evaluate_final_performance(agent, env)
        method_results["performance"]["final_performance"] = final_performance
        method_results["performance"]["learning_efficiency"] = self._analyze_learning_efficiency(training_rewards)

        return method_results

    def _create_agent(self, method, env):
        """创建对应方法的智能体"""
        if method == "resband_adaptive":
            # ResBand自适应分辨率
            config = {
                "state_dim": 6,
                "action_dim": 3,
                "dwa_enabled": True,
                "resband_enabled": True,
                "mlacf_enabled": True
            }
            agent = LightweightDWARL(env, config)
            agent._method_type = method
            return agent
        
        elif method == "fixed_coarse":
            # 固定粗分辨率
            config = {
                "state_dim": 6,
                "action_dim": 3,
                "dwa_enabled": True,
                "resband_enabled": False,
                "fixed_resolution": {"delta_a_T": 8.0, "delta_a_N": 25.0, "delta_mu": 1.0, "name": "粗分辨率"}
            }
            agent = LightweightDWARL(env, config)
            agent._method_type = method
            return agent

        elif method == "fixed_medium":
            # 固定中等分辨率
            config = {
                "state_dim": 6,
                "action_dim": 3,
                "dwa_enabled": True,
                "resband_enabled": False,
                "fixed_resolution": {"delta_a_T": 4.0, "delta_a_N": 15.0, "delta_mu": 0.5, "name": "中等分辨率"}
            }
            agent = LightweightDWARL(env, config)
            agent._method_type = method
            return agent

        elif method == "fixed_fine":
            # 固定精细分辨率
            config = {
                "state_dim": 6,
                "action_dim": 3,
                "dwa_enabled": True,
                "resband_enabled": False,
                "fixed_resolution": {"delta_a_T": 2.0, "delta_a_N": 8.0, "delta_mu": 0.3, "name": "精细分辨率"}
            }
            agent = LightweightDWARL(env, config)
            agent._method_type = method
            return agent

        elif method == "heuristic_schedule":
            # 启发式调度（简化实现）
            config = {
                "state_dim": 6,
                "action_dim": 3,
                "dwa_enabled": True,
                "resband_enabled": False,
                "heuristic_schedule": True
            }
            agent = LightweightDWARL(env, config)
            agent._method_type = method
            return agent
        
        else:
            raise ValueError(f"未知方法: {method}")
    
    def _train_episode(self, agent, env, episode, method):
        """训练一个episode"""
        # 确保环境正确重置
        state = env.reset()
        total_reward = 0
        steps = 0
        done = False
        resolution_selections = []  # 记录每步的分辨率选择

        print(f"           开始训练Episode {episode}, 初始状态: {state[:3]}")  # 只打印位置

        while not done and steps < self.config["max_steps_per_episode"]:
            try:
                if hasattr(agent, 'train_step'):
                    # DWA-RL框架
                    step_info = agent.train_step(state, training_progress=(episode + 1) / self.config["training_episodes"])
                    next_state = step_info["next_state"]
                    reward = step_info["reward"]
                    done = step_info["done"]

                    # 调试输出
                    if steps == 0:  # 只在第一步输出详细信息
                        print(f"             🔍 第1步详细信息:")
                        print(f"               动作: {step_info.get('action', 'N/A')}")
                        print(f"               新位置: {next_state[:3]}")
                        print(f"               奖励: {reward:.3f}")
                        print(f"               完成: {done}")
                        print(f"               信息: {step_info.get('info', {})}")

                    # 记录ResBand的分辨率选择
                    if method == "resband_adaptive" and hasattr(agent, 'resband'):
                        if hasattr(agent.resband, 'last_selected_arm'):
                            resolution_selections.append(agent.resband.last_selected_arm)

                else:
                    # 其他方法
                    action = agent.select_action(state, add_noise=True)
                    next_state, reward, done, info = env.step(action)
                    agent.store_transition(state, action, reward, next_state, done)
                    if agent.can_update():
                        agent.update()

                state = next_state
                total_reward += reward
                steps += 1

                # 每10步打印一次进度（仅在调试模式）
                if steps % 10 == 0 and steps > 0:
                    print(f"             步骤 {steps}: 累积奖励={total_reward:.1f}")

            except Exception as e:
                print(f"⚠️ Episode {episode} 步骤 {steps} 出错: {e}")
                # 重置环境并继续
                state = env.reset()
                break

        print(f"           Episode {episode} 结束: 总奖励={total_reward:.1f}, 总步数={steps}, 完成={done}")
        episode_info = {"reward": total_reward, "steps": steps}

        # 添加分辨率选择信息
        if resolution_selections:
            # 使用最常选择的分辨率作为该episode的代表
            from collections import Counter
            most_common = Counter(resolution_selections).most_common(1)
            if most_common:
                episode_info["resolution_selection"] = most_common[0][0]

        return episode_info
    
    def _evaluate_success_rate(self, agent, env, num_episodes=10):
        """评估成功率"""
        successes = 0
        
        for _ in range(num_episodes):
            state = env.reset()
            done = False
            steps = 0
            
            while not done and steps < self.config["max_steps_per_episode"]:
                if hasattr(agent, 'evaluate_step'):
                    step_info = agent.evaluate_step(state)
                    next_state = step_info["next_state"]
                    done = step_info["done"]
                    info = step_info.get("info", {})
                else:
                    action = agent.select_action(state, add_noise=False)
                    next_state, reward, done, info = env.step(action)
                
                state = next_state
                steps += 1
            
            if info.get("success", False):
                successes += 1
        
        return successes / num_episodes
    
    def _evaluate_final_performance(self, agent, env):
        """评估最终性能"""
        performance = {
            "avg_reward": 0,
            "success_rate": 0,
            "avg_steps": 0,
            "constraint_violations": 0
        }
        
        total_reward = 0
        total_steps = 0
        total_violations = 0
        successes = 0
        
        for episode in range(self.config["evaluation_episodes"]):
            state = env.reset()
            episode_reward = 0
            episode_steps = 0
            episode_violations = 0
            done = False
            
            while not done and episode_steps < self.config["max_steps_per_episode"]:
                if hasattr(agent, 'evaluate_step'):
                    step_info = agent.evaluate_step(state)
                    next_state = step_info["next_state"]
                    reward = step_info["reward"]
                    done = step_info["done"]
                    info = step_info.get("info", {})
                else:
                    action = agent.select_action(state, add_noise=False)
                    next_state, reward, done, info = env.step(action)
                
                if info.get("collision", False) or info.get("boundary_violation", False):
                    episode_violations += 1
                
                state = next_state
                episode_reward += reward
                episode_steps += 1
            
            if info.get("success", False):
                successes += 1
            
            total_reward += episode_reward
            total_steps += episode_steps
            total_violations += episode_violations
        
        performance["avg_reward"] = total_reward / self.config["evaluation_episodes"]
        performance["success_rate"] = successes / self.config["evaluation_episodes"]
        performance["avg_steps"] = total_steps / self.config["evaluation_episodes"]
        performance["constraint_violations"] = total_violations
        
        return performance
    
    def _analyze_learning_efficiency(self, training_rewards):
        """分析学习效率"""
        if len(training_rewards) < 50:
            return {"convergence_episode": len(training_rewards), "learning_rate": 0}
        
        # 计算收敛episode（奖励稳定的开始点）
        window_size = 20
        convergence_episode = len(training_rewards)
        
        for i in range(window_size, len(training_rewards) - window_size):
            window_rewards = training_rewards[i:i+window_size]
            if np.std(window_rewards) < np.std(training_rewards) * 0.1:  # 方差小于总体方差的10%
                convergence_episode = i
                break
        
        # 计算学习率（前50%episode的平均改善）
        mid_point = len(training_rewards) // 2
        early_avg = np.mean(training_rewards[:mid_point])
        later_avg = np.mean(training_rewards[mid_point:])
        learning_rate = (later_avg - early_avg) / early_avg if early_avg != 0 else 0
        
        return {
            "convergence_episode": convergence_episode,
            "learning_rate": learning_rate,
            "final_reward": np.mean(training_rewards[-20:]) if len(training_rewards) >= 20 else np.mean(training_rewards)
        }
    
    def _generate_comparison_analysis(self):
        """生成对比分析"""
        analysis = {
            "performance_ranking": [],
            "convergence_ranking": [],
            "efficiency_ranking": [],
            "resband_advantages": {}
        }
        
        # 性能排名
        performance_scores = {}
        for method in self.config["comparison_methods"]:
            if method in self.results["performance_comparison"]:
                final_perf = self.results["performance_comparison"][method]["final_performance"]
                # 综合评分：成功率(40%) + 平均奖励(40%) + 约束违反惩罚(20%)
                score = (final_perf["success_rate"] * 0.4 + 
                        (final_perf["avg_reward"] / 1000) * 0.4 - 
                        (final_perf["constraint_violations"] / 100) * 0.2)
                performance_scores[method] = score
        
        analysis["performance_ranking"] = sorted(performance_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 收敛速度排名
        convergence_scores = {}
        for method in self.config["comparison_methods"]:
            if method in self.results["performance_comparison"]:
                learning_eff = self.results["performance_comparison"][method]["learning_efficiency"]
                # 收敛越快越好，学习率越高越好
                score = (1.0 / (learning_eff["convergence_episode"] + 1)) + learning_eff["learning_rate"]
                convergence_scores[method] = score
        
        analysis["convergence_ranking"] = sorted(convergence_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 计算ResBand的优势
        if "resband_adaptive" in performance_scores:
            resband_score = performance_scores["resband_adaptive"]
            advantages = {}
            
            for method, score in performance_scores.items():
                if method != "resband_adaptive":
                    advantage = ((resband_score - score) / score * 100) if score > 0 else 0
                    advantages[method] = f"{advantage:.1f}%"
            
            analysis["resband_advantages"] = advantages
        
        self.results["statistical_tests"] = analysis
    
    def _generate_convergence_plots(self):
        """生成收敛曲线图"""
        plt.figure(figsize=(15, 10))
        
        # 奖励收敛曲线
        plt.subplot(2, 2, 1)
        for method in self.config["comparison_methods"]:
            if method in self.results["convergence_analysis"]:
                convergence_data = self.results["convergence_analysis"][method]
                episodes = convergence_data["episodes"]
                rewards = convergence_data["rewards"]
                
                # 平滑处理
                if len(rewards) > 20:
                    smoothed_rewards = np.convolve(rewards, np.ones(20)/20, mode='valid')
                    smoothed_episodes = episodes[19:]
                    plt.plot(smoothed_episodes, smoothed_rewards, label=method, linewidth=2)
                else:
                    plt.plot(episodes, rewards, label=method, linewidth=2)
        
        plt.xlabel('训练Episode')
        plt.ylabel('平均奖励')
        plt.title('收敛速度对比：奖励曲线')
        plt.legend()
        plt.grid(True)
        
        # 成功率收敛曲线
        plt.subplot(2, 2, 2)
        for method in self.config["comparison_methods"]:
            if method in self.results["convergence_analysis"]:
                convergence_data = self.results["convergence_analysis"][method]
                success_rates = convergence_data["success_rates"]
                
                if success_rates:
                    eval_episodes = list(range(25, len(success_rates) * 25 + 1, 25))
                    plt.plot(eval_episodes, success_rates, label=method, linewidth=2, marker='o')
        
        plt.xlabel('训练Episode')
        plt.ylabel('成功率')
        plt.title('收敛速度对比：成功率曲线')
        plt.legend()
        plt.grid(True)
        
        # 最终性能对比
        plt.subplot(2, 2, 3)
        methods = []
        final_rewards = []
        success_rates = []
        
        for method in self.config["comparison_methods"]:
            if method in self.results["performance_comparison"]:
                final_perf = self.results["performance_comparison"][method]["final_performance"]
                methods.append(method)
                final_rewards.append(final_perf["avg_reward"])
                success_rates.append(final_perf["success_rate"] * 100)  # 转换为百分比
        
        x = np.arange(len(methods))
        width = 0.35
        
        plt.bar(x - width/2, final_rewards, width, label='平均奖励', alpha=0.8)
        plt.bar(x + width/2, success_rates, width, label='成功率(%)', alpha=0.8)
        
        plt.xlabel('方法')
        plt.ylabel('性能指标')
        plt.title('最终性能对比')
        plt.xticks(x, methods, rotation=45)
        plt.legend()
        
        # 计算效率对比
        plt.subplot(2, 2, 4)
        methods = []
        avg_times = []
        
        for method in self.config["comparison_methods"]:
            if method in self.results["efficiency_analysis"]:
                efficiency_data = self.results["efficiency_analysis"][method]
                methods.append(method)
                avg_times.append(efficiency_data["avg_computation_time"])
        
        plt.bar(methods, avg_times, alpha=0.8)
        plt.xlabel('方法')
        plt.ylabel('平均计算时间 (ms)')
        plt.title('计算效率对比')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "resband_convergence_analysis.png"), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📈 收敛分析图已生成")

    def _generate_resolution_selection_plots(self):
        """生成ResBand分辨率选择可视化图"""
        if "resband_adaptive" not in self.results["convergence_analysis"]:
            print("⚠️ 没有ResBand数据，跳过分辨率选择可视化")
            return

        resband_data = self.results["convergence_analysis"]["resband_adaptive"]
        if "resolution_selections" not in resband_data:
            print("⚠️ 没有分辨率选择数据，跳过可视化")
            return

        resolution_selections = resband_data["resolution_selections"]

        # 创建分辨率选择可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 1. 分辨率选择时间序列
        ax1 = axes[0, 0]
        episodes = list(range(1, len(resolution_selections) + 1))

        # 为不同分辨率设置不同颜色
        colors = {0: 'red', 1: 'blue', 2: 'green'}
        labels = {0: '粗分辨率 (Arm 0)', 1: '中等分辨率 (Arm 1)', 2: '精细分辨率 (Arm 2)'}

        # 绘制分辨率选择序列
        for i, selection in enumerate(resolution_selections):
            ax1.scatter(i+1, selection, c=colors[selection], s=20, alpha=0.7)

        ax1.set_xlabel('Episode')
        ax1.set_ylabel('选择的分辨率臂')
        ax1.set_title('ResBand分辨率选择时间序列')
        ax1.set_yticks([0, 1, 2])
        ax1.set_yticklabels(['粗分辨率', '中等分辨率', '精细分辨率'])
        ax1.grid(True, alpha=0.3)

        # 添加图例
        for arm, color in colors.items():
            ax1.scatter([], [], c=color, label=labels[arm])
        ax1.legend()

        # 2. 分辨率选择分布饼图
        ax2 = axes[0, 1]
        from collections import Counter
        selection_counts = Counter(resolution_selections)

        arms = list(selection_counts.keys())
        counts = list(selection_counts.values())
        arm_labels = [labels[arm] for arm in arms]
        arm_colors = [colors[arm] for arm in arms]

        wedges, texts, autotexts = ax2.pie(counts, labels=arm_labels, colors=arm_colors,
                                          autopct='%1.1f%%', startangle=90)
        ax2.set_title('分辨率选择分布')

        # 3. 分辨率选择vs性能关系
        ax3 = axes[1, 0]
        if "rewards" in resband_data and len(resband_data["rewards"]) == len(resolution_selections):
            rewards = resband_data["rewards"]

            # 按分辨率分组计算平均奖励
            arm_rewards = {0: [], 1: [], 2: []}
            for selection, reward in zip(resolution_selections, rewards):
                arm_rewards[selection].append(reward)

            arm_avg_rewards = {}
            for arm in [0, 1, 2]:
                if arm_rewards[arm]:
                    arm_avg_rewards[arm] = np.mean(arm_rewards[arm])
                else:
                    arm_avg_rewards[arm] = 0

            arms = list(arm_avg_rewards.keys())
            avg_rewards = list(arm_avg_rewards.values())
            arm_labels = [labels[arm] for arm in arms]
            arm_colors = [colors[arm] for arm in arms]

            bars = ax3.bar(arm_labels, avg_rewards, color=arm_colors, alpha=0.7)
            ax3.set_xlabel('分辨率类型')
            ax3.set_ylabel('平均奖励')
            ax3.set_title('不同分辨率的平均性能')

            # 添加数值标签
            for bar, reward in zip(bars, avg_rewards):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height,
                        f'{reward:.1f}', ha='center', va='bottom')

        # 4. 分辨率选择趋势（滑动窗口）
        ax4 = axes[1, 1]
        window_size = 20
        if len(resolution_selections) >= window_size:
            # 计算滑动窗口内各分辨率的比例
            window_episodes = []
            coarse_ratios = []
            medium_ratios = []
            fine_ratios = []

            for i in range(window_size, len(resolution_selections) + 1):
                window_data = resolution_selections[i-window_size:i]
                window_counts = Counter(window_data)
                total = len(window_data)

                window_episodes.append(i)
                coarse_ratios.append(window_counts.get(0, 0) / total)
                medium_ratios.append(window_counts.get(1, 0) / total)
                fine_ratios.append(window_counts.get(2, 0) / total)

            ax4.plot(window_episodes, coarse_ratios, color='red', label='粗分辨率', linewidth=2)
            ax4.plot(window_episodes, medium_ratios, color='blue', label='中等分辨率', linewidth=2)
            ax4.plot(window_episodes, fine_ratios, color='green', label='精细分辨率', linewidth=2)

            ax4.set_xlabel('Episode')
            ax4.set_ylabel('选择比例')
            ax4.set_title(f'分辨率选择趋势 (滑动窗口={window_size})')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, f'数据不足\n(需要至少{window_size}个episodes)',
                    ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('分辨率选择趋势')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "resband_resolution_selection.png"),
                   dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 ResBand分辨率选择可视化已生成")

        # 生成分辨率选择统计报告
        self._generate_resolution_statistics(resolution_selections)

    def _generate_resolution_statistics(self, resolution_selections):
        """生成分辨率选择统计报告"""
        from collections import Counter

        selection_counts = Counter(resolution_selections)
        total_episodes = len(resolution_selections)

        stats = {
            "total_episodes": total_episodes,
            "selection_counts": dict(selection_counts),
            "selection_ratios": {arm: count/total_episodes for arm, count in selection_counts.items()},
            "most_selected": selection_counts.most_common(1)[0] if selection_counts else None,
            "diversity_score": len(selection_counts) / 3.0  # 多样性分数 (0-1)
        }

        # 保存统计数据
        stats_file = os.path.join(self.output_dir, "resolution_selection_stats.json")
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

        print(f"📈 分辨率选择统计:")
        print(f"   总Episodes: {total_episodes}")
        print(f"   选择分布: {dict(selection_counts)}")
        for arm, ratio in stats["selection_ratios"].items():
            arm_name = {0: '粗分辨率', 1: '中等分辨率', 2: '精细分辨率'}[arm]
            print(f"   {arm_name}: {ratio:.1%}")

        if stats["most_selected"]:
            most_arm, most_count = stats["most_selected"]
            most_name = {0: '粗分辨率', 1: '中等分辨率', 2: '精细分辨率'}[most_arm]
            print(f"   最常选择: {most_name} ({most_count}次, {most_count/total_episodes:.1%})")

        print(f"   多样性分数: {stats['diversity_score']:.2f}")

    def _generate_performance_comparison(self):
        """生成性能对比表"""
        table_data = []
        
        for method in self.config["comparison_methods"]:
            if method in self.results["performance_comparison"]:
                final_perf = self.results["performance_comparison"][method]["final_performance"]
                learning_eff = self.results["performance_comparison"][method]["learning_efficiency"]
                efficiency = self.results["efficiency_analysis"][method]
                
                row = {
                    "方法": method,
                    "平均奖励": f"{final_perf['avg_reward']:.1f}",
                    "成功率": f"{final_perf['success_rate']:.2%}",
                    "约束违反": final_perf["constraint_violations"],
                    "收敛Episode": learning_eff["convergence_episode"],
                    "学习率": f"{learning_eff['learning_rate']:.3f}",
                    "计算时间(ms)": f"{efficiency['avg_computation_time']:.2f}"
                }
                table_data.append(row)
        
        # 保存为CSV
        df = pd.DataFrame(table_data)
        df.to_csv(os.path.join(self.output_dir, "resband_performance_comparison.csv"), index=False)
        
        print(f"📊 性能对比表已生成")
    
    def _generate_resolution_selection_analysis(self):
        """生成分辨率选择分析"""
        if "resband_adaptive" not in self.results["resolution_selection"]:
            return
        
        resolution_data = self.results["resolution_selection"]["resband_adaptive"]
        selections = resolution_data["selections"]
        
        if not selections:
            return
        
        # 分辨率选择统计
        plt.figure(figsize=(12, 8))
        
        # 选择历史
        plt.subplot(2, 2, 1)
        plt.plot(selections, linewidth=2)
        plt.xlabel('训练Episode')
        plt.ylabel('选择的分辨率臂')
        plt.title('ResBand分辨率选择历史')
        plt.grid(True)
        
        # 选择分布
        plt.subplot(2, 2, 2)
        unique, counts = np.unique(selections, return_counts=True)
        plt.bar(unique, counts, alpha=0.8)
        plt.xlabel('分辨率臂')
        plt.ylabel('选择次数')
        plt.title('分辨率选择分布')
        
        # 选择趋势（滑动窗口）
        plt.subplot(2, 2, 3)
        window_size = 20
        if len(selections) > window_size:
            trends = []
            for i in range(window_size, len(selections)):
                window_selections = selections[i-window_size:i]
                avg_selection = np.mean(window_selections)
                trends.append(avg_selection)
            
            plt.plot(range(window_size, len(selections)), trends, linewidth=2)
            plt.xlabel('训练Episode')
            plt.ylabel('平均分辨率选择')
            plt.title(f'分辨率选择趋势（窗口={window_size}）')
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "resband_selection_analysis.png"), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📈 分辨率选择分析图已生成")
    
    def _generate_efficiency_comparison(self):
        """生成效率对比分析"""
        # 这里可以添加更详细的效率分析
        pass
    
    def _save_results(self):
        """保存实验结果"""
        # 保存完整结果
        results_file = os.path.join(self.output_dir, "resband_verification_results.json")
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        # 生成实验报告
        self._generate_experiment_report()
        
        print(f"💾 实验结果已保存")
    
    def _generate_experiment_report(self):
        """生成实验报告"""
        report_file = os.path.join(self.output_dir, "experiment_2_report.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 实验2：ResBand算法验证报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 实验概述\n\n")
            f.write("本实验验证ResBand分辨率自适应选择算法的有效性，")
            f.write("通过与固定分辨率和启发式调度方法对比，")
            f.write("评估ResBand在收敛速度、最终性能和计算效率方面的优势。\n\n")
            
            f.write("## 主要发现\n\n")
            if "statistical_tests" in self.results:
                analysis = self.results["statistical_tests"]
                
                if "performance_ranking" in analysis:
                    f.write("### 性能排名\n")
                    for i, (method, score) in enumerate(analysis["performance_ranking"]):
                        f.write(f"{i+1}. {method}: {score:.3f}\n")
                    f.write("\n")
                
                if "resband_advantages" in analysis:
                    f.write("### ResBand优势\n")
                    for method, advantage in analysis["resband_advantages"].items():
                        f.write(f"- 相比{method}: 性能提升{advantage}\n")
                    f.write("\n")
            
            f.write("## 结论\n\n")
            f.write("实验结果表明，ResBand算法能够根据场景复杂度智能选择分辨率，")
            f.write("在保证性能的同时提高学习效率，")
            f.write("相比固定分辨率方法具有明显优势。\n")

def main():
    """主函数"""
    print("🎰 实验2：ResBand算法验证")
    
    # 创建实验
    experiment = ResBandVerificationExperiment()
    
    # 运行实验
    results = experiment.run_experiment()
    
    # 输出关键结果
    print(f"\n📊 实验结果摘要:")
    if "statistical_tests" in results:
        analysis = results["statistical_tests"]
        
        if "performance_ranking" in analysis:
            print("   性能排名:")
            for i, (method, score) in enumerate(analysis["performance_ranking"][:3]):
                print(f"     {i+1}. {method}: {score:.3f}")

if __name__ == "__main__":
    main()
