"""
只运行训练
RUN TRAINING ONLY

训练所有方法：本文方法 + 所有基线方法
Train All Methods: Our Method + All Baseline Methods

预计时间：约10小时
Estimated Time: ~10 hours
"""

import os
import sys
import subprocess
from datetime import datetime

class TrainingOnlyRunner:
    """只训练运行器"""
    
    def __init__(self):
        """初始化运行器"""
        self.training_methods = [
            ("train/01_train_our_method.py", "本文方法 (DWA-RL+ResBand+MLACF)", "3小时"),
            ("train/02_train_baseline_td3.py", "TD3基线 (纯强化学习)", "2小时"),
            ("train/03_train_baseline_dwa_td3.py", "DWA-TD3基线 (固定分辨率)", "2.5小时"),
            ("train/04_train_baseline_ppo.py", "PPO基线 (约束强化学习)", "2小时"),
            ("train/05_train_baseline_traditional.py", "传统DWA基线 (固定参数)", "30分钟")
        ]
        
        print("🏋️ 训练所有方法")
        print("=" * 60)
        print("📋 训练计划:")
        for i, (_, name, time) in enumerate(self.training_methods, 1):
            print(f"   {i}. {name} - {time}")
        print("=" * 60)
        print("⏱️ 预计总时间: 约10小时")
    
    def run_all_training(self):
        """运行所有训练"""
        print(f"\n🚀 开始训练所有方法")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 确认开始
        response = input(f"\n是否开始训练所有方法？预计10小时 (y/N): ")
        if response.lower() != 'y':
            print("❌ 用户取消训练")
            return False
        
        start_time = datetime.now()
        results = []
        
        # 依次训练所有方法
        for i, (script_path, method_name, estimated_time) in enumerate(self.training_methods, 1):
            print(f"\n{'='*60}")
            print(f"🏋️ 训练进度: {i}/{len(self.training_methods)}")
            print(f"📝 方法: {method_name}")
            print(f"⏱️ 预计时间: {estimated_time}")
            print(f"{'='*60}")
            
            method_start_time = datetime.now()
            
            success = self._run_training_script(script_path)
            
            method_end_time = datetime.now()
            method_duration = method_end_time - method_start_time
            
            result = {
                "method": method_name,
                "script": script_path,
                "success": success,
                "duration": str(method_duration),
                "estimated_time": estimated_time
            }
            results.append(result)
            
            if success:
                print(f"✅ {method_name} 训练完成")
                print(f"⏱️ 实际用时: {method_duration}")
            else:
                print(f"❌ {method_name} 训练失败")
                
                # 询问是否继续
                response = input(f"\n训练失败，是否继续训练剩余方法？ (y/N): ")
                if response.lower() != 'y':
                    print("⏹️ 用户选择停止训练")
                    break
        
        # 显示总结
        end_time = datetime.now()
        total_duration = end_time - start_time
        
        self._show_training_summary(results, total_duration)
        
        # 检查训练完整性
        self._check_training_completeness()
        
        return all(result["success"] for result in results)
    
    def _run_training_script(self, script_path):
        """运行单个训练脚本"""
        try:
            print(f"🚀 开始执行: {script_path}")
            
            # 检查脚本是否存在
            if not os.path.exists(script_path):
                print(f"❌ 脚本不存在: {script_path}")
                return False
            
            # 运行训练脚本
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=14400,  # 4小时超时
                cwd=os.path.dirname(__file__)
            )
            
            if result.returncode == 0:
                print(f"✅ 脚本执行成功")
                return True
            else:
                print(f"❌ 脚本执行失败")
                print(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 脚本执行超时（4小时）")
            return False
        except Exception as e:
            print(f"💥 脚本执行异常: {e}")
            return False
    
    def _show_training_summary(self, results, total_duration):
        """显示训练总结"""
        print(f"\n{'='*60}")
        print(f"📊 训练总结")
        print(f"{'='*60}")
        
        print(f"⏰ 总耗时: {total_duration}")
        
        successful_count = sum(1 for result in results if result["success"])
        print(f"✅ 成功训练: {successful_count}/{len(results)} 个方法")
        
        print(f"\n📋 详细结果:")
        for result in results:
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {result['method']}")
            print(f"      脚本: {result['script']}")
            print(f"      预计时间: {result['estimated_time']}")
            print(f"      实际用时: {result['duration']}")
            print()
        
        if successful_count == len(results):
            print(f"🎉 所有方法训练完成！")
            print(f"\n📊 训练结果位置:")
            print(f"   • 本文方法: results/our_method/")
            print(f"   • TD3基线: results/baseline_td3/")
            print(f"   • DWA-TD3基线: results/baseline_dwa_td3/")
            print(f"   • PPO基线: results/baseline_ppo/")
            print(f"   • 传统DWA基线: results/baseline_traditional/")
            print(f"\n🎯 下一步: 运行实验验证")
            print(f"   python RUN_EXPERIMENTS_ONLY.py")
        else:
            print(f"⚠️ 部分方法训练失败")
            print(f"📋 建议:")
            print(f"   1. 检查失败方法的错误信息")
            print(f"   2. 单独重新训练失败的方法")
            print(f"   3. 或者使用成功训练的方法继续实验")
    
    def _check_training_completeness(self):
        """检查训练完整性"""
        print(f"\n🔍 检查训练完整性...")
        
        # 这里可以添加检查训练模型文件是否存在的逻辑
        expected_models = [
            "results/our_method/training_*/model_final.pkl",
            "results/baseline_td3/training_*/model_final.pkl",
            "results/baseline_dwa_td3/training_*/model_final.pkl",
            "results/baseline_ppo/training_*/model_final.pkl",
            "results/baseline_traditional/testing_*/testing_stats.json"
        ]
        
        import glob
        
        found_models = 0
        for pattern in expected_models:
            files = glob.glob(pattern)
            if files:
                found_models += 1
                print(f"   ✅ 找到模型: {files[0]}")
            else:
                print(f"   ❌ 缺少模型: {pattern}")
        
        print(f"\n📊 模型完整性: {found_models}/{len(expected_models)}")

def main():
    """主函数"""
    print("🏋️ 只运行训练")
    print("🎯 训练DWA-RL巡飞弹路径规划算法的所有方法")
    
    # 创建训练运行器
    runner = TrainingOnlyRunner()
    
    # 运行所有训练
    success = runner.run_all_training()
    
    if success:
        print(f"\n🎉 所有方法训练成功完成！")
        print(f"🎯 现在可以运行实验验证:")
        print(f"   python RUN_EXPERIMENTS_ONLY.py")
    else:
        print(f"\n⚠️ 部分方法训练完成")
        print(f"📋 请检查失败的方法并重新训练")

if __name__ == "__main__":
    main()
