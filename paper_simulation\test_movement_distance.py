#!/usr/bin/env python3
"""
测试移动距离是否与巡飞简化ver一致
"""

import numpy as np

def test_movement_distance():
    """测试单步移动距离"""
    print("🔧 测试移动距离是否与巡飞简化ver一致")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        
        # 创建环境
        env = PaperSimulationEnvironment('stage3_dynamic')
        
        print(f"✅ 环境创建成功")
        print(f"物理参数:")
        print(f"  时间步长: {env.physics_config['time_step']}秒")
        print(f"  巡航速度: {env.physics_config['V_cruise']} m/s")
        print(f"  最大切向加速度: {env.physics_config['a_T_max']} m/s²")
        print(f"  最大法向加速度: {env.physics_config['a_N_max']} m/s²")
        
        # 重置环境
        state = env.reset()
        print(f"\n初始状态: {state}")
        print(f"初始速度: {state[3]:.1f} m/s")
        
        # 测试不同的控制输入
        test_actions = [
            np.array([8.0, 0.0, 0.0]),      # 最大切向加速度
            np.array([0.0, 39.24, 0.0]),    # 最大法向加速度
            np.array([8.0, 39.24, 0.0]),    # 最大加速度组合
            np.array([4.0, 15.0, 0.5]),     # 中等加速度
            np.array([0.0, 0.0, 0.0]),      # 零加速度
        ]
        
        action_names = [
            "最大切向加速度",
            "最大法向加速度", 
            "最大加速度组合",
            "中等加速度",
            "零加速度"
        ]
        
        print(f"\n测试不同控制输入的移动距离:")
        print("控制输入 | 移动距离 | 速度变化 | 新位置")
        print("-" * 70)
        
        for i, (action, name) in enumerate(zip(test_actions, action_names)):
            # 重置到相同初始状态
            state = env.reset()
            initial_pos = state[:3].copy()
            initial_speed = state[3]
            
            # 执行一步
            next_state, reward, done, info = env.step(action)
            
            # 计算移动距离
            movement = np.linalg.norm(next_state[:3] - initial_pos)
            speed_change = next_state[3] - initial_speed
            
            print(f"{name:12s} | {movement:8.1f}米 | {speed_change:+6.1f} m/s | {next_state[:3]}")
        
        # 测试连续移动
        print(f"\n测试连续移动（10步）:")
        state = env.reset()
        initial_pos = state[:3].copy()
        
        action = np.array([4.0, 15.0, 0.5])  # 中等加速度
        trajectory = [initial_pos]
        
        print("步骤 | 位置 | 移动距离 | 累计距离 | 速度")
        print("-" * 60)
        
        total_distance = 0
        for step in range(10):
            next_state, reward, done, info = env.step(action)
            
            movement = np.linalg.norm(next_state[:3] - state[:3])
            total_distance += movement
            
            print(f"{step+1:4d} | {next_state[:3]} | {movement:8.1f}米 | {total_distance:8.1f}米 | {next_state[3]:5.1f} m/s")
            
            trajectory.append(next_state[:3].copy())
            state = next_state
            
            if done:
                print(f"     Episode在第{step+1}步结束: {info}")
                break
        
        print(f"\n总结:")
        print(f"  10步总移动距离: {total_distance:.1f}米")
        print(f"  平均每步移动: {total_distance/min(10, step+1):.1f}米")
        print(f"  最终速度: {state[3]:.1f} m/s")
        
        # 与巡飞简化ver对比
        print(f"\n与巡飞简化ver对比:")
        print(f"  巡飞简化ver预期每步移动: ~25米 (25 m/s × 0.1s)")
        print(f"  我们的实际每步移动: {total_distance/min(10, step+1):.1f}米")
        
        if total_distance/min(10, step+1) < 20:
            print(f"  ❌ 移动距离偏小，可能存在参数问题")
        else:
            print(f"  ✅ 移动距离合理")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_simplified_version():
    """与巡飞简化ver的理论计算对比"""
    print(f"\n🔬 理论计算对比:")
    print("=" * 40)
    
    # 巡飞简化ver的参数
    dt = 0.1  # 时间步长
    V_cruise = 25.0  # 巡航速度
    a_T_max = 8.0  # 最大切向加速度
    a_N_max = 39.24  # 最大法向加速度
    
    print(f"巡飞简化ver参数:")
    print(f"  时间步长: {dt}秒")
    print(f"  巡航速度: {V_cruise} m/s")
    print(f"  最大切向加速度: {a_T_max} m/s²")
    print(f"  最大法向加速度: {a_N_max} m/s²")
    
    # 理论计算
    print(f"\n理论移动距离计算:")
    
    # 1. 零加速度情况
    distance_zero = V_cruise * dt
    print(f"  零加速度: V×dt = {V_cruise}×{dt} = {distance_zero:.1f}米")
    
    # 2. 最大切向加速度情况
    # s = v0*t + 0.5*a*t²
    distance_max_tangential = V_cruise * dt + 0.5 * a_T_max * dt**2
    print(f"  最大切向加速度: V×dt + 0.5×a_T×dt² = {V_cruise}×{dt} + 0.5×{a_T_max}×{dt}² = {distance_max_tangential:.1f}米")
    
    # 3. 最大法向加速度情况（主要影响方向，距离变化较小）
    distance_max_normal = V_cruise * dt  # 法向加速度主要改变方向
    print(f"  最大法向加速度: ≈V×dt = {distance_max_normal:.1f}米 (主要改变方向)")
    
    print(f"\n预期移动距离范围: {distance_zero:.1f} - {distance_max_tangential:.1f}米")

def main():
    """主函数"""
    print("🎯 移动距离验证测试")
    print("检查是否与巡飞简化ver完全一致")
    
    # 理论对比
    compare_with_simplified_version()
    
    # 实际测试
    success = test_movement_distance()
    
    print(f"\n" + "="*80)
    if success:
        print("🎉 移动距离测试完成！")
        print("请检查实际移动距离是否与理论值匹配")
    else:
        print("❌ 移动距离测试失败！")

if __name__ == "__main__":
    main()
