# 🎯 独立实验模块说明

## 📋 **模块化实验设计**

每个实验都是独立的Python脚本，可以单独运行、中断、恢复。避免了一次性运行所有实验的问题。

### **🔧 核心特性**
- ✅ **独立运行**: 每个算法单独训练/测试
- ✅ **中断恢复**: 支持Ctrl+C优雅中断和恢复
- ✅ **自动保存**: 每50个episode自动保存检查点
- ✅ **完整输出**: JSON、CSV、图表、模型、日志
- ✅ **进度监控**: 实时显示训练进度和性能指标

---

## 📁 **实验模块列表**

### **基线对比实验**

#### **01_dwa_td3.py - DWA-TD3算法（本文方法）**
```bash
# 新训练
python 01_dwa_td3.py

# 恢复训练
python 01_dwa_td3.py --resume

# 从指定检查点恢复
python 01_dwa_td3.py --resume --checkpoint /path/to/checkpoint
```
- **特点**: DWA安全约束 + TD3强化学习
- **训练**: 随机探索150 + 固定强化100 episodes
- **预期**: 高性能，低约束违反

#### **02_pure_td3.py - 纯TD3算法（基线对比）**
```bash
# 新训练
python 02_pure_td3.py

# 恢复训练
python 02_pure_td3.py --resume
```
- **特点**: 纯强化学习，无DWA安全约束
- **训练**: 随机探索150 + 固定强化100 episodes
- **预期**: 性能较好，但约束违反较多

#### **03_traditional_dwa.py - 传统DWA算法（经典方法）**
```bash
# 运行测试（默认50次）
python 03_traditional_dwa.py

# 指定测试次数
python 03_traditional_dwa.py --episodes 100

# 恢复测试
python 03_traditional_dwa.py --resume
```
- **特点**: 经典动态窗口法，无强化学习
- **测试**: 直接测试，无需训练
- **预期**: 性能稳定但有限

#### **04_ppo_constrained.py - PPO约束算法（基线对比）**
```bash
# 新训练
python 04_ppo_constrained.py

# 恢复训练
python 04_ppo_constrained.py --resume
```
- **特点**: PPO + 约束优化
- **训练**: 随机探索150 + 固定强化100 episodes
- **预期**: 约束违反少，但性能可能不如TD3

### **ResBand算法验证实验**

#### **05_resband_ucb.py - ResBand算法（本文方法）**
```bash
# 新训练
python 05_resband_ucb.py

# 恢复训练
python 05_resband_ucb.py --resume
```
- **特点**: UCB多臂老虎机分辨率选择
- **训练**: 随机探索120 + 固定强化80 episodes
- **预期**: 自适应分辨率，高效学习

#### **06_fixed_coarse.py - 固定粗分辨率（对比基线）**
```bash
python 06_fixed_coarse.py
python 06_fixed_coarse.py --resume
```

#### **07_fixed_medium.py - 固定中等分辨率（对比基线）**
```bash
python 07_fixed_medium.py
python 07_fixed_medium.py --resume
```

#### **08_fixed_fine.py - 固定精细分辨率（对比基线）**
```bash
python 08_fixed_fine.py
python 08_fixed_fine.py --resume
```

#### **09_heuristic.py - 启发式调度（对比基线）**
```bash
python 09_heuristic.py
python 09_heuristic.py --resume
```

### **场景复杂度验证实验**

#### **10_stage2_complex.py - Stage2复杂场景**
```bash
python 10_stage2_complex.py
python 10_stage2_complex.py --resume
```
- **特点**: 高密度静态障碍物环境
- **训练**: 随机探索100 + 固定强化80 episodes

#### **11_stage3_dynamic.py - Stage3动态环境**
```bash
python 11_stage3_dynamic.py
python 11_stage3_dynamic.py --resume
```
- **特点**: 高密度动态障碍物环境
- **训练**: 随机探索80 + 固定强化60 episodes

---

## 🔄 **中断和恢复机制**

### **优雅中断**
```bash
# 训练过程中按Ctrl+C
^C
⚠️  接收到中断信号，正在保存检查点...
💾 中断检查点已保存
🔄 恢复命令: python 01_dwa_td3.py --resume
```

### **自动检查点**
```bash
# 每50个episode自动保存
Episode 50: 奖励 = 1250.50, 平均奖励 = 1180.30
💾 检查点已保存: auto_save_ep50

Episode 100: 奖励 = 1380.20, 平均奖励 = 1290.15
💾 检查点已保存: auto_save_ep100
```

### **恢复训练**
```bash
# 自动查找最新检查点
python 01_dwa_td3.py --resume
🔍 找到最新检查点: /path/to/latest/checkpoint
🔄 从Episode 100恢复训练

# 指定检查点
python 01_dwa_td3.py --resume --checkpoint /path/to/specific/checkpoint
```

---

## 📊 **输出结构**

每个实验运行后会在以下目录生成完整结果：

```
paper_simulation/results/individual/
└── experiment_method_YYYYMMDD_HHMMSS/
    ├── 📊 data/
    │   ├── method_results.json      # 详细JSON结果
    │   └── method_report.txt        # 文本报告
    ├── 💾 models/
    │   └── final_model.pth          # 训练好的模型
    ├── 📈 plots/
    │   ├── convergence_curve.png    # 收敛曲线
    │   └── performance_summary.png  # 性能摘要
    ├── 💾 checkpoints/
    │   ├── auto_save_ep50/          # 自动检查点
    │   ├── auto_save_ep100/
    │   └── random_phase_complete/   # 阶段检查点
    └── 📝 logs/
        └── training.log             # 详细日志
```

---

## 🎯 **推荐运行顺序**

### **第一阶段：基线对比**
```bash
# 1. 运行本文核心方法
python 01_dwa_td3.py

# 2. 运行基线对比方法
python 02_pure_td3.py
python 03_traditional_dwa.py
python 04_ppo_constrained.py

# 3. 分析基线对比结果
python ../analysis/compare_baseline.py
```

### **第二阶段：ResBand验证**
```bash
# 1. 运行ResBand算法
python 05_resband_ucb.py

# 2. 运行固定分辨率对比
python 06_fixed_coarse.py
python 07_fixed_medium.py
python 08_fixed_fine.py
python 09_heuristic.py

# 3. 分析ResBand结果
python ../analysis/compare_resband.py
```

### **第三阶段：复杂度验证**
```bash
# 1. 运行复杂场景验证
python 10_stage2_complex.py
python 11_stage3_dynamic.py

# 2. 最终综合分析
python ../analysis/final_analysis.py
```

---

## ⚠️ **注意事项**

### **训练时间预估**
- **强化学习方法**: 2-4小时（250 episodes）
- **ResBand系列**: 1.5-3小时（200 episodes）
- **传统DWA**: 30-60分钟（50次测试）
- **复杂场景**: 2-3小时（140-180 episodes）

### **资源需求**
- **内存**: 建议8GB以上
- **存储**: 每个实验约100-200MB结果文件
- **GPU**: 可选，但会显著加速训练

### **中断恢复最佳实践**
1. **定期检查**: 每50个episode自动保存
2. **阶段保存**: 随机阶段完成后保存
3. **错误保存**: 发生错误时自动保存
4. **手动保存**: 可在任意时刻Ctrl+C保存

### **并行运行**
```bash
# 可以同时运行多个不冲突的实验
python 01_dwa_td3.py &
python 02_pure_td3.py &
python 03_traditional_dwa.py &

# 等待所有完成
wait
```

---

## 🔗 **下一步**

1. **选择实验**: 根据需要选择要运行的实验模块
2. **开始训练**: 运行对应的Python脚本
3. **监控进度**: 观察日志输出和检查点保存
4. **分析结果**: 使用analysis模块进行对比分析
5. **生成报告**: 汇总所有结果生成最终报告

**每个实验都是独立的，可以根据时间和资源灵活安排！** 🚀
