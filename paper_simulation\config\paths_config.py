"""
统一路径配置
Unified Path Configuration

定义所有训练、测试、保存的统一路径，确保一致性
"""

import os
from datetime import datetime

class PathsConfig:
    """统一路径配置类"""
    
    # 基础目录
    BASE_DIR = "results"
    
    # 训练结果目录
    TRAINING_DIRS = {
        "our_method": "our_method",
        "pure_td3": "pure_td3", 
        "traditional_dwa": "traditional_dwa",
        "dwa_td3_fixed": "dwa_td3_fixed",
        "ppo_constrained": "ppo_constrained"
    }
    
    # 实验结果目录
    EXPERIMENT_DIRS = {
        "safety_verification": "experiment_1_safety",
        "resband_verification": "experiment_2_resband",
        "counterintuitive_validation": "counterintuitive_validation",
        "performance_comparison": "performance_comparison"
    }
    
    # 可视化结果目录
    VISUALIZATION_DIR = "visualization_results"
    
    # 模型文件名
    MODEL_FILES = {
        "final_model": "model_final.pkl",
        "checkpoint": "checkpoint_episode_{}.pkl",
        "training_stats": "training_stats.json",
        "training_config": "training_config.json"
    }
    
    @classmethod
    def get_training_dir(cls, method_name, create_timestamp=True):
        """获取训练目录路径"""
        if method_name not in cls.TRAINING_DIRS:
            raise ValueError(f"未知训练方法: {method_name}")
        
        base_path = os.path.join(cls.BASE_DIR, cls.TRAINING_DIRS[method_name])
        
        if create_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return os.path.join(base_path, f"training_{timestamp}")
        else:
            return base_path
    
    @classmethod
    def get_experiment_dir(cls, experiment_name, create_timestamp=True):
        """获取实验目录路径"""
        if experiment_name not in cls.EXPERIMENT_DIRS:
            raise ValueError(f"未知实验: {experiment_name}")
        
        base_path = os.path.join(cls.BASE_DIR, cls.EXPERIMENT_DIRS[experiment_name])
        
        if create_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return os.path.join(base_path, f"{experiment_name}_{timestamp}")
        else:
            return base_path
    
    @classmethod
    def get_visualization_dir(cls, create_timestamp=True):
        """获取可视化目录路径"""
        base_path = os.path.join(cls.BASE_DIR, cls.VISUALIZATION_DIR)
        
        if create_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return os.path.join(base_path, f"figures_{timestamp}")
        else:
            return base_path
    
    @classmethod
    def find_latest_training_dir(cls, method_name):
        """查找最新的训练目录"""
        import glob
        
        base_path = os.path.join(cls.BASE_DIR, cls.TRAINING_DIRS[method_name])
        pattern = os.path.join(base_path, "training_*")
        
        dirs = glob.glob(pattern)
        if dirs:
            return max(dirs, key=os.path.getctime)
        return None
    
    @classmethod
    def find_latest_model(cls, method_name):
        """查找最新的训练模型"""
        import glob
        
        # 首先查找最终模型
        latest_dir = cls.find_latest_training_dir(method_name)
        if latest_dir:
            final_model_path = os.path.join(latest_dir, cls.MODEL_FILES["final_model"])
            if os.path.exists(final_model_path):
                return final_model_path
            
            # 如果没有最终模型，查找最新的checkpoint
            checkpoint_pattern = os.path.join(latest_dir, "checkpoint_episode_*.pkl")
            checkpoints = glob.glob(checkpoint_pattern)
            if checkpoints:
                return max(checkpoints, key=os.path.getctime)
        
        return None
    
    @classmethod
    def get_model_save_path(cls, output_dir, model_type="final", episode=None):
        """获取模型保存路径"""
        if model_type == "final":
            return os.path.join(output_dir, cls.MODEL_FILES["final_model"])
        elif model_type == "checkpoint":
            if episode is None:
                raise ValueError("checkpoint类型需要指定episode")
            return os.path.join(output_dir, cls.MODEL_FILES["checkpoint"].format(episode))
        else:
            raise ValueError(f"未知模型类型: {model_type}")
    
    @classmethod
    def get_stats_save_path(cls, output_dir):
        """获取统计数据保存路径"""
        return os.path.join(output_dir, cls.MODEL_FILES["training_stats"])
    
    @classmethod
    def get_config_save_path(cls, output_dir):
        """获取配置保存路径"""
        return os.path.join(output_dir, cls.MODEL_FILES["training_config"])
    
    @classmethod
    def ensure_dir_exists(cls, dir_path):
        """确保目录存在"""
        os.makedirs(dir_path, exist_ok=True)
        return dir_path
    
    @classmethod
    def get_all_training_methods(cls):
        """获取所有训练方法列表"""
        return list(cls.TRAINING_DIRS.keys())
    
    @classmethod
    def get_all_experiments(cls):
        """获取所有实验列表"""
        return list(cls.EXPERIMENT_DIRS.keys())
    
    @classmethod
    def check_training_completeness(cls):
        """检查训练完整性"""
        completeness = {}
        
        for method in cls.TRAINING_DIRS.keys():
            model_path = cls.find_latest_model(method)
            completeness[method] = {
                "has_model": model_path is not None,
                "model_path": model_path,
                "training_dir": cls.find_latest_training_dir(method)
            }
        
        return completeness
    
    @classmethod
    def print_path_summary(cls):
        """打印路径配置摘要"""
        print("📁 统一路径配置摘要")
        print("=" * 50)
        
        print("\n🏋️ 训练方法路径:")
        for method, path in cls.TRAINING_DIRS.items():
            print(f"   {method}: {os.path.join(cls.BASE_DIR, path)}")
        
        print("\n🧪 实验路径:")
        for experiment, path in cls.EXPERIMENT_DIRS.items():
            print(f"   {experiment}: {os.path.join(cls.BASE_DIR, path)}")
        
        print(f"\n📊 可视化路径: {os.path.join(cls.BASE_DIR, cls.VISUALIZATION_DIR)}")
        
        print("\n💾 模型文件:")
        for file_type, filename in cls.MODEL_FILES.items():
            print(f"   {file_type}: {filename}")

# 全局路径配置实例
PATHS = PathsConfig()

def main():
    """测试路径配置"""
    PATHS.print_path_summary()
    
    print("\n🔍 检查训练完整性:")
    completeness = PATHS.check_training_completeness()
    for method, status in completeness.items():
        status_icon = "✅" if status["has_model"] else "❌"
        print(f"   {status_icon} {method}: {status['model_path'] or '无模型'}")

if __name__ == "__main__":
    main()
