#!/usr/bin/env python3
"""
详细调试训练流程
模拟实验中的完整训练过程
"""

def debug_training_flow_detailed():
    """详细调试训练流程"""
    print("🔍 详细调试训练流程")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体（完全模拟实验）
        env = PaperSimulationEnvironment("stage3_dynamic")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        print(f"✅ 环境和智能体创建成功")
        
        # 模拟训练episode（完全模拟实验的_train_episode方法）
        max_steps_per_episode = 2000
        
        for episode in range(3):  # 测试3个episodes
            print(f"\n🎯 Episode {episode + 1}")
            print("=" * 40)
            
            # 步骤1: 重置环境（模拟训练器）
            print(f"步骤1: 训练器重置环境")
            state = env.reset()
            print(f"   重置后状态: {state[:3]}")
            print(f"   重置后step_count: {env.step_count}")
            print(f"   环境内部状态: {env.state[:3]}")
            
            # 步骤2: 调用agent.train_step（模拟训练器）
            print(f"步骤2: 调用agent.train_step")
            print(f"   传入状态: {state[:3]}")
            print(f"   调用前env.state: {env.state[:3]}")
            print(f"   调用前env.step_count: {env.step_count}")
            
            training_progress = (episode + 1) / 600
            step_info = agent.train_step(state, training_progress=training_progress)
            
            print(f"   调用后env.state: {env.state[:3]}")
            print(f"   调用后env.step_count: {env.step_count}")
            print(f"   返回的next_state: {step_info['next_state'][:3]}")
            print(f"   返回的动作: {step_info['action']}")
            print(f"   返回的奖励: {step_info['reward']:.3f}")
            print(f"   返回的done: {step_info['done']}")
            print(f"   返回的info: {step_info.get('info', {})}")
            
            # 步骤3: 分析结果
            movement = np.linalg.norm(step_info['next_state'][:3] - state[:3])
            print(f"   移动距离: {movement:.3f}米")
            
            # 检查异常
            if movement > 10:
                print(f"   🚨 移动距离异常: {movement:.1f}米")
                
                # 详细分析异常原因
                print(f"   🔍 异常分析:")
                print(f"     初始位置: {state[:3]}")
                print(f"     最终位置: {step_info['next_state'][:3]}")
                print(f"     环境状态: {env.state[:3]}")
                print(f"     是否被边界约束: x={step_info['next_state'][0]==50}, z={step_info['next_state'][2]==50}")
                
                return False
            
            if step_info['done'] and env.step_count == 1:
                print(f"   🚨 第1步就结束")
                
                # 分析终止原因
                info = step_info.get('info', {})
                if info.get('timeout'):
                    print(f"     原因: 超时 - 这不应该在第1步发生")
                    print(f"     step_count: {env.step_count}")
                    print(f"     max_steps: {max_steps_per_episode}")
                
                return False
            
            print(f"   ✅ Episode {episode + 1} 第1步正常")
            
            # 如果第一个episode有问题，立即停止
            if episode == 0 and (movement > 10 or (step_info['done'] and env.step_count == 1)):
                print(f"\n❌ 第一个episode就有问题，停止调试")
                break
        
        print(f"\n🎉 所有Episodes的第1步都正常！")
        return True
        
    except Exception as e:
        print(f"❌ 训练流程调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_agent_train_step():
    """专门调试智能体的train_step方法"""
    print("\n🤖 专门调试智能体的train_step方法")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage3_dynamic")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        # 重置环境
        state = env.reset()
        print(f"初始状态: {state[:3]}")
        print(f"环境内部状态: {env.state[:3]}")
        
        # 手动调用train_step的各个步骤
        print(f"\n🔍 手动调用train_step的各个步骤:")
        
        # 步骤1: DWA生成安全动作
        print(f"步骤1: DWA生成安全动作")
        safe_actions = agent.dwa.get_safe_actions(state)
        print(f"   安全动作数量: {len(safe_actions)}")
        
        # 步骤2: ResBand选择分辨率
        print(f"步骤2: ResBand选择分辨率")
        resolution_config = agent.resband.select_resolution(state, safe_actions)
        print(f"   选择的分辨率: {resolution_config}")
        
        # 步骤3: RL选择动作
        print(f"步骤3: RL选择动作")
        if len(safe_actions) > 0:
            selected_action = safe_actions[0]  # 选择第一个安全动作
        else:
            selected_action = np.array([0.0, 0.0, 0.0])
        print(f"   选择的动作: {selected_action}")
        
        # 步骤4: 执行动作
        print(f"步骤4: 执行动作")
        print(f"   执行前env.state: {env.state[:3]}")
        print(f"   执行前env.step_count: {env.step_count}")
        
        next_state, reward, done, info = env.step(selected_action)
        
        print(f"   执行后env.state: {env.state[:3]}")
        print(f"   执行后env.step_count: {env.step_count}")
        print(f"   返回的next_state: {next_state[:3]}")
        print(f"   返回的reward: {reward:.3f}")
        print(f"   返回的done: {done}")
        print(f"   返回的info: {info}")
        
        # 检查结果
        movement = np.linalg.norm(next_state[:3] - state[:3])
        print(f"   移动距离: {movement:.3f}米")
        
        if movement > 10:
            print(f"   🚨 移动距离异常")
            return False
        
        if done and env.step_count == 1:
            print(f"   🚨 第1步就结束")
            return False
        
        print(f"   ✅ 手动train_step正常")
        
        # 现在调用完整的train_step
        print(f"\n🚀 调用完整的train_step:")
        env.reset()  # 重新重置
        state = env.state.copy()
        
        step_info = agent.train_step(state, training_progress=0.1)
        
        movement = np.linalg.norm(step_info['next_state'][:3] - state[:3])
        print(f"   移动距离: {movement:.3f}米")
        print(f"   完成: {step_info['done']}")
        
        if movement > 10:
            print(f"   🚨 完整train_step移动异常")
            return False
        
        if step_info['done'] and env.step_count == 1:
            print(f"   🚨 完整train_step第1步结束")
            return False
        
        print(f"   ✅ 完整train_step正常")
        return True
        
    except Exception as e:
        print(f"❌ 智能体train_step调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 详细训练流程调试")
    
    # 调试训练流程
    flow_success = debug_training_flow_detailed()
    
    # 调试智能体train_step
    agent_success = debug_agent_train_step()
    
    if flow_success and agent_success:
        print("\n" + "=" * 80)
        print("🎯 详细训练流程调试完成！")
        print("训练流程正常工作")
    else:
        print("\n❌ 发现训练流程问题")
        print("需要进一步调试")

if __name__ == "__main__":
    main()
