#!/usr/bin/env python3
"""
独立实验3: 传统DWA算法测试
经典的动态窗口法，无强化学习组件

运行方式:
1. 运行测试: python 03_traditional_dwa.py
2. 指定测试次数: python 03_traditional_dwa.py --episodes 100

特点:
- 传统DWA算法，无需训练
- 直接在多个随机场景中测试性能
- 用于对比验证强化学习方法的优势
- 支持中断和恢复（虽然是测试，但可能耗时较长）
"""

import os
import sys
import argparse
import numpy as np
import time
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from paper_simulation.experiments.utils.base_trainer import BaseTrainer

# 导入核心组件（已复制到本地）- 传统DWA只需要环境和DWA控制器
from paper_simulation.core import (
    LoiteringMunitionEnvironment,
    LoiteringMunitionDWA,
    get_environment_config,
    get_loitering_munition_config,
    get_dwa_config
)

class TraditionalDWA_Tester(BaseTrainer):
    """传统DWA测试器 - 基于巡飞简化ver的DWA框架"""

    def __init__(self, test_episodes: int = 50):
        super().__init__("baseline_comparison", "traditional_dwa")

        # 设置随机种子
        np.random.seed(42)

        # 测试配置
        self.config = {
            "test_episodes": test_episodes,   # 测试episodes数量
            "stage": 1,                       # 测试阶段
            "algorithm": "traditional_dwa",
            "visualization_interval": 10
        }

        # 获取巡飞简化ver的配置
        self.lm_config = get_loitering_munition_config()
        self.dwa_config = get_dwa_config()

        # 测试结果
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "config": self.config,
            "test_data": {
                "episode_rewards": [],
                "episode_lengths": [],
                "constraint_violations": []
            }
        }

        self.logger.info("🎯 传统DWA测试器初始化完成")
        self.logger.info(f"📋 测试配置: {self.config}")
        self.logger.info("✅ 核心技术组件:")
        self.logger.info("  • LoiteringMunitionEnvironment: 真实的巡飞弹环境")
        self.logger.info("  • LoiteringMunitionDWA: 传统动态窗口法")
        self.logger.info("ℹ️  注意: 传统DWA无需训练，直接测试性能")
    
    def create_environment(self):
        """创建真实的巡飞弹测试环境"""
        self.logger.info("🌍 创建巡飞弹测试环境...")

        # 获取环境配置（与其他方法相同的环境，确保公平对比）
        env_config = get_environment_config("stage1_simple")

        # 创建真实的巡飞弹环境
        env = LoiteringMunitionEnvironment(
            bounds=[2000, 2000, 200],
            environment_config=env_config,
            reward_type='simplified'
        )

        self.logger.info(f"✅ 巡飞弹环境创建完成")
        self.logger.info(f"  环境类型: stage1_simple")
        self.logger.info(f"  静态障碍物数量: {env_config['static_obstacle_count']}")
        self.logger.info(f"  动态障碍物: {'启用' if env_config['enable_dynamic_obstacles'] else '禁用'}")
        self.logger.info(f"  描述: {env_config['description']}")

        return env

    def create_dwa_controller(self):
        """创建真实的传统DWA控制器"""
        self.logger.info("🤖 创建传统DWA控制器...")

        # 创建传统DWA控制器（使用固定分辨率配置）
        dwa_controller = LoiteringMunitionDWA(dt=self.lm_config['dt'])

        # 传统模式（不使用ResBand等高级功能）
        # dwa_controller.set_traditional_mode(True)  # 如果方法不存在则注释掉

        self.logger.info(f"✅ 传统DWA控制器创建完成")
        self.logger.info(f"  算法类型: 经典动态窗口法")
        self.logger.info(f"  时间步长: {self.lm_config['dt']}")
        self.logger.info(f"  分辨率配置: 固定中等分辨率")
        self.logger.info("ℹ️  无强化学习组件，纯基于规则的路径规划")

        return dwa_controller
    
    def run_test_episodes(self, env, dwa_controller, start_episode: int = 0):
        """运行测试episodes - 使用真实的传统DWA测试流程"""
        self.logger.info(f"🧪 开始传统DWA测试 ({self.config['test_episodes']} episodes)")

        for episode in range(start_episode, self.config['test_episodes']):
            if self.interrupted:
                break

            # 重置环境
            state = env.reset()

            # 运行episode（传统DWA，无强化学习）
            episode_reward = 0
            episode_length = 0
            violations = 0
            done = False

            while not done and episode_length < 1000:
                # 传统DWA生成最优动作
                try:
                    # 获取环境中的障碍物和目标
                    obstacles = env.obstacles if hasattr(env, 'obstacles') else []
                    goal = env.goal if hasattr(env, 'goal') else [1800, 1800, 100]

                    action = dwa_controller.select_best_control(state, obstacles, goal)
                except Exception as e:
                    # 如果DWA失败，使用简单的前进动作
                    action = np.array([1.0, 0.0, 0.0])

                # 执行动作
                next_observation, reward, done, info = env.step(action)
                next_state = next_observation

                # 记录约束违反
                if info.get('constraint_violation', False):
                    violations += 1

                state = next_state
                episode_reward += reward
                episode_length += 1

            # 记录episode结果
            self.test_results["test_data"]["episode_rewards"].append(episode_reward)
            self.test_results["test_data"]["episode_lengths"].append(episode_length)
            self.test_results["test_data"]["constraint_violations"].append(violations)

            # 记录数据到基类（传统DWA没有训练阶段，都标记为"test"）
            self.record_episode_data(episode, episode_reward, episode_length, violations, "test")

            # 打印进度
            self.print_progress(episode, episode_reward, "测试")

            # 定期保存进度（虽然是测试，但可能耗时较长）
            if episode % 20 == 0 and episode > 0:
                self.save_checkpoint(f"test_progress_ep{episode}", None, {"test_progress": episode})
    
    def test(self, resume_from_checkpoint: str = None):
        """主测试函数 - 使用真实的传统DWA测试流程"""
        self.logger.info("🚀 开始传统DWA测试")

        # 创建环境和控制器
        env = self.create_environment()
        dwa_controller = self.create_dwa_controller()

        # 恢复进度（如果需要）
        start_episode = 0

        if resume_from_checkpoint:
            checkpoint_data = self.load_checkpoint(resume_from_checkpoint)
            if checkpoint_data:
                start_episode = checkpoint_data.get("additional_data", {}).get("test_progress", 0)
                self.logger.info(f"🔄 从Episode {start_episode}恢复测试")

        try:
            # 运行测试episodes
            self.run_test_episodes(env, dwa_controller, start_episode)

            # 保存最终结果
            if not self.interrupted:
                self.logger.info("✅ 测试完成，保存最终结果...")

                # 收集传统DWA结果
                traditional_dwa_results = {
                    "config": self.config,
                    "test_results": self.test_results,
                    "algorithm_type": "traditional_dwa"
                }

                results = self.save_final_results(traditional_dwa_results)

                # 打印测试摘要
                summary = self.get_training_summary()
                self.logger.info("📊 传统DWA测试摘要:")
                self.logger.info(f"  总测试Episodes: {summary['total_episodes']}")
                self.logger.info(f"  平均奖励: {summary['avg_reward']:.2f}")
                self.logger.info(f"  最佳奖励: {summary['best_reward']:.2f}")
                self.logger.info(f"  成功率: {summary['success_rate']:.2%}")
                self.logger.info(f"  总约束违反: {summary['total_violations']}")

                # 与强化学习方法的对比提示
                self.logger.info("📋 对比分析:")
                self.logger.info("  • 传统方法，性能稳定但有限")
                self.logger.info("  • 预期平均奖励低于强化学习方法")
                self.logger.info("  • 约束违反介于DWA-TD3和纯TD3之间")
                self.logger.info("  • 用于验证强化学习方法的优势")

                self.logger.info("🏗️ 算法特点:")
                self.logger.info(f"  传统DWA: ✅启用")
                self.logger.info(f"  强化学习: ❌禁用")
                self.logger.info(f"  动作来源: 基于规则的路径规划")

                self.logger.info(f"📁 结果保存在: {self.experiment_dir}")

                return results
            else:
                self.logger.info("⚠️  测试被中断")
                return None

        except Exception as e:
            self.logger.error(f"❌ 测试过程中发生错误: {e}")
            # 保存错误时的进度
            self.save_checkpoint(f"error_ep{self.current_episode}", None)
            raise

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="传统DWA独立测试")
    
    parser.add_argument(
        "--episodes", 
        type=int, 
        default=50, 
        help="测试episodes数量（默认50）"
    )
    
    parser.add_argument(
        "--resume", 
        action="store_true", 
        help="恢复测试"
    )
    
    parser.add_argument(
        "--checkpoint", 
        type=str, 
        help="指定检查点路径"
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    print("🎯 传统DWA独立测试")
    print("=" * 50)
    print("📋 实验说明:")
    print("  • 经典动态窗口法，无强化学习组件")
    print("  • 直接测试性能，无需训练过程")
    print("  • 用于对比验证强化学习方法的优势")
    print("  • 预期性能稳定但低于强化学习方法")
    print("  • 支持中断和恢复（测试可能耗时较长）")
    print("=" * 50)
    
    args = parse_arguments()
    
    # 创建测试器
    tester = TraditionalDWA_Tester(args.episodes)
    
    # 确定检查点路径
    checkpoint_path = None
    if args.resume:
        if args.checkpoint:
            checkpoint_path = args.checkpoint
        else:
            # 查找最新检查点
            checkpoint_path = tester.find_latest_checkpoint()
            if checkpoint_path:
                print(f"🔍 找到最新检查点: {checkpoint_path}")
            else:
                print("❌ 没有找到检查点，将开始新测试")
    
    # 开始测试
    try:
        results = tester.test(checkpoint_path)
        
        if results:
            print("\n🎉 传统DWA测试完成!")
            print(f"📁 结果保存在: {tester.experiment_dir}")
            print("\n🔗 下一步:")
            print("  1. 查看测试结果: data/traditional_dwa_results.json")
            print("  2. 与强化学习方法结果对比分析")
            print("  3. 运行分析脚本: python experiments/analysis/compare_baseline.py")
        else:
            print("\n⚠️  测试被中断")
            print(f"🔄 恢复命令: python {__file__} --resume")
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
