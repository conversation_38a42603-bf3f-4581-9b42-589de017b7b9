#!/usr/bin/env python3
"""
调试长时间运行后的Episode终止问题
模拟第一个Episode运行1001步的情况
"""

def debug_long_episode():
    """调试长时间运行后的问题"""
    print("🔍 调试长时间运行后的Episode终止问题")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        print(f"✅ 环境和智能体创建成功")
        
        # 第一个Episode：模拟运行1001步
        print(f"\n🎯 第一个Episode：模拟运行1001步")
        print("=" * 40)
        
        state = env.reset()
        print(f"初始状态: {state[:3]}")
        print(f"初始step_count: {env.step_count}")
        
        # 运行1001步
        for step in range(1001):
            step_info = agent.train_step(state, training_progress=step/1001)
            state = step_info["next_state"]
            
            # 每100步打印一次进度
            if step % 100 == 0:
                print(f"   步骤 {step}: step_count={env.step_count}, done={step_info['done']}")
            
            # 如果提前结束，记录原因
            if step_info["done"]:
                print(f"   Episode在步骤{step}结束: {step_info.get('info', {})}")
                break
        
        print(f"第一个Episode结束: step_count={env.step_count}")
        
        # 第二个Episode：检查是否正常
        print(f"\n🎯 第二个Episode：检查是否正常")
        print("=" * 40)
        
        print(f"重置前step_count: {env.step_count}")
        state = env.reset()
        print(f"重置后step_count: {env.step_count}")
        print(f"重置后状态: {state[:3]}")
        
        # 检查初始终止条件
        done, info = env._check_termination()
        print(f"初始终止检查: done={done}, info={info}")
        
        # 执行第一步
        print(f"执行第一步...")
        step_info = agent.train_step(state, training_progress=0.0)
        
        print(f"第一步结果:")
        print(f"   step_count: {env.step_count}")
        print(f"   done: {step_info['done']}")
        print(f"   info: {step_info.get('info', {})}")
        print(f"   新位置: {step_info['next_state'][:3]}")
        
        # 如果第一步就结束，详细分析
        if step_info['done']:
            print(f"\n🚨 第二个Episode第一步就结束！详细分析:")
            
            from environments.environment_configs import FAST_TRAINING_CONFIG
            max_steps = FAST_TRAINING_CONFIG["max_steps_per_episode"]
            
            print(f"   step_count: {env.step_count}")
            print(f"   max_steps: {max_steps}")
            print(f"   timeout检查: {env.step_count >= max_steps}")
            
            # 检查其他终止条件
            current_pos = step_info['next_state'][:3]
            distance_to_target = np.linalg.norm(current_pos - env.target_pos)
            print(f"   距离目标: {distance_to_target:.1f}m")
            print(f"   成功检查: {distance_to_target < 50}")
            
            collision = env._check_collision()
            print(f"   碰撞检查: {collision}")
            
            stall = step_info['next_state'][3] < env.physics_config["V_min"]
            print(f"   失速检查: {stall}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_training_loop():
    """调试完整的训练循环"""
    print("\n🔄 调试完整的训练循环")
    print("=" * 60)
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建实验（但不运行）
        experiment = ResBandVerificationExperiment("debug_training")
        
        # 创建环境和智能体
        env = experiment._create_environment("stage1_simple")
        agent = experiment._create_agent("resband_adaptive", env)
        
        print(f"✅ 实验环境创建成功")
        
        # 模拟训练3个episodes
        for episode in range(3):
            print(f"\n--- Episode {episode + 1} ---")
            
            # 使用实验的_train_episode方法
            episode_result = experiment._train_episode(agent, env, episode, "resband_adaptive")
            
            print(f"Episode {episode + 1} 结果:")
            print(f"   总奖励: {episode_result['total_reward']:.1f}")
            print(f"   总步数: {episode_result['steps']}")
            print(f"   完成状态: {episode_result['done']}")
            print(f"   成功: {episode_result.get('success', False)}")
            
            # 如果Episode异常短，停止调试
            if episode_result['steps'] <= 1:
                print(f"🚨 Episode {episode + 1} 异常短，停止调试")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 训练循环调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 长时间运行Episode终止问题调试")
    
    # 调试长时间运行
    long_success = debug_long_episode()
    
    # 调试训练循环
    loop_success = debug_training_loop()
    
    if long_success and loop_success:
        print("\n" + "=" * 80)
        print("🎯 长时间运行调试完成！")
        print("如果发现问题，请查看上述详细输出")
    else:
        print("\n❌ 长时间运行调试失败")

if __name__ == "__main__":
    main()
