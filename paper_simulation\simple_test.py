"""
简单测试脚本
"""

print("开始测试...")

try:
    print("1. 测试基础导入...")
    import numpy as np
    print("   ✅ numpy导入成功")
    
    import sys
    import os
    print("   ✅ 系统模块导入成功")
    
    print("2. 测试环境导入...")
    from environments.paper_environment import PaperSimulationEnvironment
    print("   ✅ PaperSimulationEnvironment导入成功")
    
    print("3. 测试算法导入...")
    from algorithms.simplified_td3 import SimplifiedTD3
    print("   ✅ SimplifiedTD3导入成功")
    
    from algorithms.simplified_resband import EnhancedResBand
    print("   ✅ EnhancedResBand导入成功")
    
    from algorithms.correct_reward_system import CorrectRewardSystem
    print("   ✅ CorrectRewardSystem导入成功")
    
    from algorithms.lightweight_dwa_rl import LightweightDWARL
    print("   ✅ LightweightDWARL导入成功")
    
    print("4. 测试基本功能...")
    env = PaperSimulationEnvironment("stage1_simple")
    print("   ✅ 环境创建成功")
    
    state = env.reset()
    print(f"   ✅ 环境重置成功，状态: {state}")
    
    print("\n🎉 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
