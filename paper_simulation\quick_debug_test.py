#!/usr/bin/env python3
"""
快速调试测试
运行一个简短的实验来查看详细信息
"""

def quick_debug_test():
    """快速调试测试"""
    print("🔍 快速调试测试")
    print("=" * 60)
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建实验
        experiment = ResBandVerificationExperiment("quick_debug")
        
        # 修改配置为快速测试
        experiment.config["episodes_per_stage"] = 3  # 每阶段只测试3个episodes
        experiment.config["comparison_methods"] = ["resband_adaptive"]  # 只测试一种方法
        experiment.config["test_scenarios"] = ["stage1_simple"]  # 只测试一个阶段
        
        print(f"✅ 实验创建成功")
        print(f"📋 快速测试配置:")
        print(f"   - 方法: {experiment.config['comparison_methods']}")
        print(f"   - 阶段: {experiment.config['test_scenarios']}")
        print(f"   - Episodes: {experiment.config['episodes_per_stage']}")
        
        # 运行快速测试
        print(f"\n🚀 开始快速调试测试...")
        results = experiment.run_experiment()
        
        if results:
            print(f"\n✅ 快速测试完成")
        else:
            print(f"\n❌ 快速测试失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速调试测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 快速调试测试")
    
    success = quick_debug_test()
    
    if success:
        print("\n🎯 快速调试测试完成！")
        print("请查看上述输出中的详细调试信息")
    else:
        print("\n❌ 快速调试测试失败")

if __name__ == "__main__":
    main()
