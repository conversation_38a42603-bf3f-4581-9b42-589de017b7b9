#!/usr/bin/env python3
"""
基础训练器 - 所有独立实验的基类
提供统一的训练、保存、恢复功能
"""

import os
import sys
import time
import json
import signal
import logging
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

class BaseTrainer:
    """基础训练器类"""
    
    def __init__(self, experiment_name: str, method_name: str):
        self.experiment_name = experiment_name
        self.method_name = method_name
        self.start_time = time.time()
        self.interrupted = False
        
        # 设置日志
        self.setup_logging()
        
        # 创建输出目录
        self.setup_directories()
        
        # 设置信号处理（Ctrl+C优雅退出）
        self.setup_signal_handlers()
        
        # 训练状态
        self.current_episode = 0
        self.training_data = {
            "episode_rewards": [],
            "episode_lengths": [],
            "constraint_violations": [],
            "phase_data": {
                "random_phase": {"rewards": [], "violations": [], "success_count": 0},
                "fixed_phase": {"rewards": [], "violations": [], "success_count": 0}
            }
        }
        
        self.logger.info(f"🚀 初始化训练器: {experiment_name} - {method_name}")
    
    def setup_logging(self):
        """设置日志系统"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_dir = Path("paper_simulation/logs/individual")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"{self.experiment_name}_{self.method_name}_{timestamp}.log"
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(f"{self.experiment_name}_{self.method_name}")
        
        self.logger.info(f"📝 日志文件: {log_file}")
    
    def setup_directories(self):
        """创建输出目录结构"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_dir = Path("paper_simulation/results/individual")
        
        self.experiment_dir = base_dir / f"{self.experiment_name}_{self.method_name}_{timestamp}"
        
        self.dirs = {
            "checkpoints": self.experiment_dir / "checkpoints",
            "models": self.experiment_dir / "models",
            "plots": self.experiment_dir / "plots",
            "data": self.experiment_dir / "data",
            "logs": self.experiment_dir / "logs"
        }
        
        # 创建所有目录
        for dir_path in self.dirs.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"📁 实验目录: {self.experiment_dir}")
    
    def setup_signal_handlers(self):
        """设置信号处理器（Ctrl+C优雅退出）"""
        def signal_handler(signum, frame):
            self.logger.info("⚠️  接收到中断信号，正在保存检查点...")
            self.interrupted = True
            self.save_checkpoint(f"interrupted_ep{self.current_episode}")
            self.logger.info("💾 中断检查点已保存")
            self.logger.info(f"🔄 恢复命令: python {self.get_resume_command()}")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def get_resume_command(self) -> str:
        """获取恢复训练的命令"""
        script_name = f"{self.experiment_name}_{self.method_name}.py"
        return f"paper_simulation/experiments/individual/{script_name} --resume"
    
    def save_checkpoint(self, checkpoint_name: str, agent=None, additional_data: Dict = None) -> str:
        """保存训练检查点"""
        try:
            checkpoint_dir = self.dirs["checkpoints"] / checkpoint_name
            checkpoint_dir.mkdir(exist_ok=True)
            
            # 保存训练状态
            checkpoint_data = {
                "experiment_name": self.experiment_name,
                "method_name": self.method_name,
                "current_episode": self.current_episode,
                "training_data": self.training_data,
                "timestamp": datetime.now().isoformat(),
                "additional_data": additional_data or {}
            }
            
            # 保存检查点数据
            checkpoint_file = checkpoint_dir / "checkpoint.json"
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(self._make_json_serializable(checkpoint_data), f, indent=2, ensure_ascii=False)
            
            # 保存模型（如果提供）
            if agent and hasattr(agent, 'save'):
                model_file = checkpoint_dir / "model.pth"
                agent.save(str(model_file))
                self.logger.info(f"💾 模型已保存: {model_file}")
            
            self.logger.info(f"💾 检查点已保存: {checkpoint_name}")
            return str(checkpoint_dir)
            
        except Exception as e:
            self.logger.error(f"❌ 保存检查点失败: {e}")
            return None
    
    def load_checkpoint(self, checkpoint_path: str) -> Optional[Dict]:
        """加载训练检查点"""
        try:
            checkpoint_file = Path(checkpoint_path) / "checkpoint.json"
            
            if not checkpoint_file.exists():
                self.logger.error(f"❌ 检查点文件不存在: {checkpoint_file}")
                return None
            
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            # 恢复训练状态
            self.current_episode = checkpoint_data.get("current_episode", 0)
            self.training_data = checkpoint_data.get("training_data", self.training_data)
            
            self.logger.info(f"✅ 检查点已加载: {checkpoint_path}")
            self.logger.info(f"📈 恢复到Episode: {self.current_episode}")
            
            return checkpoint_data
            
        except Exception as e:
            self.logger.error(f"❌ 加载检查点失败: {e}")
            return None
    
    def find_latest_checkpoint(self) -> Optional[str]:
        """查找最新的检查点"""
        try:
            checkpoints_dir = self.dirs["checkpoints"]
            if not checkpoints_dir.exists():
                return None
            
            checkpoints = []
            for checkpoint_dir in checkpoints_dir.iterdir():
                if checkpoint_dir.is_dir():
                    checkpoint_file = checkpoint_dir / "checkpoint.json"
                    if checkpoint_file.exists():
                        checkpoints.append(checkpoint_dir)
            
            if checkpoints:
                # 按修改时间排序，返回最新的
                latest = max(checkpoints, key=lambda x: x.stat().st_mtime)
                return str(latest)
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 查找检查点失败: {e}")
            return None
    
    def auto_save_checkpoint(self, episode: int, agent=None, save_interval: int = 50):
        """自动保存检查点"""
        if episode % save_interval == 0 and episode > 0:
            checkpoint_name = f"auto_save_ep{episode}"
            self.save_checkpoint(checkpoint_name, agent)
    
    def record_episode_data(self, episode: int, reward: float, length: int, 
                          violations: int, phase: str = "unknown"):
        """记录episode数据"""
        self.current_episode = episode
        
        # 记录到总数据
        self.training_data["episode_rewards"].append(reward)
        self.training_data["episode_lengths"].append(length)
        self.training_data["constraint_violations"].append(violations)
        
        # 记录到阶段数据
        success = reward > 1000  # 简化的成功判断
        if phase == "random":
            self.training_data["phase_data"]["random_phase"]["rewards"].append(reward)
            self.training_data["phase_data"]["random_phase"]["violations"].append(violations)
            if success:
                self.training_data["phase_data"]["random_phase"]["success_count"] += 1
        elif phase == "fixed":
            self.training_data["phase_data"]["fixed_phase"]["rewards"].append(reward)
            self.training_data["phase_data"]["fixed_phase"]["violations"].append(violations)
            if success:
                self.training_data["phase_data"]["fixed_phase"]["success_count"] += 1
    
    def save_final_results(self, additional_data: Dict = None):
        """保存最终结果"""
        try:
            # 计算最终性能指标
            rewards = self.training_data["episode_rewards"]
            violations = self.training_data["constraint_violations"]
            
            final_performance = {
                "avg_reward": np.mean(rewards[-20:]) if len(rewards) >= 20 else np.mean(rewards) if rewards else 0,
                "success_rate": sum(1 for r in rewards if r > 1000) / len(rewards) if rewards else 0,
                "total_violations": sum(violations),
                "total_episodes": len(rewards),
                "random_phase_success_rate": (
                    self.training_data["phase_data"]["random_phase"]["success_count"] / 
                    len(self.training_data["phase_data"]["random_phase"]["rewards"])
                    if self.training_data["phase_data"]["random_phase"]["rewards"] else 0
                ),
                "fixed_phase_success_rate": (
                    self.training_data["phase_data"]["fixed_phase"]["success_count"] / 
                    len(self.training_data["phase_data"]["fixed_phase"]["rewards"])
                    if self.training_data["phase_data"]["fixed_phase"]["rewards"] else 0
                )
            }
            
            # 完整结果
            results = {
                "experiment_info": {
                    "experiment_name": self.experiment_name,
                    "method_name": self.method_name,
                    "start_time": self.start_time,
                    "end_time": time.time(),
                    "duration": time.time() - self.start_time,
                    "interrupted": self.interrupted
                },
                "training_data": self.training_data,
                "final_performance": final_performance,
                "additional_data": additional_data or {}
            }
            
            # 保存JSON结果
            results_file = self.dirs["data"] / f"{self.method_name}_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self._make_json_serializable(results), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 最终结果已保存: {results_file}")
            
            # 生成文本报告
            self._generate_text_report(results)
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 保存最终结果失败: {e}")
            return None
    
    def _generate_text_report(self, results: Dict):
        """生成文本报告"""
        try:
            report_file = self.dirs["data"] / f"{self.method_name}_report.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"# {self.experiment_name} - {self.method_name} 训练报告\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 实验信息
                exp_info = results["experiment_info"]
                f.write("## 实验信息\n")
                f.write(f"- 实验名称: {exp_info['experiment_name']}\n")
                f.write(f"- 方法名称: {exp_info['method_name']}\n")
                f.write(f"- 训练时长: {exp_info['duration']/3600:.2f} 小时\n")
                f.write(f"- 是否中断: {'是' if exp_info['interrupted'] else '否'}\n\n")
                
                # 性能指标
                perf = results["final_performance"]
                f.write("## 性能指标\n")
                f.write(f"- 平均奖励: {perf['avg_reward']:.2f}\n")
                f.write(f"- 成功率: {perf['success_rate']:.2%}\n")
                f.write(f"- 总约束违反: {perf['total_violations']}\n")
                f.write(f"- 总Episodes: {perf['total_episodes']}\n")
                f.write(f"- 随机阶段成功率: {perf['random_phase_success_rate']:.2%}\n")
                f.write(f"- 固定阶段成功率: {perf['fixed_phase_success_rate']:.2%}\n\n")
                
                f.write("## 文件说明\n")
                f.write("- JSON结果: 包含完整的训练数据\n")
                f.write("- 检查点: 支持恢复训练\n")
                f.write("- 模型文件: 训练好的神经网络权重\n")
            
            self.logger.info(f"📄 文本报告已保存: {report_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 生成文本报告失败: {e}")
    
    def _make_json_serializable(self, obj):
        """使对象可JSON序列化"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        else:
            return obj
    
    def print_progress(self, episode: int, reward: float, phase: str = ""):
        """打印训练进度"""
        if episode % 10 == 0:
            avg_reward = np.mean(self.training_data["episode_rewards"][-10:]) if len(self.training_data["episode_rewards"]) >= 10 else reward
            phase_str = f" ({phase})" if phase else ""
            self.logger.info(f"Episode {episode}{phase_str}: 奖励 = {reward:.2f}, 平均奖励 = {avg_reward:.2f}")
    
    def get_training_summary(self) -> Dict:
        """获取训练摘要"""
        rewards = self.training_data["episode_rewards"]
        violations = self.training_data["constraint_violations"]
        
        return {
            "total_episodes": len(rewards),
            "avg_reward": np.mean(rewards) if rewards else 0,
            "best_reward": max(rewards) if rewards else 0,
            "success_rate": sum(1 for r in rewards if r > 1000) / len(rewards) if rewards else 0,
            "total_violations": sum(violations),
            "current_episode": self.current_episode
        }
