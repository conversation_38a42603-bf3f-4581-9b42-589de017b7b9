"""
完整系统测试
Complete System Test

测试整个论文实验系统是否正常工作
"""

import sys
import os
import time

# 添加路径
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")

    try:
        # 环境模块
        from environments.paper_environment import PaperSimulationEnvironment
        import environments.environment_configs as env_configs
        print("   ✅ 环境模块导入成功")

        # 算法模块
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        from algorithms.simplified_resband import EnhancedResBand
        from algorithms.simplified_td3 import SimplifiedTD3
        from algorithms.basic_mlacf import EnhancedMLACF
        print("   ✅ 算法模块导入成功")

        # 实验模块
        import experiments.experiment_configs as exp_configs
        from experiments.fast_training_protocol import get_protocol
        from experiments.statistical_analysis import run_statistical_analysis
        from experiments.performance_metrics import get_metrics_collector
        print("   ✅ 实验模块导入成功")

        return True

    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_creation():
    """测试环境创建"""
    print("\n🏗️ 测试环境创建...")
    
    try:
        # 测试三个阶段的环境
        stages = ["stage1_simple", "stage2_complex", "stage3_dynamic"]
        
        for stage in stages:
            env = PaperSimulationEnvironment(stage)
            state = env.reset()
            print(f"   ✅ {stage} 环境创建成功，状态维度: {len(state)}")
            
            # 测试一步动作
            action = [1.0, 0.0, 0.0]  # [a_T, a_N, μ]
            next_state, reward, done, info = env.step(action)
            print(f"      动作执行成功，奖励: {reward:.1f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 环境测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_integration():
    """测试算法集成"""
    print("\n🤖 测试算法集成...")
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        
        # 创建环境和框架
        env = PaperSimulationEnvironment("stage1_simple")
        framework = LightweightDWARL(env, {
            "dwa_enabled": True,
            "resband_enabled": True,
            "state_dim": 6,
            "action_dim": 3
        })
        
        print("   ✅ 框架创建成功")
        
        # 测试训练一个episode
        episode_info = framework.train_episode(max_steps=10, training_progress=0.0)
        print(f"   ✅ Episode训练成功，奖励: {episode_info['episode_reward']:.1f}")
        
        # 测试统计信息
        stats = framework.get_training_statistics()
        print(f"   ✅ 统计信息获取成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 算法集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_experiment_configs():
    """测试实验配置"""
    print("\n⚙️ 测试实验配置...")
    
    try:
        from experiments.experiment_configs import get_experiment_config, get_all_experiment_names
        
        # 测试获取所有实验名称
        exp_names = get_all_experiment_names()
        print(f"   ✅ 获取到 {len(exp_names)} 个实验配置")
        
        # 测试获取具体实验配置
        for exp_name in exp_names[:3]:  # 测试前3个
            config = get_experiment_config(exp_name)
            print(f"   ✅ {exp_name} 配置获取成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 实验配置测试失败: {e}")
        return False

def test_fast_training_protocol():
    """测试快速训练协议"""
    print("\n⚡ 测试快速训练协议...")
    
    try:
        from experiments.fast_training_protocol import get_protocol, start_fast_training
        
        protocol = get_protocol()
        print("   ✅ 协议获取成功")
        
        # 测试时间预算
        remaining = protocol.get_remaining_time()
        print(f"   ✅ 剩余时间: {remaining/3600:.1f}小时")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 快速训练协议测试失败: {e}")
        return False

def test_mini_experiment():
    """测试迷你实验"""
    print("\n🧪 执行迷你实验...")
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        from experiments.performance_metrics import get_metrics_collector
        
        # 创建环境和框架
        env = PaperSimulationEnvironment("stage1_simple")
        framework = LightweightDWARL(env, {
            "dwa_enabled": True,
            "resband_enabled": True
        })
        
        # 获取指标收集器
        collector = get_metrics_collector()
        collector.start_collection("mini_test")
        
        # 运行5个episodes
        total_reward = 0
        success_count = 0
        
        for episode in range(5):
            episode_info = framework.train_episode(max_steps=20, training_progress=episode/5)
            total_reward += episode_info["episode_reward"]
            if episode_info["success"]:
                success_count += 1
            
            # 记录指标
            collector.record_episode_metrics("mini_test", episode, {
                "episode_reward": episode_info["episode_reward"],
                "success": episode_info["success"]
            })
        
        # 完成指标收集
        final_results = {
            "success_rate": success_count / 5,
            "average_reward": total_reward / 5,
            "total_episodes": 5,
            "total_constraint_violations": 0
        }
        
        collector.finalize_experiment_metrics("mini_test", final_results)
        summary = collector.get_experiment_summary("mini_test")
        
        print(f"   ✅ 迷你实验完成")
        print(f"      成功率: {summary['success_rate']:.2f}")
        print(f"      平均奖励: {summary['average_reward']:.1f}")
        print(f"      训练时间: {summary['training_time_minutes']:.1f}分钟")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 迷你实验失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_statistical_analysis():
    """测试统计分析"""
    print("\n📊 测试统计分析...")
    
    try:
        from experiments.statistical_analysis import StatisticalAnalyzer
        
        # 创建模拟实验结果
        mock_results = {
            "experiment_2_resband_algorithm": {
                "resband": {
                    "performance": {
                        "success_rate": 0.92,
                        "average_reward": 1480.5,
                        "total_violations": 8
                    }
                },
                "fixed_coarse": {
                    "performance": {
                        "success_rate": 0.85,
                        "average_reward": 1250.3,
                        "total_violations": 28
                    }
                }
            }
        }
        
        analyzer = StatisticalAnalyzer()
        analysis = analyzer.analyze_experiment_results(mock_results)
        
        print("   ✅ 统计分析完成")
        print(f"      分析项目数: {len(analysis.get('significance_tests', {}))}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 统计分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 完整系统测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("环境创建", test_environment_creation),
        ("算法集成", test_algorithm_integration),
        ("实验配置", test_experiment_configs),
        ("快速训练协议", test_fast_training_protocol),
        ("迷你实验", test_mini_experiment),
        ("统计分析", test_statistical_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    total_time = time.time() - start_time
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"⏱️ 总耗时: {total_time:.1f}秒")
    
    if passed == total:
        print("🎉 系统完整性验证通过!")
        print("✅ 所有核心功能正常工作")
        print("✅ 可以安全执行完整实验")
        print("\n🚀 运行完整实验:")
        print("   python run_paper_experiments.py")
    else:
        print("❌ 部分测试失败，请检查错误信息")
        print("🔧 建议先修复失败的测试再运行完整实验")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
