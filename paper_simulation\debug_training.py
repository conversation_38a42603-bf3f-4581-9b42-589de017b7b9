#!/usr/bin/env python3
"""
调试训练过程
"""

def debug_single_episode():
    """调试单个episode"""
    print("🔍 调试单个episode训练过程")
    print("=" * 50)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        print(f"✅ 环境创建成功")
        
        # 创建框架
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        framework = LightweightDWARL(env, config)
        print("✅ 框架创建成功")
        
        # 手动训练一个episode
        print("\n🏋️ 开始手动训练episode...")
        state = env.reset()
        print(f"初始状态: {state}")
        
        total_reward = 0
        steps = 0
        done = False
        max_steps = 20  # 限制步数便于调试
        
        while not done and steps < max_steps:
            print(f"\n--- 步骤 {steps + 1} ---")
            print(f"当前状态: {state}")
            
            # 使用框架的train_step
            step_info = framework.train_step(state)
            
            next_state = step_info["next_state"]
            reward = step_info["reward"]
            done = step_info["done"]
            action = step_info["action"]
            
            print(f"动作: {action}")
            print(f"奖励: {reward:.3f}")
            print(f"下一状态: {next_state}")
            print(f"完成: {done}")
            
            state = next_state
            total_reward += reward
            steps += 1
        
        print(f"\n📊 Episode结果:")
        print(f"   总奖励: {total_reward:.3f}")
        print(f"   总步数: {steps}")
        print(f"   完成: {done}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_experiment_train_episode():
    """调试实验的_train_episode方法"""
    print("\n🧪 调试实验的_train_episode方法")
    print("=" * 50)
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        from environments.paper_environment import PaperSimulationEnvironment
        
        # 创建实验
        experiment = ResBandVerificationExperiment("debug_output")
        experiment.config["max_steps_per_episode"] = 20  # 限制步数
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage1_simple")
        agent = experiment._create_agent("resband_adaptive", env)
        
        print("✅ 实验和智能体创建成功")
        
        # 调用_train_episode
        print("\n🏋️ 调用_train_episode...")
        episode_info = experiment._train_episode(agent, env, 0, "resband_adaptive")
        
        print(f"\n📊 _train_episode结果:")
        print(f"   奖励: {episode_info['reward']:.3f}")
        print(f"   步数: {episode_info['steps']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 开始调试训练过程")
    
    # 调试单个episode
    success1 = debug_single_episode()
    
    # 调试实验方法
    success2 = debug_experiment_train_episode()
    
    if success1 and success2:
        print("\n✅ 调试完成，训练过程正常")
    else:
        print("\n❌ 发现问题，需要进一步修复")

if __name__ == "__main__":
    main()
