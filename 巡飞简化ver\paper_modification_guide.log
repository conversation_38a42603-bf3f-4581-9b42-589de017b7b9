This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.11.28)  3 SEP 2025 00:57
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**e:/basic_dwa_rl_framework_717ver/巡飞简化ver/paper_modification_guide.tex
(e:/basic_dwa_rl_framework_717ver/巡飞简化ver/paper_modification_guide.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(e:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(e:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctex.sty (e:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (e:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count196
\l__pdf_internal_box=\box51
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (e:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count197
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen141
\g__ctex_section_depth_int=\count198
\g__ctex_font_size_int=\count199
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)

Package ctex Warning: UTF8 will be used as the default encoding.

(e:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-pdftex.def
File: ctex-engine-pdftex.def 2022/07/14 v2.5.10 (pdf)LaTeX adapter (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJKutf8.sty
Package: CJKutf8 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)) (e:/texlive/2024/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.sty
Package: CJK 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/mule/MULEenc.sty
Package: MULEenc 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.enc
File: CJK.enc 2021/10/16 4.8.5
Now handling font encoding C00 ...
... no UTF-8 mapping file for font encoding C00
Now handling font encoding C05 ...
... no UTF-8 mapping file for font encoding C05
Now handling font encoding C09 ...
... no UTF-8 mapping file for font encoding C09
Now handling font encoding C10 ...
... no UTF-8 mapping file for font encoding C10
Now handling font encoding C20 ...
... no UTF-8 mapping file for font encoding C20
Now handling font encoding C19 ...
... no UTF-8 mapping file for font encoding C19
Now handling font encoding C40 ...
... no UTF-8 mapping file for font encoding C40
Now handling font encoding C42 ...
... no UTF-8 mapping file for font encoding C42
Now handling font encoding C43 ...
... no UTF-8 mapping file for font encoding C43
Now handling font encoding C50 ...
... no UTF-8 mapping file for font encoding C50
Now handling font encoding C52 ...
... no UTF-8 mapping file for font encoding C52
Now handling font encoding C49 ...
... no UTF-8 mapping file for font encoding C49
Now handling font encoding C60 ...
... no UTF-8 mapping file for font encoding C60
Now handling font encoding C61 ...
... no UTF-8 mapping file for font encoding C61
Now handling font encoding C63 ...
... no UTF-8 mapping file for font encoding C63
Now handling font encoding C64 ...
... no UTF-8 mapping file for font encoding C64
Now handling font encoding C65 ...
... no UTF-8 mapping file for font encoding C65
Now handling font encoding C70 ...
... no UTF-8 mapping file for font encoding C70
Now handling font encoding C31 ...
... no UTF-8 mapping file for font encoding C31
Now handling font encoding C32 ...
... no UTF-8 mapping file for font encoding C32
Now handling font encoding C33 ...
... no UTF-8 mapping file for font encoding C33
Now handling font encoding C34 ...
... no UTF-8 mapping file for font encoding C34
Now handling font encoding C35 ...
... no UTF-8 mapping file for font encoding C35
Now handling font encoding C36 ...
... no UTF-8 mapping file for font encoding C36
Now handling font encoding C37 ...
... no UTF-8 mapping file for font encoding C37
Now handling font encoding C80 ...
... no UTF-8 mapping file for font encoding C80
Now handling font encoding C81 ...
... no UTF-8 mapping file for font encoding C81
Now handling font encoding C01 ...
... no UTF-8 mapping file for font encoding C01
Now handling font encoding C11 ...
... no UTF-8 mapping file for font encoding C11
Now handling font encoding C21 ...
... no UTF-8 mapping file for font encoding C21
Now handling font encoding C41 ...
... no UTF-8 mapping file for font encoding C41
Now handling font encoding C62 ...
... no UTF-8 mapping file for font encoding C62
)
\CJK@indent=\box53
) (e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)) (e:/texlive/2024/texmf-dist/tex/latex/cjkpunct/CJKpunct.sty
Package: CJKpunct 2016/05/14 4.8.4
\CJKpunct@cnta=\count266
\CJKpunct@cntb=\count267
\CJKpunct@cntc=\count268
\CJKpunct@cntd=\count269
\CJKpunct@cnte=\count270
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2026 (decimal 8230)
 (e:/texlive/2024/texmf-dist/tex/latex/cjkpunct/CJKpunct.spa)) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJKspace.sty
Package: CJKspace 2021/10/16 3.8.0
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.bdg
File: UTF8.bdg 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexspa.def
File: ctexspa.def 2022/07/14 v2.5.10 Space info for CJKpunct (CTEX)
)
\ccwd=\dimen142
\l__ctex_ccglue_skip=\skip50
)
\l__ctex_ziju_dim=\dimen143
 (e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count271
\l__zhnum_tmp_int=\count272
 (e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
)) (e:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (e:/texlive/2024/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctex-c5size.clo
File: ctex-c5size.clo 2022/07/14 v2.5.10 c5size option (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)
)) (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen144
)) (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
) (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count273
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count274
\leftroot@=\count275
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count276
\DOTSCASE@=\count277
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count278
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count279
\dotsspace@=\muskip16
\c@parentequation=\count280
\dspbrk@lvl=\count281
\tag@help=\toks20
\row@=\count282
\column@=\count283
\maxfields@=\count284
\andhelp@=\toks21
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (e:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
) (e:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (e:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen153
\Gin@req@width=\dimen154
) (e:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (e:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count285
\float@exts=\toks25
\float@box=\box56
\@float@everytoks=\toks26
\@floatcapt=\box57
) (e:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks27
\c@algorithm=\count286
) (e:/texlive/2024/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 
 (e:/texlive/2024/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count287
\c@ALG@rem=\count288
\c@ALG@nested=\count289
\ALG@tlm=\skip54
\ALG@thistlm=\skip55
\c@ALG@Lnr=\count290
\c@ALG@blocknr=\count291
\c@ALG@storecount=\count292
\c@ALG@tmpcounter=\count293
\ALG@tmplength=\skip56
)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (e:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen155
\lightrulewidth=\dimen156
\cmidrulewidth=\dimen157
\belowrulesep=\dimen158
\belowbottomsep=\dimen159
\aboverulesep=\dimen160
\abovetopsep=\dimen161
\cmidrulesep=\dimen162
\cmidrulekern=\dimen163
\defaultaddspace=\dimen164
\@cmidla=\count294
\@cmidlb=\count295
\@aboverulesep=\dimen165
\@belowrulesep=\dimen166
\@thisruleclass=\count296
\@lastruleclass=\count297
\@thisrulewidth=\dimen167
) (e:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (e:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (e:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (e:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (e:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (e:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (e:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (e:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (e:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (e:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (e:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (e:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (e:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (e:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (e:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count298
) (e:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count299
)
\@linkdim=\dimen168
\Hy@linkcounter=\count300
\Hy@pagecounter=\count301
 (e:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (e:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count302
 (e:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `unicode' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count303
 (e:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen169
 (e:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (e:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count304
\Field@Width=\dimen170
\Fld@charsize=\dimen171
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (e:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count305
\c@Item=\count306
\c@Hfootnote=\count307
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (e:/texlive/2024/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-01-20 v7.01h Hyperref driver for pdfTeX
 (e:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count308
\c@bookmark@seq@number=\count309
 (e:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (e:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip57
) (e:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count310
\Gm@cntv=\count311
\c@Gm@tempcnt=\count312
\Gm@bindingoffset=\dimen172
\Gm@wd@mp=\dimen173
\Gm@odd@mp=\dimen174
\Gm@even@mp=\dimen175
\Gm@layoutwidth=\dimen176
\Gm@layoutheight=\dimen177
\Gm@layouthoffset=\dimen178
\Gm@layoutvoffset=\dimen179
\Gm@dimlist=\toks28
) (e:/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip58
\enit@outerparindent=\dimen180
\enit@toks=\toks29
\enit@inbox=\box58
\enit@count@id=\count313
\enitdp@description=\count314
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.enc
File: UTF8.enc 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.chr
File: UTF8.chr 2021/10/16 4.8.5
) (./paper_modification_guide.aux)
\openout1 = `paper_modification_guide.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C00/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C05/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C09/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C10/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C20/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C19/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C40/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C42/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C43/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C50/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C52/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C49/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C60/mj/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C61/mj/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C63/mj/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C64/mj/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C65/mj/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C70/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C31/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C32/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C33/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C34/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C35/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C36/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C37/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C80/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C81/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C01/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C11/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C21/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C41/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for C62/song/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 36.
LaTeX Font Info:    ... okay on input line 36.
 (e:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count315
\scratchdimen=\dimen181
\scratchbox=\box59
\nofMPsegments=\count316
\nofMParguments=\count317
\everyMPshowfont=\toks30
\MPscratchCnt=\count318
\MPscratchDim=\dimen182
\MPnumerator=\count319
\makeMPintoPDFobject=\count320
\everyMPtoPDFconversion=\toks31
) (e:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (e:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package hyperref Info: Link coloring OFF on input line 36.
 (./paper_modification_guide.out) (./paper_modification_guide.out)
\@outlinefile=\write3
\openout3 = `paper_modification_guide.out'.


*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for C70+rm on input line 39.
(e:/texlive/2024/texmf-dist/tex/latex/ctex/fd/c70rm.fd
File: c70rm.fd 2022/07/14 v2.5.10 Chinese font definition (CTEX)
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 39.
 (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 39.
 (e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Package CJKpunct Info: use punctuation spaces for family 'rm' with punctstyle (quanjiao) on input line 39.
LaTeX Font Info:    Trying to load font information for C70+zhfs on input line 66.
 (e:/texlive/2024/texmf-dist/tex/latex/zhmetrics/c70zhfs.fd
File: c70zhfs.fd 2009/09/23 4.8.2
)
Package CJKpunct Info: use punctuation spaces for family 'zhfs' with punctstyle (quanjiao) on input line 66.
 (e:/texlive/2024/texmf-dist/tex/generic/ctex/zhmap/ctex-zhmap-windows.tex
File: ctex-zhmap-windows.tex 2022/07/14 v2.5.10 Windows font map loader for pdfTeX and DVIPDFMx (CTEX)
{e:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{UGBK.sfd}{Unicode.sfd}) [1

{e:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}] [2]
Package hyperref Info: bookmark level for unknown algorithm defaults to 0 on input line 148.
 [3] [4] [5] [6] [7] [8] (./paper_modification_guide.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/07/14>
 ***********
Package rerunfilecheck Info: File `paper_modification_guide.out' has not changed.
(rerunfilecheck)             Checksum: 99F476BE483A8682F3D154B53D15CBB9;4439.
 ) 
Here is how much of TeX's memory you used:
 16620 strings out of 474116
 273637 string characters out of 5747716
 1949190 words of memory out of 5000000
 38250 multiletter control sequences out of 15000+600000
 634135 words of font info for 316 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 80i,7n,83p,785b,602s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simfang.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simhei.ttf><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><c:/Windows/Fonts/simsun.ttc><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex7.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi5.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy5.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmtt10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msam10.pfb><e:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb>
Output written on paper_modification_guide.pdf (8 pages, 735463 bytes).
PDF statistics:
 1009 PDF objects out of 1200 (max. 8388607)
 742 compressed objects within 8 object streams
 82 named destinations out of 1000 (max. 500000)
 609 words of extra memory for PDF output out of 10000 (max. 10000000)

