#!/usr/bin/env python3
"""
调试Episode过早终止问题
"""

def debug_episode_termination():
    """调试Episode终止条件"""
    print("🔍 调试Episode过早终止问题")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        print(f"✅ 环境和智能体创建成功")
        
        # 重置环境
        state = env.reset()
        print(f"\n📍 初始状态:")
        print(f"   位置: {state[:3]}")
        print(f"   速度: {state[3]:.2f}m/s")
        print(f"   航迹角: {state[4]:.3f}rad ({np.degrees(state[4]):.1f}°)")
        print(f"   偏航角: {state[5]:.3f}rad ({np.degrees(state[5]):.1f}°)")
        print(f"   目标位置: {env.target_pos}")
        print(f"   初始距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        print(f"   步数计数: {env.step_count}")
        
        # 检查初始终止条件
        print(f"\n🔍 检查初始终止条件:")
        done, info = env._check_termination()
        print(f"   初始done: {done}")
        print(f"   初始info: {info}")
        
        if done:
            print(f"   ❌ 环境在初始状态就被标记为完成！")
            
            # 详细检查各个终止条件
            current_pos = state[:3]
            distance_to_target = np.linalg.norm(current_pos - env.target_pos)
            print(f"   距离目标: {distance_to_target:.1f}m (阈值: 50m)")
            
            collision = env._check_collision()
            print(f"   碰撞检查: {collision}")
            
            max_steps = 1000  # FAST_TRAINING_CONFIG["max_steps_per_episode"]
            timeout = env.step_count >= max_steps
            print(f"   超时检查: {timeout} (步数: {env.step_count}/{max_steps})")
            
            stall = state[3] < env.physics_config["V_min"]
            print(f"   失速检查: {stall} (速度: {state[3]:.2f}m/s, 最小: {env.physics_config['V_min']}m/s)")
            
            return False
        
        # 执行一步动作
        print(f"\n🚀 执行一步动作:")
        step_info = agent.train_step(state, training_progress=0.1)
        
        next_state = step_info["next_state"]
        reward = step_info["reward"]
        done = step_info["done"]
        info = step_info["info"]
        action = step_info["action"]
        
        print(f"   动作: {action}")
        print(f"   新状态: {next_state}")
        print(f"   新位置: {next_state[:3]}")
        print(f"   移动距离: {np.linalg.norm(next_state[:3] - state[:3]):.3f}m")
        print(f"   新的目标距离: {np.linalg.norm(next_state[:3] - env.target_pos):.1f}m")
        print(f"   奖励: {reward:.3f}")
        print(f"   完成: {done}")
        print(f"   信息: {info}")
        print(f"   步数计数: {env.step_count}")
        
        if done:
            print(f"\n🔍 分析为什么Episode在第1步就结束:")
            
            # 详细检查各个终止条件
            current_pos = next_state[:3]
            distance_to_target = np.linalg.norm(current_pos - env.target_pos)
            print(f"   距离目标: {distance_to_target:.1f}m (阈值: 50m)")
            if distance_to_target < 50:
                print(f"   ✅ 到达目标！")
            
            collision = env._check_collision()
            print(f"   碰撞检查: {collision}")
            if collision:
                print(f"   💥 发生碰撞！")
            
            max_steps = 1000
            timeout = env.step_count >= max_steps
            print(f"   超时检查: {timeout} (步数: {env.step_count}/{max_steps})")
            if timeout:
                print(f"   ⏰ 超时！")
            
            stall = next_state[3] < env.physics_config["V_min"]
            print(f"   失速检查: {stall} (速度: {next_state[3]:.2f}m/s, 最小: {env.physics_config['V_min']}m/s)")
            if stall:
                print(f"   🛑 失速！")
            
            # 检查info中的具体原因
            if info.get('success'):
                print(f"   🎯 原因: 成功到达目标")
            elif info.get('collision'):
                print(f"   💥 原因: 碰撞")
            elif info.get('timeout'):
                print(f"   ⏰ 原因: 超时")
            elif info.get('stall'):
                print(f"   🛑 原因: 失速")
            else:
                print(f"   ❓ 原因: 未知")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_multiple_steps():
    """调试多步执行"""
    print("\n🎯 调试多步执行")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        
        # 重置环境
        state = env.reset()
        print(f"初始状态: {state[:3]}")
        print(f"目标位置: {env.target_pos}")
        print(f"初始距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        
        # 手动执行多步
        for step in range(5):
            print(f"\n--- 步骤 {step+1} ---")
            print(f"当前位置: {state[:3]}")
            print(f"当前步数: {env.step_count}")
            
            # 检查终止条件
            done, info = env._check_termination()
            print(f"终止检查: done={done}, info={info}")
            
            if done:
                print(f"Episode在步骤{step+1}终止")
                break
            
            # 执行简单动作（朝向目标）
            action = np.array([2.0, 5.0, 0.1])
            next_state, reward, done, info = env.step(action)
            
            movement = np.linalg.norm(next_state[:3] - state[:3])
            distance = np.linalg.norm(next_state[:3] - env.target_pos)
            
            print(f"动作: {action}")
            print(f"新位置: {next_state[:3]}")
            print(f"移动距离: {movement:.3f}m")
            print(f"目标距离: {distance:.1f}m")
            print(f"奖励: {reward:.3f}")
            print(f"完成: {done}")
            print(f"信息: {info}")
            
            state = next_state
            
            if done:
                print(f"Episode在步骤{step+1}结束")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 多步调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 Episode过早终止问题调试")
    
    # 调试单步
    single_success = debug_episode_termination()
    
    # 调试多步
    multi_success = debug_multiple_steps()
    
    if single_success and multi_success:
        print("\n" + "=" * 80)
        print("🎯 调试完成！")
        print("请查看上述输出，找出Episode过早终止的具体原因")
    else:
        print("\n❌ 调试失败")

if __name__ == "__main__":
    main()
