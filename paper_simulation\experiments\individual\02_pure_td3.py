#!/usr/bin/env python3
"""
独立实验2: 纯TD3算法训练
基线对比方法，不使用DWA安全约束的纯强化学习

运行方式:
1. 新训练: python 02_pure_td3.py
2. 恢复训练: python 02_pure_td3.py --resume
3. 从指定检查点恢复: python 02_pure_td3.py --resume --checkpoint /path/to/checkpoint

特点:
- 纯TD3强化学习，无DWA安全约束
- 用于对比验证DWA安全约束的有效性
- 预期会有更多约束违反
- 支持完整的中断恢复功能
"""

import os
import sys
import argparse
import numpy as np
import torch
import time
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from paper_simulation.experiments.utils.base_trainer import BaseTrainer

# 导入核心组件（已复制到本地）- 纯TD3只需要环境和TD3控制器
from paper_simulation.core import (
    LoiteringMunitionEnvironment,
    StabilizedTD3Controller,
    get_environment_config,
    get_td3_config,
    get_loitering_munition_config,
    get_training_config
)

class PureTD3_Trainer(BaseTrainer):
    """纯TD3训练器 - 基于巡飞简化ver的TD3框架"""

    def __init__(self):
        super().__init__("baseline_comparison", "pure_td3")

        # 设置随机种子
        np.random.seed(42)
        torch.manual_seed(42)

        # 训练配置
        self.config = {
            "random_episodes": 150,    # 随机场景探索
            "fixed_episodes": 100,     # 固定场景强化
            "total_episodes": 250,     # 总训练episodes
            "save_interval": 50,       # 检查点保存间隔
            "stage": 1,                # 训练阶段
            "use_dwa": False,          # 关键区别：不使用DWA安全约束
            "visualization_interval": 10
        }

        # 获取巡飞简化ver的配置
        self.td3_config = get_td3_config()
        self.lm_config = get_loitering_munition_config()
        self.training_config = get_training_config()

        # 性能统计
        self.training_results = {
            "start_time": datetime.now().isoformat(),
            "config": self.config,
            "stage_results": {}
        }

        self.logger.info("🎯 纯TD3训练器初始化完成")
        self.logger.info(f"📋 训练配置: {self.config}")
        self.logger.info("✅ 核心技术组件:")
        self.logger.info("  • LoiteringMunitionEnvironment: 真实的巡飞弹环境")
        self.logger.info("  • StabilizedTD3Controller: 纯TD3强化学习")
        self.logger.info("⚠️  注意: 不使用DWA安全约束，预期约束违反较多")
    
    def create_environment(self):
        """创建真实的巡飞弹训练环境"""
        self.logger.info("🌍 创建巡飞弹训练环境...")

        # 获取阶段配置（与DWA-TD3相同的环境，确保公平对比）
        stage_config = get_training_stage_config(self.config["stage"])
        env_config = get_environment_config(self.config["stage"])

        # 创建真实的巡飞弹环境
        env = LoiteringMunitionEnvironment(
            bounds=env_config['bounds'],
            obstacles=env_config['obstacles'],
            dynamic_obstacles=env_config['dynamic_obstacles'],
            goal=env_config['goal'],
            dt=self.lm_config['dt']
        )

        self.logger.info(f"✅ 巡飞弹环境创建完成")
        self.logger.info(f"  边界: {env_config['bounds']}")
        self.logger.info(f"  静态障碍物: {len(env_config['obstacles'])}个")
        self.logger.info(f"  动态障碍物: {len(env_config['dynamic_obstacles'])}个")
        self.logger.info(f"  目标位置: {env_config['goal']}")

        return env

    def create_agent(self):
        """创建纯TD3智能体（无DWA安全约束）"""
        self.logger.info("🤖 创建纯TD3智能体...")

        # 创建纯TD3控制器（不使用DWA安全约束）
        td3_controller = StabilizedTD3Controller(self.td3_config)

        self.logger.info(f"✅ 纯TD3智能体创建完成")
        self.logger.info(f"  状态维度: {self.td3_config['state_dim']}")
        self.logger.info(f"  动作维度: {self.td3_config['action_dim']}")
        self.logger.info(f"  网络结构: {self.td3_config['hidden_sizes']}")
        self.logger.info("⚠️  无DWA安全约束，动作直接来自神经网络输出")

        return td3_controller
    
    def run_random_phase(self, env, td3_controller, start_episode: int = 0):
        """运行随机场景探索阶段 - 纯TD3训练流程"""
        self.logger.info(f"🎲 开始随机场景探索阶段 ({self.config['random_episodes']} episodes)")

        scenario_candidates = []
        phase_results = {
            "episode_rewards": [],
            "episode_lengths": [],
            "constraint_violations": []
        }

        for episode in range(start_episode, self.config['random_episodes']):
            if self.interrupted:
                break

            # 重置环境
            state = env.reset()

            # 运行episode（纯TD3，无DWA安全约束）
            episode_reward = 0
            episode_length = 0
            violations = 0
            done = False

            while not done and episode_length < 1000:
                # 纯TD3直接输出动作（无安全约束）
                action = td3_controller.select_action(state)

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 记录约束违反（预期会比DWA-TD3多）
                if info.get('constraint_violation', False):
                    violations += 1

                # 存储经验
                td3_controller.store_transition(state, action, reward, next_state, done)

                # 更新网络
                if td3_controller.can_update():
                    td3_controller.update()

                state = next_state
                episode_reward += reward
                episode_length += 1

            # 记录episode结果
            phase_results["episode_rewards"].append(episode_reward)
            phase_results["episode_lengths"].append(episode_length)
            phase_results["constraint_violations"].append(violations)

            # 记录数据到基类
            self.record_episode_data(episode, episode_reward, episode_length, violations, "random")

            # 记录场景复杂度
            complexity_score = violations + episode_length / 1000.0
            scenario_candidates.append({
                "episode": episode,
                "complexity_score": complexity_score,
                "reward": episode_reward,
                "state_snapshot": env.get_state_snapshot() if hasattr(env, 'get_state_snapshot') else None
            })

            # 打印进度
            self.print_progress(episode, episode_reward, "随机探索")

            # 自动保存检查点
            self.auto_save_checkpoint(episode, td3_controller, self.config['save_interval'])

        # 选择最具挑战性的场景
        if scenario_candidates:
            scenario_candidates.sort(key=lambda x: x['complexity_score'], reverse=True)
            selected_scenario = scenario_candidates[0]
            self.logger.info(f"🎯 选择固定场景: Episode {selected_scenario['episode']} (复杂度: {selected_scenario['complexity_score']:.2f})")
        else:
            selected_scenario = None

        # 保存随机阶段结果
        self.training_results["stage_results"]["random_phase"] = phase_results

        return selected_scenario
    
    def run_fixed_phase(self, env, td3_controller, selected_scenario, start_episode: int = 0):
        """运行固定场景强化训练阶段 - 纯TD3训练流程"""
        self.logger.info(f"📌 开始固定场景强化训练 ({self.config['fixed_episodes']} episodes)")

        if selected_scenario and selected_scenario.get('state_snapshot'):
            self.logger.info(f"🎯 使用固定场景: Episode {selected_scenario['episode']}")
        else:
            self.logger.info("🎯 使用当前环境配置进行固定场景训练")

        start_episode = max(start_episode, self.config['random_episodes'])

        phase_results = {
            "episode_rewards": [],
            "episode_lengths": [],
            "constraint_violations": []
        }

        for episode in range(start_episode, self.config['total_episodes']):
            if self.interrupted:
                break

            # 重置环境到固定场景
            if selected_scenario and selected_scenario.get('state_snapshot'):
                state = env.reset_to_snapshot(selected_scenario['state_snapshot'])
            else:
                state = env.reset()

            # 运行episode（纯TD3，无DWA安全约束）
            episode_reward = 0
            episode_length = 0
            violations = 0
            done = False

            while not done and episode_length < 1000:
                # 纯TD3直接输出动作（无安全约束）
                action = td3_controller.select_action(state)

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 记录约束违反
                if info.get('constraint_violation', False):
                    violations += 1

                # 存储经验
                td3_controller.store_transition(state, action, reward, next_state, done)

                # 更新网络
                if td3_controller.can_update():
                    td3_controller.update()

                state = next_state
                episode_reward += reward
                episode_length += 1

            # 记录episode结果
            phase_results["episode_rewards"].append(episode_reward)
            phase_results["episode_lengths"].append(episode_length)
            phase_results["constraint_violations"].append(violations)

            # 记录数据到基类
            self.record_episode_data(episode, episode_reward, episode_length, violations, "fixed")

            # 打印进度
            self.print_progress(episode, episode_reward, "固定强化")

            # 自动保存检查点
            self.auto_save_checkpoint(episode, td3_controller, self.config['save_interval'])

        # 保存固定阶段结果
        self.training_results["stage_results"]["fixed_phase"] = phase_results
    
    def train(self, resume_from_checkpoint: str = None):
        """主训练函数 - 使用真实的纯TD3训练流程"""
        self.logger.info("🚀 开始纯TD3训练")

        # 创建环境和智能体
        env = self.create_environment()
        td3_controller = self.create_agent()

        # 恢复检查点（如果需要）
        start_episode = 0
        selected_scenario = None

        if resume_from_checkpoint:
            checkpoint_data = self.load_checkpoint(resume_from_checkpoint)
            if checkpoint_data:
                start_episode = checkpoint_data.get("current_episode", 0)
                selected_scenario = checkpoint_data.get("additional_data", {}).get("selected_scenario")

                # 恢复模型状态
                if "td3_state" in checkpoint_data.get("additional_data", {}):
                    td3_controller.load_state(checkpoint_data["additional_data"]["td3_state"])

                self.logger.info(f"🔄 从Episode {start_episode}恢复训练")

        try:
            # 阶段1: 随机场景探索
            if start_episode < self.config['random_episodes']:
                selected_scenario = self.run_random_phase(env, td3_controller, start_episode)

                # 保存阶段1完成的检查点
                if not self.interrupted:
                    checkpoint_data = {
                        "selected_scenario": selected_scenario,
                        "td3_state": td3_controller.get_state() if hasattr(td3_controller, 'get_state') else None
                    }
                    self.save_checkpoint("random_phase_complete", td3_controller, checkpoint_data)

            # 阶段2: 固定场景强化训练
            if not self.interrupted and start_episode < self.config['total_episodes']:
                self.run_fixed_phase(env, td3_controller, selected_scenario, start_episode)

            # 保存最终结果
            if not self.interrupted:
                self.logger.info("✅ 训练完成，保存最终结果...")

                # 收集纯TD3结果
                pure_td3_results = {
                    "config": self.config,
                    "training_results": self.training_results,
                    "selected_scenario": selected_scenario,
                    "td3_final_state": td3_controller.get_state() if hasattr(td3_controller, 'get_state') else None
                }

                results = self.save_final_results(pure_td3_results)

                # 打印训练摘要
                summary = self.get_training_summary()
                self.logger.info("📊 纯TD3训练摘要:")
                self.logger.info(f"  总Episodes: {summary['total_episodes']}")
                self.logger.info(f"  平均奖励: {summary['avg_reward']:.2f}")
                self.logger.info(f"  最佳奖励: {summary['best_reward']:.2f}")
                self.logger.info(f"  成功率: {summary['success_rate']:.2%}")
                self.logger.info(f"  总约束违反: {summary['total_violations']}")

                # 与DWA-TD3的对比提示
                self.logger.info("📋 对比分析:")
                self.logger.info("  • 预期约束违反比DWA-TD3多")
                self.logger.info("  • 预期平均奖励比DWA-TD3低")
                self.logger.info("  • 用于验证DWA安全约束的有效性")

                self.logger.info("🏗️ 算法特点:")
                self.logger.info(f"  DWA安全约束: ❌禁用")
                self.logger.info(f"  TD3强化学习: ✅启用")
                self.logger.info(f"  动作来源: 神经网络直接输出")

                self.logger.info(f"📁 结果保存在: {self.experiment_dir}")

                return results
            else:
                self.logger.info("⚠️  训练被中断")
                return None

        except Exception as e:
            self.logger.error(f"❌ 训练过程中发生错误: {e}")
            # 保存错误时的检查点
            self.save_checkpoint(f"error_ep{self.current_episode}", td3_controller)
            raise

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="纯TD3独立训练")
    
    parser.add_argument(
        "--resume", 
        action="store_true", 
        help="恢复训练"
    )
    
    parser.add_argument(
        "--checkpoint", 
        type=str, 
        help="指定检查点路径"
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    print("🎯 纯TD3独立训练")
    print("=" * 60)
    print("📋 实验说明:")
    print("  • 基线对比方法：纯TD3强化学习")
    print("  • 不使用DWA安全约束，用于对比验证")
    print("  • 预期约束违反较多，性能较DWA-TD3差")
    print("  • 分阶段训练：随机探索150 + 固定强化100 episodes")
    print("  • 支持Ctrl+C中断和恢复训练")
    print()
    print("🏗️ 技术特点:")
    print("  • LoiteringMunitionEnvironment: 真实的巡飞弹环境")
    print("  • StabilizedTD3Controller: 纯TD3强化学习")
    print("  • 无DWA安全约束: 动作直接来自神经网络")
    print("  • 用于验证DWA安全约束的必要性")
    print("=" * 60)
    
    args = parse_arguments()
    
    # 创建训练器
    trainer = PureTD3_Trainer()
    
    # 确定检查点路径
    checkpoint_path = None
    if args.resume:
        if args.checkpoint:
            checkpoint_path = args.checkpoint
        else:
            # 查找最新检查点
            checkpoint_path = trainer.find_latest_checkpoint()
            if checkpoint_path:
                print(f"🔍 找到最新检查点: {checkpoint_path}")
            else:
                print("❌ 没有找到检查点，将开始新训练")
    
    # 开始训练
    try:
        results = trainer.train(checkpoint_path)
        
        if results:
            print("\n🎉 纯TD3训练完成!")
            print(f"📁 结果保存在: {trainer.experiment_dir}")
            print("\n📊 验证结果:")
            print("  ✅ 纯TD3强化学习: 无安全约束的基线性能")
            print("  ⚠️ 约束违反统计: 用于对比DWA安全约束效果")
            print("  📈 学习曲线: 展示纯强化学习的收敛特性")
            print("\n🔗 下一步:")
            print("  1. 查看训练结果: data/pure_td3_results.json")
            print("  2. 与DWA-TD3结果对比分析")
            print("  3. 运行DWA-TD3: python 01_dwa_td3.py")
            print("  4. 运行分析脚本: python experiments/analysis/compare_baseline.py")
        else:
            print("\n⚠️  训练被中断")
            print(f"🔄 恢复命令: python {__file__} --resume")
            
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
