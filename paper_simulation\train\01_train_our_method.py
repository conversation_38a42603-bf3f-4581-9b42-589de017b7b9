"""
训练本文方法：DWA-RL + ResBand + MLACF 融合框架
Train Our Method: DWA-RL + ResBand + MLACF Integrated Framework

渐进式训练：简单场景 → 复杂场景 → 复杂动态场景
Progressive Training: Simple → Complex → Complex Dynamic Scenarios
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pickle

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from environments.paper_environment import PaperSimulationEnvironment
from algorithms.lightweight_dwa_rl import LightweightDWARL
from config.paths_config import PATHS

class OurMethodTrainer:
    """本文方法训练器"""
    
    def __init__(self, config=None, output_dir=None):
        """
        初始化训练器
        
        Args:
            config: 训练配置
            output_dir: 输出目录
        """
        # 默认配置
        default_config = {
            "total_episodes": 500,
            "max_steps_per_episode": 500,
            "save_interval": 50,
            "eval_interval": 25,
            "eval_episodes": 10,
            "learning_rate": 3e-4,
            "batch_size": 256,
            "buffer_size": 100000,
            "environment_stages": ["stage1_simple", "stage2_complex", "stage3_dynamic"],
            "progressive_training": True
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 创建输出目录
        if output_dir is None:
            output_dir = PATHS.get_training_dir("our_method")
        
        self.output_dir = PATHS.ensure_dir_exists(output_dir)
        
        # 训练统计
        self.training_stats = {
            "episode_rewards": [],
            "episode_lengths": [],
            "success_rates": [],
            "constraint_violations": [],
            "eval_results": [],
            "stage_stats": []
        }
        
        print(f"🎯 本文方法训练器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📋 训练配置: {self.config}")
    
    def train(self):
        """渐进式训练本文方法"""
        print("\n🚀 开始训练本文方法：DWA-RL + ResBand + MLACF")
        print("📈 渐进式训练模式：简单→复杂→动态")
        
        # 创建集成框架
        framework = self._create_framework()
        
        total_episodes = 0
        
        # 渐进式训练各阶段
        for stage_idx, stage in enumerate(self.config["environment_stages"]):
            print(f"\n🏗️ 阶段 {stage_idx + 1}/{len(self.config['environment_stages'])}: {stage}")
            
            # 为每个阶段分配episodes
            stage_episodes = self.config["total_episodes"] // len(self.config["environment_stages"])
            if stage_idx == len(self.config["environment_stages"]) - 1:
                stage_episodes = self.config["total_episodes"] - total_episodes
            
            stage_stats = self._train_stage(framework, stage, stage_episodes, total_episodes)
            total_episodes += stage_episodes
            
            # 保存阶段模型
            self.save_stage_model(framework, stage_idx + 1, stage_stats)
            
            print(f"   ✅ 阶段 {stage_idx + 1} 完成: {stage_episodes} episodes")
            print(f"   📊 阶段统计: 平均奖励={stage_stats['avg_reward']:.1f}, "
                  f"成功率={stage_stats['avg_success_rate']:.2%}")
        
        print(f"\n✅ 渐进式训练完成，总episodes: {total_episodes}")
        
        # 保存最终模型
        self.save_final_model(framework)
        
        # 保存训练统计
        self.save_training_stats()
        
        # 生成训练曲线
        self.plot_training_curves()
        
        return self.training_stats
    
    def _create_framework(self):
        """创建DWA-RL集成框架"""
        # 创建一个示例环境用于获取状态和动作维度
        env = PaperSimulationEnvironment("stage1_simple")
        
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True,
            "mlacf_enabled": True,
            "learning_rate": self.config["learning_rate"],
            "batch_size": self.config["batch_size"],
            "buffer_size": self.config["buffer_size"]
        }
        
        framework = LightweightDWARL(env, config)
        
        print(f"🤖 集成框架创建完成:")
        print(f"   • DWA-RL分层控制架构: ✅")
        print(f"   • ResBand自适应分辨率: ✅")
        print(f"   • MLACF元学习约束: ✅")
        
        return framework
    
    def _train_stage(self, framework, stage, episodes, episode_offset):
        """训练单个阶段"""
        # 创建该阶段的环境
        env = PaperSimulationEnvironment(stage)
        
        stage_stats = {
            "stage": stage,
            "episodes": episodes,
            "rewards": [],
            "lengths": [],
            "violations": [],
            "success_rates": [],
            "avg_reward": 0,
            "avg_length": 0,
            "avg_violations": 0,
            "avg_success_rate": 0
        }
        
        for episode in range(episodes):
            episode_reward = 0
            episode_length = 0
            constraint_violations = 0
            
            # 重置环境
            state = env.reset()
            done = False
            
            # 计算训练进度
            training_progress = (episode_offset + episode + 1) / self.config["total_episodes"]
            
            while not done and episode_length < self.config["max_steps_per_episode"]:
                # 使用集成框架进行训练步骤
                step_info = framework.train_step(state, training_progress=training_progress)
                
                next_state = step_info["next_state"]
                reward = step_info["reward"]
                done = step_info["done"]
                info = step_info.get("info", {})
                
                # 检查约束违反
                if info.get("collision", False) or info.get("boundary_violation", False):
                    constraint_violations += 1
                
                state = next_state
                episode_reward += reward
                episode_length += 1
            
            # 记录统计
            self.training_stats["episode_rewards"].append(episode_reward)
            self.training_stats["episode_lengths"].append(episode_length)
            self.training_stats["constraint_violations"].append(constraint_violations)
            
            stage_stats["rewards"].append(episode_reward)
            stage_stats["lengths"].append(episode_length)
            stage_stats["violations"].append(constraint_violations)
            
            # 定期评估
            global_episode = episode_offset + episode + 1
            if (episode + 1) % self.config["eval_interval"] == 0:
                eval_result = self._evaluate_framework(framework, env, global_episode)
                self.training_stats["eval_results"].append(eval_result)
                stage_stats["success_rates"].append(eval_result["success_rate"])
                
                print(f"   Episode {episode + 1}/{episodes} (全局{global_episode}): "
                      f"奖励={episode_reward:.1f}, "
                      f"长度={episode_length}, "
                      f"成功率={eval_result['success_rate']:.2%}, "
                      f"违反={constraint_violations}")
            
            # 定期保存
            if (episode + 1) % self.config["save_interval"] == 0:
                self.save_checkpoint(framework, global_episode)
        
        # 计算阶段统计
        if stage_stats["rewards"]:
            stage_stats["avg_reward"] = np.mean(stage_stats["rewards"])
            stage_stats["avg_length"] = np.mean(stage_stats["lengths"])
            stage_stats["avg_violations"] = np.mean(stage_stats["violations"])
            
        if stage_stats["success_rates"]:
            stage_stats["avg_success_rate"] = np.mean(stage_stats["success_rates"])
        
        self.training_stats["stage_stats"].append(stage_stats)
        
        return stage_stats
    
    def _evaluate_framework(self, framework, env, episode):
        """评估集成框架"""
        successes = 0
        total_reward = 0
        total_violations = 0
        
        for _ in range(self.config["eval_episodes"]):
            state = env.reset()
            done = False
            steps = 0
            episode_reward = 0
            violations = 0
            
            while not done and steps < self.config["max_steps_per_episode"]:
                step_info = framework.evaluate_step(state)
                next_state = step_info["next_state"]
                reward = step_info["reward"]
                done = step_info["done"]
                info = step_info.get("info", {})
                
                if info.get("collision", False) or info.get("boundary_violation", False):
                    violations += 1
                
                state = next_state
                episode_reward += reward
                steps += 1
            
            if info.get("success", False):
                successes += 1
            
            total_reward += episode_reward
            total_violations += violations
        
        return {
            "episode": episode,
            "success_rate": successes / self.config["eval_episodes"],
            "avg_reward": total_reward / self.config["eval_episodes"],
            "avg_violations": total_violations / self.config["eval_episodes"]
        }
    
    def save_stage_model(self, framework, stage_num, stage_stats):
        """保存阶段模型"""
        model_path = PATHS.get_model_save_path(self.output_dir, "checkpoint", f"stage_{stage_num}")
        
        model_data = {
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "stage_stats": stage_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat(),
            "model_type": f"stage_{stage_num}"
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"   💾 保存阶段模型: {model_path}")
    
    def save_final_model(self, framework):
        """保存最终训练模型"""
        model_path = PATHS.get_model_save_path(self.output_dir, "final")
        
        model_data = {
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat(),
            "model_type": "final"
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"💾 保存最终模型: {model_path}")
    
    def save_checkpoint(self, framework, episode):
        """保存训练检查点"""
        checkpoint_path = PATHS.get_model_save_path(self.output_dir, "checkpoint", episode)
        
        checkpoint_data = {
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "episode": episode,
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)
    
    def save_training_stats(self):
        """保存训练统计数据"""
        stats_path = PATHS.get_stats_save_path(self.output_dir)
        
        with open(stats_path, 'w') as f:
            json.dump(self.training_stats, f, indent=2, default=str)
        
        config_path = PATHS.get_config_save_path(self.output_dir)
        
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        print(f"📊 保存训练统计: {stats_path}")
        print(f"⚙️ 保存训练配置: {config_path}")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 奖励曲线
        axes[0, 0].plot(self.training_stats["episode_rewards"])
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].grid(True)
        
        # 成功率曲线
        if self.training_stats["eval_results"]:
            eval_episodes = [r["episode"] for r in self.training_stats["eval_results"]]
            success_rates = [r["success_rate"] for r in self.training_stats["eval_results"]]
            axes[0, 1].plot(eval_episodes, success_rates, 'o-')
            axes[0, 1].set_title('Success Rate')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Success Rate')
            axes[0, 1].grid(True)
        
        # Episode长度
        axes[1, 0].plot(self.training_stats["episode_lengths"])
        axes[1, 0].set_title('Episode Lengths')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Length')
        axes[1, 0].grid(True)
        
        # 约束违反
        axes[1, 1].plot(self.training_stats["constraint_violations"])
        axes[1, 1].set_title('Constraint Violations')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Violations')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        curves_path = os.path.join(self.output_dir, "training_curves.png")
        plt.savefig(curves_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📈 保存训练曲线: {curves_path}")

def main():
    """主函数"""
    print("🎯 训练本文方法：DWA-RL + ResBand + MLACF 融合框架")
    
    # 创建训练器
    trainer = OurMethodTrainer()
    
    # 开始训练
    results = trainer.train()
    
    print(f"\n🎉 本文方法训练完成！")
    print(f"📊 最终统计:")
    print(f"   总episodes: {len(results['episode_rewards'])}")
    print(f"   平均奖励: {np.mean(results['episode_rewards']):.1f}")
    if results["eval_results"]:
        final_success_rate = results["eval_results"][-1]["success_rate"]
        print(f"   最终成功率: {final_success_rate:.2%}")

if __name__ == "__main__":
    main()
