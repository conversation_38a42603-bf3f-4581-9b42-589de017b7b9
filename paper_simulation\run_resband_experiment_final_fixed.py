#!/usr/bin/env python3
"""
运行修复后的ResBand验证实验
- 每个episode最大2000步
- 每个阶段200个episodes
- 完整的分辨率选择可视化
"""

import os
import sys

# 添加路径
sys.path.append(os.path.dirname(__file__))

def main():
    """运行ResBand验证实验"""
    print("🎰 启动修复后的ResBand验证实验")
    print("=" * 60)
    print("📋 实验配置:")
    print("   - 每个Episode最大步数: 2000")
    print("   - 每个阶段Episodes: 200")
    print("   - 总阶段数: 3 (简单→复杂→动态)")
    print("   - 对比方法: 4种")
    print("   - 分辨率选择可视化: 启用")
    print("   - 预计时间: 2-3小时")
    
    # 确认开始
    response = input("\n是否开始实验? (y/n): ")
    if response.lower() != 'y':
        print("实验已取消")
        return
    
    try:
        # 导入实验类
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建实验
        experiment = ResBandVerificationExperiment()
        
        print(f"\n🚀 开始ResBand验证实验...")
        print(f"📁 结果将保存到: {experiment.output_dir}")
        
        # 运行实验
        results = experiment.run_experiment()
        
        print(f"\n🎉 实验完成!")
        print(f"📊 结果摘要:")
        
        # 显示关键结果
        if "statistical_tests" in results:
            analysis = results["statistical_tests"]
            
            if "performance_ranking" in analysis:
                print("   性能排名:")
                for i, (method, score) in enumerate(analysis["performance_ranking"][:3]):
                    print(f"     {i+1}. {method}: {score:.3f}")
        
        # 显示分辨率选择统计
        if "resband_adaptive" in results.get("convergence_analysis", {}):
            resband_data = results["convergence_analysis"]["resband_adaptive"]
            if "resolution_selections" in resband_data:
                selections = resband_data["resolution_selections"]
                from collections import Counter
                selection_counts = Counter(selections)
                
                print(f"\n📊 ResBand分辨率选择统计:")
                print(f"   总Episodes: {len(selections)}")
                for arm, count in selection_counts.items():
                    arm_name = {0: '粗分辨率', 1: '中等分辨率', 2: '精细分辨率'}[arm]
                    ratio = count / len(selections)
                    print(f"   {arm_name}: {count}次 ({ratio:.1%})")
        
        print(f"\n📁 详细结果已保存到: {experiment.output_dir}")
        print(f"📈 可视化图表:")
        print(f"   - resband_convergence_analysis.png (收敛分析)")
        print(f"   - resband_resolution_selection.png (分辨率选择可视化)")
        print(f"   - performance_comparison_table.csv (性能对比表)")
        
        return results
        
    except Exception as e:
        print(f"❌ 实验失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
