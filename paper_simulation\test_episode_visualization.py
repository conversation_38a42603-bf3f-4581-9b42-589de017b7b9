#!/usr/bin/env python3
"""
清晰展示Episode概念和轨迹可视化
一个Episode = 从起点到终点的完整路径（包含很多steps）
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def test_single_episode():
    """测试单个episode，清晰展示概念"""
    print("🎯 测试单个Episode - 清晰展示概念")
    print("=" * 60)
    
    try:
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        from environments.paper_environment import PaperSimulationEnvironment
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment('stage3_dynamic')
        config = {
            'state_dim': 6,
            'action_dim': 3,
            'dwa_enabled': True,
            'resband_enabled': True,
            'mlacf_enabled': True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = 'resband_adaptive'
        
        print(f"✅ 环境和智能体创建成功")
        
        # 开始一个完整的Episode
        episode_num = 1
        print(f"\n🚀 开始Episode {episode_num}")
        print("=" * 40)
        
        # 重置环境
        state = env.reset()
        start_pos = state[:3].copy()
        target_pos = env.target_pos.copy()
        
        print(f"起点: {start_pos}")
        print(f"终点: {target_pos}")
        initial_distance = np.linalg.norm(target_pos - start_pos)
        print(f"初始距离: {initial_distance:.1f}米")
        
        # 记录轨迹
        trajectory = [start_pos]
        rewards = []
        actions = []
        
        total_reward = 0
        step = 0
        max_steps = 500  # 限制最大步数以便观察
        
        print(f"\n📍 Episode {episode_num} 进行中:")
        print("步骤 | 位置 | 距目标 | 移动 | 动作 | 奖励 | 状态")
        print("-" * 80)
        
        while step < max_steps:
            # 执行一步
            step_info = agent.train_step(state, training_progress=step/1000)
            
            next_state = step_info['next_state']
            action = step_info['action']
            reward = step_info['reward']
            done = step_info['done']
            info = step_info.get('info', {})
            
            # 计算移动距离
            movement = np.linalg.norm(next_state[:3] - state[:3])
            distance_to_goal = np.linalg.norm(target_pos - next_state[:3])
            
            # 记录数据
            trajectory.append(next_state[:3].copy())
            rewards.append(reward)
            actions.append(action.copy())
            total_reward += reward
            step += 1
            
            # 打印进度（每5步或重要事件）
            if step % 5 == 0 or done or step <= 10:
                status = "正常"
                if done:
                    if info.get('success'):
                        status = "🎉成功"
                    elif info.get('collision'):
                        status = "💥碰撞"
                    elif info.get('timeout'):
                        status = "⏰超时"
                    else:
                        status = "结束"
                
                print(f"{step:4d} | {next_state[:3]} | {distance_to_goal:6.1f} | {movement:4.1f} | {action} | {reward:6.2f} | {status}")
            
            # 检查Episode结束条件
            if done:
                print(f"\n🏁 Episode {episode_num} 结束!")
                print(f"   结束原因: {info}")
                print(f"   总步数: {step}")
                print(f"   总奖励: {total_reward:.2f}")
                print(f"   最终距离: {distance_to_goal:.1f}米")
                
                total_movement = sum(np.linalg.norm(trajectory[i] - trajectory[i-1]) 
                                   for i in range(1, len(trajectory)))
                print(f"   总移动距离: {total_movement:.1f}米")
                print(f"   平均每步移动: {total_movement/step:.1f}米")
                
                if info.get('success'):
                    print(f"   ✅ 成功到达目标!")
                elif info.get('collision'):
                    print(f"   ❌ 发生碰撞")
                else:
                    print(f"   ⚠️ 其他原因结束")
                
                break
            
            state = next_state
        
        if step >= max_steps:
            print(f"\n⏰ Episode {episode_num} 达到最大步数限制 ({max_steps})")
            distance_to_goal = np.linalg.norm(target_pos - state[:3])
            print(f"   最终距离: {distance_to_goal:.1f}米")
        
        # 可视化轨迹
        visualize_trajectory(trajectory, start_pos, target_pos, episode_num)
        
        return {
            'episode': episode_num,
            'steps': step,
            'total_reward': total_reward,
            'success': info.get('success', False) if 'info' in locals() else False,
            'trajectory': trajectory
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def visualize_trajectory(trajectory, start_pos, target_pos, episode_num):
    """可视化轨迹"""
    print(f"\n📊 生成Episode {episode_num}轨迹图...")
    
    try:
        trajectory = np.array(trajectory)
        
        # 创建3D图
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # 绘制轨迹
        ax.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
                'b-', linewidth=2, label='轨迹')
        
        # 标记起点和终点
        ax.scatter(*start_pos, color='green', s=100, label='起点')
        ax.scatter(*target_pos, color='red', s=100, label='终点')
        
        # 标记轨迹点
        ax.scatter(trajectory[::10, 0], trajectory[::10, 1], trajectory[::10, 2], 
                  color='blue', s=20, alpha=0.6)
        
        # 设置标签和标题
        ax.set_xlabel('X (米)')
        ax.set_ylabel('Y (米)')
        ax.set_zlabel('Z (米)')
        ax.set_title(f'Episode {episode_num} 轨迹 ({len(trajectory)}步)')
        ax.legend()
        
        # 保存图片
        filename = f'episode_{episode_num}_trajectory.png'
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"   轨迹图已保存: {filename}")
        
        # 显示统计信息
        total_distance = sum(np.linalg.norm(trajectory[i] - trajectory[i-1]) 
                           for i in range(1, len(trajectory)))
        final_distance = np.linalg.norm(target_pos - trajectory[-1])
        
        print(f"   轨迹统计:")
        print(f"     总步数: {len(trajectory)}")
        print(f"     总移动距离: {total_distance:.1f}米")
        print(f"     最终距目标: {final_distance:.1f}米")
        print(f"     平均每步: {total_distance/(len(trajectory)-1):.1f}米")
        
        plt.close()
        
    except Exception as e:
        print(f"   ⚠️ 可视化失败: {e}")

def test_multiple_episodes():
    """测试多个episodes"""
    print(f"\n🎯 测试多个Episodes")
    print("=" * 60)
    
    results = []
    for episode in range(3):
        print(f"\n{'='*20} Episode {episode+1} {'='*20}")
        result = test_single_episode()
        if result:
            results.append(result)
    
    # 汇总统计
    if results:
        print(f"\n📊 多Episode统计:")
        print("Episode | 步数 | 奖励 | 成功")
        print("-" * 30)
        for r in results:
            success_mark = "✅" if r['success'] else "❌"
            print(f"{r['episode']:7d} | {r['steps']:4d} | {r['total_reward']:6.1f} | {success_mark}")

def main():
    """主函数"""
    print("🎯 Episode概念清晰展示")
    print("一个Episode = 从起点到终点的完整路径（包含很多steps）")
    print("一个Step = 智能体执行一个动作，移动一小步（2-5米）")
    
    # 测试单个episode
    test_single_episode()
    
    print(f"\n" + "="*80)
    print("🎉 Episode概念展示完成！")
    print("现在您可以清楚地看到：")
    print("- 一个Episode包含很多steps")
    print("- 每个step移动2-5米")
    print("- Episode直到到达目标、碰撞或超时才结束")

if __name__ == "__main__":
    main()
