#!/usr/bin/env python3
"""
检查实验依赖和配置
"""

import sys
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} (满足要求)")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (需要Python 3.7+)")
        return False

def check_required_packages():
    """检查必需的包"""
    print("\n📦 检查必需包...")
    
    required_packages = [
        ("numpy", "科学计算"),
        ("scipy", "统计分析"),
        ("json", "数据序列化"),
        ("logging", "日志记录"),
        ("pathlib", "路径处理"),
        ("time", "时间处理"),
        ("typing", "类型提示")
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少包: {', '.join(missing_packages)}")
        print("安装命令: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("\n📁 检查项目结构...")
    
    required_files = [
        "paper_simulation/experiments/experiment_configs.py",
        "paper_simulation/experiments/experiment_runner.py", 
        "paper_simulation/experiments/fast_training_protocol.py",
        "paper_simulation/experiments/performance_metrics.py",
        "paper_simulation/analysis/statistical_analysis.py",
        "paper_simulation/run_simplified_experiments.py"
    ]
    
    required_dirs = [
        "paper_simulation/core",
        "paper_simulation/experiments", 
        "paper_simulation/analysis"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查目录
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            print(f"❌ 目录: {dir_path}")
            missing_dirs.append(dir_path)
        else:
            print(f"✅ 目录: {dir_path}")
    
    # 检查文件
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 文件: {file_path}")
            missing_files.append(file_path)
        else:
            print(f"✅ 文件: {file_path}")
    
    if missing_dirs or missing_files:
        print(f"\n⚠️  缺少项目文件/目录")
        if missing_dirs:
            print(f"缺少目录: {missing_dirs}")
        if missing_files:
            print(f"缺少文件: {missing_files}")
        return False
    
    return True

def check_core_modules():
    """检查核心模块是否可导入"""
    print("\n🔧 检查核心模块...")
    
    core_modules = [
        ("paper_simulation.experiments.experiment_configs", "实验配置"),
        ("paper_simulation.experiments.fast_training_protocol", "快速训练协议"),
        ("paper_simulation.experiments.performance_metrics", "性能指标"),
        ("paper_simulation.analysis.statistical_analysis", "统计分析")
    ]
    
    import_errors = []
    
    for module_name, description in core_modules:
        try:
            importlib.import_module(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError as e:
            print(f"❌ {module_name} - {description} (导入失败: {e})")
            import_errors.append((module_name, str(e)))
    
    if import_errors:
        print(f"\n⚠️  模块导入错误:")
        for module, error in import_errors:
            print(f"  {module}: {error}")
        return False
    
    return True

def check_experiment_configs():
    """检查实验配置"""
    print("\n⚙️  检查实验配置...")
    
    try:
        from paper_simulation.experiments.experiment_configs import (
            MAIN_EXPERIMENTS, 
            COMPLEXITY_VALIDATION_EXPERIMENTS,
            FAST_TRAINING_PROTOCOL
        )
        
        print(f"✅ 主要实验配置: {len(MAIN_EXPERIMENTS)} 个实验")
        print(f"✅ 复杂度验证配置: {len(COMPLEXITY_VALIDATION_EXPERIMENTS)} 个实验")
        print(f"✅ 快速训练协议: 已配置")
        
        # 检查关键实验是否存在
        key_experiments = [
            "experiment_1_safety_verification",
            "experiment_2_resband_algorithm", 
            "experiment_3_mlacf_framework"
        ]
        
        for exp_name in key_experiments:
            if exp_name in MAIN_EXPERIMENTS:
                print(f"✅ 关键实验: {exp_name}")
            else:
                print(f"❌ 缺少关键实验: {exp_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def create_missing_directories():
    """创建缺失的目录"""
    print("\n📁 创建必要目录...")
    
    dirs_to_create = [
        "paper_simulation/results",
        "paper_simulation/logs", 
        "paper_simulation/checkpoints",
        "paper_simulation/figures"
    ]
    
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")

def main():
    """主检查函数"""
    print("🔍 实验依赖检查")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("必需包", check_required_packages),
        ("项目结构", check_project_structure),
        ("核心模块", check_core_modules),
        ("实验配置", check_experiment_configs)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            all_passed = False
    
    # 创建必要目录
    create_missing_directories()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过! 可以运行实验")
        print("\n🚀 运行实验:")
        print("  python paper_simulation/run_all.py")
        print("  或")
        print("  python paper_simulation/run_simplified_experiments.py")
    else:
        print("❌ 检查未通过，请修复上述问题后重试")
        print("\n🔧 常见解决方案:")
        print("  1. 安装缺失的包: pip install numpy scipy")
        print("  2. 检查项目文件完整性")
        print("  3. 确保Python版本 >= 3.7")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
