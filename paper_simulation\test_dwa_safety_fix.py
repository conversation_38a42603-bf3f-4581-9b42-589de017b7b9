#!/usr/bin/env python3
"""
测试DWA安全检查修复
验证DWA能够生成安全动作
"""

def test_dwa_safety_check():
    """测试DWA安全检查"""
    print("🛡️ 测试DWA安全检查修复")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        print(f"✅ 环境和智能体创建成功")
        
        # 重置环境
        state = env.reset()
        print(f"\n📍 环境状态:")
        print(f"   起点: {state[:3]}")
        print(f"   终点: {env.target_pos}")
        print(f"   距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        print(f"   静态障碍物: {len(env.static_obstacles)}个")
        print(f"   动态障碍物: {len(env.dynamic_obstacles)}个")
        
        # 测试不同分辨率配置的安全动作生成
        resolution_configs = [
            {"arm_id": 0, "a_T": 8.0, "a_N": 25.0, "mu": 1.0, "name": "粗分辨率"},
            {"arm_id": 1, "a_T": 4.0, "a_N": 15.0, "mu": 0.5, "name": "中等分辨率"},
            {"arm_id": 2, "a_T": 2.0, "a_N": 8.0, "mu": 0.3, "name": "精细分辨率"}
        ]
        
        print(f"\n🎯 测试不同分辨率的安全动作生成:")
        
        for config_test in resolution_configs:
            safe_actions = agent._generate_safe_actions_with_dwa(state, config_test)
            print(f"   {config_test['name']}: {len(safe_actions)}个安全动作")
            
            if len(safe_actions) > 0:
                print(f"     ✅ 成功生成安全动作")
                # 显示前3个安全动作
                for i, action in enumerate(safe_actions[:3]):
                    print(f"       动作{i+1}: a_T={action[0]:.2f}, a_N={action[1]:.2f}, μ={action[2]:.2f}")
            else:
                print(f"     ❌ 没有生成安全动作")
        
        # 测试单个动作的安全性检查
        print(f"\n🔍 测试单个动作的安全性检查:")
        
        test_actions = [
            np.array([0.0, 0.0, 0.0]),    # 静止
            np.array([2.0, 5.0, 0.1]),    # 朝向目标的动作
            np.array([4.0, 10.0, 0.2]),   # 中等强度动作
            np.array([8.0, 25.0, 0.5]),   # 较强动作
            np.array([10.0, 50.0, 2.0]),  # 超出约束的动作
        ]
        
        for i, action in enumerate(test_actions):
            is_safe = agent._is_action_safe(state, action)
            print(f"   动作{i+1} {action}: {'✅ 安全' if is_safe else '❌ 不安全'}")
        
        # 测试边界检查
        print(f"\n🚧 测试边界检查:")
        
        # 创建接近边界的状态
        boundary_states = [
            np.array([100.0, 100.0, 100.0, 25.0, 0.0, 0.0]),    # 接近下边界
            np.array([1900.0, 1900.0, 1900.0, 25.0, 0.0, 0.0]), # 接近上边界
            np.array([1000.0, 1000.0, 1000.0, 25.0, 0.0, 0.0]), # 中心位置
        ]
        
        test_action = np.array([2.0, 5.0, 0.1])
        
        for i, test_state in enumerate(boundary_states):
            is_safe = agent._is_action_safe(test_state, test_action)
            print(f"   位置{i+1} {test_state[:3]}: {'✅ 安全' if is_safe else '❌ 不安全'}")
        
        return True
        
    except Exception as e:
        print(f"❌ DWA安全检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_dwa_rl_step():
    """测试完整的DWA-RL步骤"""
    print("\n🚀 测试完整的DWA-RL步骤")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        # 重置环境
        state = env.reset()
        print(f"初始状态: {state[:3]}")
        print(f"目标位置: {env.target_pos}")
        print(f"初始距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        
        # 执行多个DWA-RL步骤
        total_reward = 0
        steps = 0
        done = False
        max_steps = 10
        
        while not done and steps < max_steps:
            step_info = agent.train_step(state, training_progress=steps/max_steps)
            
            next_state = step_info["next_state"]
            reward = step_info["reward"]
            done = step_info["done"]
            safe_actions_count = step_info["safe_actions_count"]
            action = step_info["action"]
            
            # 计算移动距离
            movement = np.linalg.norm(next_state[:3] - state[:3])
            distance_to_goal = np.linalg.norm(next_state[:3] - env.target_pos)
            
            print(f"步骤 {steps+1}:")
            print(f"   安全动作数: {safe_actions_count}")
            print(f"   选择动作: {action}")
            print(f"   新位置: {next_state[:3]}")
            print(f"   移动距离: {movement:.3f}m")
            print(f"   目标距离: {distance_to_goal:.1f}m")
            print(f"   奖励: {reward:.3f}")
            print(f"   完成: {done}")
            
            if safe_actions_count == 0:
                print(f"   ⚠️ 警告：没有安全动作！")
                break
            
            state = next_state
            total_reward += reward
            steps += 1
        
        print(f"\n📊 测试结果:")
        print(f"   总步数: {steps}")
        print(f"   总奖励: {total_reward:.2f}")
        print(f"   最终距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        print(f"   成功运行: {steps > 0}")
        
        return steps > 0
        
    except Exception as e:
        print(f"❌ 完整DWA-RL步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 DWA安全检查修复验证")
    
    # 测试DWA安全检查
    safety_success = test_dwa_safety_check()
    
    # 测试完整DWA-RL步骤
    step_success = test_complete_dwa_rl_step()
    
    if safety_success and step_success:
        print("\n" + "=" * 80)
        print("🎉 DWA安全检查修复验证成功！")
        print("✅ 验证了:")
        print("   1. 正确的边界检查（2000x2000x2000空间）")
        print("   2. 不同分辨率配置的安全动作生成")
        print("   3. 完整的DWA-RL流程运行")
        print("   4. 巡飞弹正常移动和决策")
        print("\n🚀 现在可以重新运行ResBand验证实验！")
    else:
        print("\n❌ DWA安全检查修复验证失败，需要进一步调试")

if __name__ == "__main__":
    main()
