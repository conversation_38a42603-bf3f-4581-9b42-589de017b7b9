#!/usr/bin/env python3
"""
恢复训练脚本 - 从检查点继续训练
"""

import os
import sys
import argparse
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from paper_simulation.experiments.experiment_runner import ExperimentRunner

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="从检查点恢复训练")
    
    parser.add_argument(
        "--checkpoint", 
        type=str, 
        help="检查点路径"
    )
    
    parser.add_argument(
        "--experiment", 
        type=str, 
        help="实验名称（如果不指定检查点路径）"
    )
    
    parser.add_argument(
        "--method", 
        type=str, 
        help="方法名称（如果不指定检查点路径）"
    )
    
    parser.add_argument(
        "--list-checkpoints", 
        action="store_true", 
        help="列出所有可用的检查点"
    )
    
    parser.add_argument(
        "--continue-episodes", 
        type=int, 
        default=50, 
        help="继续训练的episode数量（默认50）"
    )
    
    return parser.parse_args()

def list_available_checkpoints():
    """列出所有可用的检查点"""
    runner = ExperimentRunner()
    checkpoints_dir = runner.dirs["checkpoints"]
    
    if not checkpoints_dir.exists():
        print("❌ 没有找到检查点目录")
        return
    
    checkpoints = []
    for checkpoint_dir in checkpoints_dir.iterdir():
        if checkpoint_dir.is_dir():
            checkpoint_file = checkpoint_dir / "checkpoint_data.json"
            if checkpoint_file.exists():
                try:
                    with open(checkpoint_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    checkpoints.append((checkpoint_dir, data))
                except:
                    continue
    
    if not checkpoints:
        print("❌ 没有找到有效的检查点")
        return
    
    print("📋 可用的检查点:")
    print("=" * 80)
    
    for i, (checkpoint_dir, data) in enumerate(checkpoints, 1):
        print(f"{i}. {checkpoint_dir.name}")
        print(f"   实验: {data.get('experiment_name', 'Unknown')}")
        print(f"   方法: {data.get('method_name', 'Unknown')}")
        print(f"   Episode: {data.get('episode', 'Unknown')}")
        print(f"   时间: {data.get('timestamp', 'Unknown')}")
        print(f"   路径: {checkpoint_dir}")
        print()

def resume_from_checkpoint(checkpoint_path: str, continue_episodes: int):
    """从检查点恢复训练"""
    
    print(f"🔄 从检查点恢复训练: {checkpoint_path}")
    
    # 创建实验运行器
    runner = ExperimentRunner()
    
    # 加载检查点数据
    checkpoint_data = runner.load_checkpoint(checkpoint_path)
    if not checkpoint_data:
        print("❌ 加载检查点失败")
        return False
    
    experiment_name = checkpoint_data.get("experiment_name")
    method_name = checkpoint_data.get("method_name")
    last_episode = checkpoint_data.get("episode", 0)
    
    print(f"📊 实验: {experiment_name}")
    print(f"🔧 方法: {method_name}")
    print(f"📈 上次Episode: {last_episode}")
    print(f"🎯 继续训练: {continue_episodes} episodes")
    
    # 这里应该实现具体的恢复训练逻辑
    # 由于当前的实验框架还没有完全实现，这里提供一个框架
    
    try:
        # 1. 重新创建环境和智能体
        print("🌍 重新创建环境...")
        
        # 2. 加载模型权重
        model_file = Path(checkpoint_path) / "model.pth"
        if model_file.exists():
            print(f"💾 加载模型: {model_file}")
            # agent.load(str(model_file))
        
        # 3. 继续训练
        print(f"🚀 继续训练 {continue_episodes} episodes...")
        
        # 这里应该调用实际的训练循环
        # for episode in range(last_episode + 1, last_episode + 1 + continue_episodes):
        #     # 训练逻辑
        #     pass
        
        print("✅ 恢复训练完成")
        return True
        
    except Exception as e:
        print(f"❌ 恢复训练失败: {e}")
        return False

def find_and_resume(experiment_name: str = None, method_name: str = None, continue_episodes: int = 50):
    """查找并恢复最新的检查点"""
    
    runner = ExperimentRunner()
    
    # 查找最新检查点
    latest_checkpoint = runner.find_latest_checkpoint(experiment_name, method_name)
    
    if not latest_checkpoint:
        print("❌ 没有找到匹配的检查点")
        if experiment_name:
            print(f"   实验名称: {experiment_name}")
        if method_name:
            print(f"   方法名称: {method_name}")
        print("\n💡 提示: 使用 --list-checkpoints 查看所有可用检查点")
        return False
    
    print(f"🔍 找到最新检查点: {latest_checkpoint}")
    
    # 恢复训练
    return resume_from_checkpoint(latest_checkpoint, continue_episodes)

def interactive_resume():
    """交互式恢复训练"""
    
    print("🎯 交互式恢复训练")
    print("=" * 50)
    
    # 列出检查点
    runner = ExperimentRunner()
    checkpoints_dir = runner.dirs["checkpoints"]
    
    if not checkpoints_dir.exists():
        print("❌ 没有找到检查点目录")
        return False
    
    checkpoints = []
    for checkpoint_dir in checkpoints_dir.iterdir():
        if checkpoint_dir.is_dir():
            checkpoint_file = checkpoint_dir / "checkpoint_data.json"
            if checkpoint_file.exists():
                try:
                    with open(checkpoint_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    checkpoints.append((checkpoint_dir, data))
                except:
                    continue
    
    if not checkpoints:
        print("❌ 没有找到有效的检查点")
        return False
    
    # 显示选项
    print("📋 选择要恢复的检查点:")
    for i, (checkpoint_dir, data) in enumerate(checkpoints, 1):
        print(f"{i}. {data.get('experiment_name', 'Unknown')} - {data.get('method_name', 'Unknown')} (Episode {data.get('episode', 'Unknown')})")
    
    # 用户选择
    try:
        choice = int(input("\n请输入选择 (1-{}): ".format(len(checkpoints))))
        if 1 <= choice <= len(checkpoints):
            selected_checkpoint, _ = checkpoints[choice - 1]
            
            # 询问继续训练的episode数
            continue_episodes = input("继续训练的episode数 (默认50): ").strip()
            continue_episodes = int(continue_episodes) if continue_episodes else 50
            
            return resume_from_checkpoint(str(selected_checkpoint), continue_episodes)
        else:
            print("❌ 无效的选择")
            return False
            
    except (ValueError, KeyboardInterrupt):
        print("\n❌ 操作取消")
        return False

def main():
    """主函数"""
    args = parse_arguments()
    
    print("🔄 训练恢复工具")
    print("=" * 50)
    
    # 列出检查点
    if args.list_checkpoints:
        list_available_checkpoints()
        return
    
    # 从指定检查点恢复
    if args.checkpoint:
        if not Path(args.checkpoint).exists():
            print(f"❌ 检查点路径不存在: {args.checkpoint}")
            return
        
        success = resume_from_checkpoint(args.checkpoint, args.continue_episodes)
        if success:
            print("🎉 恢复训练成功!")
        else:
            print("❌ 恢复训练失败!")
        return
    
    # 查找并恢复
    if args.experiment or args.method:
        success = find_and_resume(args.experiment, args.method, args.continue_episodes)
        if success:
            print("🎉 恢复训练成功!")
        else:
            print("❌ 恢复训练失败!")
        return
    
    # 交互式恢复
    success = interactive_resume()
    if success:
        print("🎉 恢复训练成功!")
    else:
        print("❌ 恢复训练失败!")

if __name__ == "__main__":
    main()
