"""
本文方法训练脚本
Our Method Training Script

训练DWA-RL + ResBand + MLACF集成框架
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pickle

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from environments.paper_environment import PaperSimulationEnvironment
from algorithms.lightweight_dwa_rl import LightweightDWARL
from algorithms.simplified_resband import EnhancedResBand
from algorithms.basic_mlacf import EnhancedMLACF

class OurMethodTrainer:
    """本文方法训练器"""
    
    def __init__(self, config=None, output_dir="results/our_method"):
        """
        初始化训练器
        
        Args:
            config: 训练配置
            output_dir: 输出目录
        """
        # 默认配置
        default_config = {
            "total_episodes": 500,
            "max_steps_per_episode": 500,
            "save_interval": 50,
            "eval_interval": 25,
            "eval_episodes": 10,
            "learning_rate": 3e-4,
            "batch_size": 256,
            "buffer_size": 100000,
            "environment_stages": ["stage1_simple", "stage2_complex", "stage3_dynamic"],
            "progressive_training": True
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"training_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 训练统计
        self.training_stats = {
            "episode_rewards": [],
            "episode_lengths": [],
            "success_rates": [],
            "constraint_violations": [],
            "resband_selections": [],
            "mlacf_adaptations": [],
            "eval_results": []
        }
        
        print(f"🚀 本文方法训练器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 训练配置: {self.config}")
    
    def create_environment(self, stage="stage2_complex"):
        """创建训练环境"""
        return PaperSimulationEnvironment(stage)
    
    def create_framework(self, environment):
        """创建DWA-RL框架"""
        framework_config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True,
            "mlacf_enabled": True,
            "learning_rate": self.config["learning_rate"],
            "batch_size": self.config["batch_size"],
            "buffer_size": self.config["buffer_size"]
        }
        
        return LightweightDWARL(environment, framework_config)
    
    def train_progressive(self):
        """渐进式训练：从简单到复杂"""
        print("\n🎯 开始渐进式训练")
        
        total_episodes = 0
        
        for stage_idx, stage in enumerate(self.config["environment_stages"]):
            print(f"\n📊 训练阶段 {stage_idx + 1}/{len(self.config['environment_stages'])}: {stage}")
            
            # 创建环境和框架
            env = self.create_environment(stage)
            framework = self.create_framework(env)
            
            # 计算该阶段的episode数
            stage_episodes = self.config["total_episodes"] // len(self.config["environment_stages"])
            if stage_idx == len(self.config["environment_stages"]) - 1:
                # 最后阶段包含剩余的episodes
                stage_episodes = self.config["total_episodes"] - total_episodes
            
            # 训练该阶段
            stage_stats = self.train_stage(framework, stage_episodes, total_episodes, stage)
            
            # 更新总统计
            self.update_global_stats(stage_stats)
            
            # 保存阶段模型
            self.save_stage_model(framework, stage, stage_idx)
            
            total_episodes += stage_episodes
        
        print(f"\n✅ 渐进式训练完成，总episodes: {total_episodes}")

        # 保存最终模型
        self.save_final_model(framework)

        return self.training_stats
    
    def train_stage(self, framework, episodes, episode_offset, stage_name):
        """训练单个阶段"""
        print(f"   🔄 开始训练 {episodes} episodes")
        
        stage_stats = {
            "episode_rewards": [],
            "episode_lengths": [],
            "success_rates": [],
            "constraint_violations": [],
            "resband_selections": [],
            "mlacf_adaptations": []
        }
        
        for episode in range(episodes):
            global_episode = episode_offset + episode
            
            # 训练一个episode
            episode_info = framework.train_episode(
                max_steps=self.config["max_steps_per_episode"],
                training_progress=(global_episode + 1) / self.config["total_episodes"]
            )
            
            # 记录统计信息
            stage_stats["episode_rewards"].append(episode_info["episode_reward"])
            stage_stats["episode_lengths"].append(episode_info["episode_length"])
            stage_stats["constraint_violations"].append(episode_info.get("constraint_violations", 0))
            
            # 记录ResBand选择
            if hasattr(framework, 'resband') and framework.resband:
                resband_info = {
                    "episode": global_episode,
                    "selections": framework.resband.arm_selection_history[-10:] if len(framework.resband.arm_selection_history) > 0 else []
                }
                stage_stats["resband_selections"].append(resband_info)
            
            # 记录MLACF适应
            if hasattr(framework, 'mlacf') and framework.mlacf:
                mlacf_info = {
                    "episode": global_episode,
                    "adaptations": len(framework.mlacf.adaptation_history)
                }
                stage_stats["mlacf_adaptations"].append(mlacf_info)
            
            # 定期评估
            if (episode + 1) % self.config["eval_interval"] == 0:
                eval_result = self.evaluate_model(framework, global_episode)
                stage_stats["success_rates"].append(eval_result["success_rate"])
                
                print(f"     Episode {global_episode + 1}: "
                      f"奖励={episode_info['episode_reward']:.1f}, "
                      f"成功率={eval_result['success_rate']:.2%}, "
                      f"违反={episode_info.get('constraint_violations', 0)}")
            
            # 定期保存
            if (episode + 1) % self.config["save_interval"] == 0:
                self.save_checkpoint(framework, global_episode, stage_stats)
        
        return stage_stats
    
    def evaluate_model(self, framework, episode):
        """评估模型性能"""
        eval_rewards = []
        eval_successes = []
        eval_violations = []
        
        for eval_ep in range(self.config["eval_episodes"]):
            eval_info = framework.evaluate_episode(max_steps=self.config["max_steps_per_episode"])
            
            eval_rewards.append(eval_info["episode_reward"])
            eval_successes.append(eval_info.get("success", False))
            eval_violations.append(eval_info.get("constraint_violations", 0))
        
        eval_result = {
            "episode": episode,
            "avg_reward": np.mean(eval_rewards),
            "success_rate": np.mean(eval_successes),
            "avg_violations": np.mean(eval_violations),
            "std_reward": np.std(eval_rewards)
        }
        
        self.training_stats["eval_results"].append(eval_result)
        return eval_result
    
    def update_global_stats(self, stage_stats):
        """更新全局统计"""
        for key in ["episode_rewards", "episode_lengths", "constraint_violations"]:
            self.training_stats[key].extend(stage_stats[key])
        
        self.training_stats["success_rates"].extend(stage_stats["success_rates"])
        self.training_stats["resband_selections"].extend(stage_stats["resband_selections"])
        self.training_stats["mlacf_adaptations"].extend(stage_stats["mlacf_adaptations"])
    
    def save_stage_model(self, framework, stage_name, stage_idx):
        """保存阶段模型"""
        model_path = os.path.join(self.output_dir, f"model_stage_{stage_idx}_{stage_name}.pkl")
        
        model_data = {
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "stage_name": stage_name,
            "stage_idx": stage_idx,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"   💾 保存阶段模型: {model_path}")

    def save_final_model(self, framework):
        """保存最终训练模型"""
        model_path = os.path.join(self.output_dir, "model_final.pkl")

        model_data = {
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat(),
            "model_type": "final"
        }

        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"   💾 保存最终模型: {model_path}")

    def save_checkpoint(self, framework, episode, stage_stats):
        """保存训练检查点"""
        checkpoint_path = os.path.join(self.output_dir, f"checkpoint_episode_{episode}.pkl")
        
        checkpoint_data = {
            "episode": episode,
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "training_stats": self.training_stats,
            "stage_stats": stage_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)
    
    def save_final_results(self):
        """保存最终训练结果"""
        # 保存训练统计
        stats_path = os.path.join(self.output_dir, "training_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(self.training_stats, f, indent=2, default=str)
        
        # 保存配置
        config_path = os.path.join(self.output_dir, "training_config.json")
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # 生成训练曲线图
        self.plot_training_curves()
        
        print(f"📊 训练结果保存至: {self.output_dir}")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 奖励曲线
        if self.training_stats["episode_rewards"]:
            axes[0, 0].plot(self.training_stats["episode_rewards"])
            axes[0, 0].set_title("Episode Rewards")
            axes[0, 0].set_xlabel("Episode")
            axes[0, 0].set_ylabel("Reward")
        
        # 成功率曲线
        if self.training_stats["eval_results"]:
            eval_episodes = [r["episode"] for r in self.training_stats["eval_results"]]
            success_rates = [r["success_rate"] for r in self.training_stats["eval_results"]]
            axes[0, 1].plot(eval_episodes, success_rates)
            axes[0, 1].set_title("Success Rate")
            axes[0, 1].set_xlabel("Episode")
            axes[0, 1].set_ylabel("Success Rate")
        
        # 约束违反
        if self.training_stats["constraint_violations"]:
            axes[1, 0].plot(self.training_stats["constraint_violations"])
            axes[1, 0].set_title("Constraint Violations")
            axes[1, 0].set_xlabel("Episode")
            axes[1, 0].set_ylabel("Violations")
        
        # Episode长度
        if self.training_stats["episode_lengths"]:
            axes[1, 1].plot(self.training_stats["episode_lengths"])
            axes[1, 1].set_title("Episode Lengths")
            axes[1, 1].set_xlabel("Episode")
            axes[1, 1].set_ylabel("Steps")
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "training_curves.png"), dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    print("🚀 开始训练本文方法 (DWA-RL + ResBand + MLACF)")
    
    # 训练配置
    config = {
        "total_episodes": 500,
        "max_steps_per_episode": 500,
        "save_interval": 50,
        "eval_interval": 25,
        "eval_episodes": 10,
        "progressive_training": True
    }
    
    # 创建训练器
    trainer = OurMethodTrainer(config)
    
    # 开始训练
    start_time = datetime.now()
    
    if config["progressive_training"]:
        training_stats = trainer.train_progressive()
    else:
        # 单阶段训练的代码可以在这里添加
        pass
    
    end_time = datetime.now()
    training_duration = end_time - start_time
    
    # 保存结果
    trainer.save_final_results()
    
    print(f"\n✅ 训练完成!")
    print(f"⏱️ 训练时间: {training_duration}")
    print(f"📊 最终统计:")
    
    if training_stats["episode_rewards"]:
        print(f"   平均奖励: {np.mean(training_stats['episode_rewards'][-50:]):.2f}")
    
    if training_stats["eval_results"]:
        final_success_rate = training_stats["eval_results"][-1]["success_rate"]
        print(f"   最终成功率: {final_success_rate:.2%}")
    
    if training_stats["constraint_violations"]:
        avg_violations = np.mean(training_stats["constraint_violations"][-50:])
        print(f"   平均约束违反: {avg_violations:.2f}")

if __name__ == "__main__":
    main()
