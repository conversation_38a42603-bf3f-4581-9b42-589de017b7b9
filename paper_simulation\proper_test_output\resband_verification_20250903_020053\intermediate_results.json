{"convergence_analysis": {"resband_adaptive": {"episodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], "rewards": [876.6593842634558, 899.4386765221934, 885.1188221901347, 853.5530717800514, 865.5512079568649, 4.28249024157539, 4.027512575952172, 4.473140244000305, 4.8773765863630985, 5.042717438941879, 4.088030138416914, 3.2941661815334715, 6.074430968169165, 4.328952297462527, 3.43008074831104, 5.138364413838656, 5.693333953165227, 5.687745306872016, 4.406635407913223, 4.90526318912619, 4.0514448034744595, 3.5778093003750477, 5.009953546016111, 4.984757675370068, 3.0647950413356857, 3.789440516619429, 2.9911815735082143, 5.03748000108028, 3.9479276049553507, 3.0854088486713858, 4.119094305073842, 3.0900317279329728, 3.7749707876052305, 5.26131497393486, 4.588849889018906, 4.081005112982132, 2.896976006387829, 3.6200042845847062, 5.137964435871818, 5.005733297522586, 4.690279793068692, 1.985916653804401, 4.489884535896958, 5.763703248741332, 2.3369166856412003], "success_rates": []}, "fixed_coarse": {"episodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], "rewards": [858.1777446050378, 872.4882664492725, 840.2547843252376, 870.603398064371, 885.384584844306, 5.136933083507101, 4.529869143154173, 3.1961774871484674, 5.516211136978859, 6.021523054519125, 4.677506044917473, 4.600703092569407, 4.286768501782469, 3.7153191884015166, 2.6726636452654224, 5.300376785988897, 3.851497549800002, 2.5238458629649325, 3.339536320134509, 5.43482672544563, 4.773790670389661, 3.322466916653839, 4.10371919322445, 3.5870417596083786, 4.995953894072378, 4.156905891506084, 5.295095785343663, 4.027471435161702, 5.118502630183698, 2.9232853447006115, 5.540892108311549, 3.1413178762986482, 5.6421748102743745, 3.909253171921872, 5.3889490722684315, 6.2502183588437275, 4.628675629316467, 4.834673875343921, 3.7966505696924147, 3.32810591640948, 5.01742741575791, 5.039545061513745, 6.593140958558223, 4.600069312333582, 5.585469610578088], "success_rates": []}, "fixed_medium": {"episodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], "rewards": [858.7566907160179, 861.7149734802878, 859.083812829475, 888.5570587770652, 892.3158401734406, 4.009046290853652, 5.007846296075879, 2.9926763571948882, 4.133665956322264, 4.5873469269633205, 5.555183646172408, 4.309151382177153, 1.900254471587498, 4.844679226781369, 4.077632502657752, 1.989501893651629, 3.251915461748423, 4.3961532799008465, 5.349622219804041, 3.815945729003647, 3.0325163962577277, 4.262176482837553, 5.176895223351475, 2.2956034934402894, 2.2618825284402404, 4.67260326613897, 3.3561377414294777, 5.015638932011163, 6.047318597279029, 5.900494431998762, 3.3190413826887024, 2.6346384687812012, 4.705964335417055, 4.842396267280507, 2.063381644450282, 1.6403324384168283, 3.2901294974414084, 3.8893868306931, 5.097259220406418, 5.360402847043596, 5.403068998262863, 4.967307955131072, 4.132255596121497, 5.104818950907981, 2.943730529774487], "success_rates": []}}, "performance_comparison": {"resband_adaptive": {"final_performance": {"avg_reward": 871.9433067887445, "success_rate": 0.0, "avg_steps": 200.0, "constraint_violations": 0}, "learning_efficiency": {"convergence_episode": 45, "learning_rate": 0}}, "fixed_coarse": {"final_performance": {"avg_reward": 878.2893736747916, "success_rate": 0.0, "avg_steps": 200.0, "constraint_violations": 0}, "learning_efficiency": {"convergence_episode": 45, "learning_rate": 0}}, "fixed_medium": {"final_performance": {"avg_reward": 862.9530615872188, "success_rate": 0.0, "avg_steps": 200.0, "constraint_violations": 0}, "learning_efficiency": {"convergence_episode": 45, "learning_rate": 0}}}, "efficiency_analysis": {"resband_adaptive": {"computation_times": [45.395851135253906, 35.74228286743164, 33.07509422302246, 35.70103645324707, 33.58101844787598, 2.1512508392333984, 1.0271072387695312, 1.110076904296875, 1.0008811950683594, 0.0, 0.0, 1.0097026824951172, 0.5943775177001953, 1.0030269622802734, 0.0, 0.0, 2.8536319732666016, 0.0, 2.315521240234375, 0.9996891021728516, 0.9989738464355469, 0.0, 1.2269020080566406, 0.3483295440673828, 0.0, 0.0, 0.0, 0.0, 0.0, 4.894256591796875, 0.0, 1.0447502136230469, 0.9965896606445312, 0.45800209045410156, 0.0, 0.0, 0.0, 0.25725364685058594, 2.8994083404541016, 0.0, 0.9989738464355469, 0.0, 0.8656978607177734, 0.3559589385986328, 0.0], "memory_usage": [], "avg_computation_time": 4.73123656378852}, "fixed_coarse": {"computation_times": [31.985998153686523, 32.71603584289551, 33.42580795288086, 32.744407653808594, 40.346622467041016, 0.9992122650146484, 0.0, 0.9996891021728516, 1.9543170928955078, 0.07319450378417969, 0.0, 0.0, 1.0020732879638672, 1.9042491912841797, 0.7517337799072266, 0.9999275207519531, 2.0987987518310547, 1.0325908660888672, 0.99945068359375, 1.1951923370361328, 0.9996891021728516, 1.7910003662109375, 0.0, 0.0, 0.0, 4.423379898071289, 0.9877681732177734, 0.0, 1.0018348693847656, 0.9977817535400391, 0.7429122924804688, 0.0, 0.0, 0.0, 4.7760009765625, 1.6634464263916016, 1.03759765625, 1.2793540954589844, 0.0, 0.0, 0.0, 3.1731128692626953, 0.0, 2.4614334106445312, 0.0], "memory_usage": [], "avg_computation_time": 4.679213629828559}, "fixed_medium": {"computation_times": [35.02202033996582, 37.8415584564209, 34.68942642211914, 33.58340263366699, 35.91322898864746, 0.5271434783935547, 0.0, 0.0, 1.798391342163086, 1.44195556640625, 0.19598007202148438, 0.0, 0.0, 0.0, 3.764629364013672, 0.0, 0.0, 0.9968280792236328, 0.0, 3.0870437622070312, 0.44989585876464844, 0.0, 0.0, 1.0008811950683594, 1.0309219360351562, 0.9684562683105469, 1.2431144714355469, 0.2346038818359375, 0.0, 0.0, 0.0, 5.552053451538086, 0.6289482116699219, 0.0, 1.0044574737548828, 0.9968280792236328, 1.230001449584961, 0.7991790771484375, 1.0221004486083984, 0.0, 0.0, 0.0, 4.611730575561523, 1.1587142944335938, 0.5979537963867188], "memory_usage": [], "avg_computation_time": 4.69758775499132}}, "resolution_selection": {"resband_adaptive": {"selections": [], "performance_by_resolution": {}}}, "statistical_tests": {}}