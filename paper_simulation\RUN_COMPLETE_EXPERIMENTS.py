"""
一键运行完整实验
RUN COMPLETE EXPERIMENTS

完整流程：训练所有方法 → 运行所有实验 → 生成所有图表
Complete Pipeline: Train All Methods → Run All Experiments → Generate All Figures

预计时间：约12小时
Estimated Time: ~12 hours
"""

import os
import sys
import subprocess
from datetime import datetime
import time

class CompleteExperimentRunner:
    """完整实验运行器"""
    
    def __init__(self):
        """初始化运行器"""
        self.start_time = None
        self.results = {
            "training": {"success": False, "duration": None, "error": None},
            "experiments": {"success": False, "duration": None, "error": None},
            "plotting": {"success": False, "duration": None, "error": None}
        }
        
        print("🚀 完整实验运行器")
        print("=" * 60)
        print("📋 实验流程:")
        print("   1. 训练所有方法 (约10小时)")
        print("   2. 运行所有实验 (约2小时)")
        print("   3. 生成所有图表 (约10分钟)")
        print("=" * 60)
    
    def run_complete_experiments(self):
        """运行完整实验流程"""
        print(f"\n🎯 开始完整实验流程")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 确认开始
        print(f"\n⚠️ 重要提醒:")
        print(f"   • 完整实验预计需要约12小时")
        print(f"   • 建议在空闲时间运行（如周末）")
        print(f"   • 所有数据都是真实训练，符合学术标准")
        
        response = input(f"\n是否开始完整实验？(y/N): ")
        if response.lower() != 'y':
            print("❌ 用户取消实验")
            return False
        
        self.start_time = datetime.now()
        
        # 步骤1：训练所有方法
        print(f"\n{'='*60}")
        print(f"🏋️ 步骤1：训练所有方法")
        print(f"{'='*60}")
        
        training_success = self._run_training()
        self.results["training"]["success"] = training_success
        
        if not training_success:
            print(f"❌ 训练失败，停止实验")
            return False
        
        # 步骤2：运行所有实验
        print(f"\n{'='*60}")
        print(f"🧪 步骤2：运行所有实验")
        print(f"{'='*60}")
        
        experiments_success = self._run_experiments()
        self.results["experiments"]["success"] = experiments_success
        
        if not experiments_success:
            print(f"❌ 实验失败，但可以继续作图")
        
        # 步骤3：生成所有图表
        print(f"\n{'='*60}")
        print(f"📊 步骤3：生成所有图表")
        print(f"{'='*60}")
        
        plotting_success = self._run_plotting()
        self.results["plotting"]["success"] = plotting_success
        
        # 显示总结
        self._show_summary()
        
        return all(result["success"] for result in self.results.values())
    
    def _run_training(self):
        """运行训练阶段"""
        print("🚀 开始训练所有方法...")
        
        training_scripts = [
            ("train/01_train_our_method.py", "本文方法"),
            ("train/02_train_baseline_td3.py", "TD3基线"),
            ("train/03_train_baseline_dwa_td3.py", "DWA-TD3基线"),
            ("train/04_train_baseline_ppo.py", "PPO基线"),
            ("train/05_train_baseline_traditional.py", "传统DWA基线")
        ]
        
        start_time = datetime.now()
        
        for script_path, method_name in training_scripts:
            print(f"\n🏋️ 训练 {method_name}...")
            
            success = self._run_script(script_path, timeout=14400)  # 4小时超时
            
            if success:
                print(f"✅ {method_name} 训练完成")
            else:
                print(f"❌ {method_name} 训练失败")
                return False
        
        end_time = datetime.now()
        duration = end_time - start_time
        self.results["training"]["duration"] = str(duration)
        
        print(f"\n✅ 所有方法训练完成，耗时: {duration}")
        return True
    
    def _run_experiments(self):
        """运行实验阶段"""
        print("🧪 开始运行所有实验...")
        
        experiment_scripts = [
            ("experiment/01_safety_test.py", "安全保障验证"),
            ("experiment/02_resband_test.py", "ResBand验证"),
            ("experiment/03_counterintuitive_test.py", "反直觉验证")
        ]
        
        start_time = datetime.now()
        
        for script_path, experiment_name in experiment_scripts:
            print(f"\n🧪 运行 {experiment_name}...")
            
            success = self._run_script(script_path, timeout=7200)  # 2小时超时
            
            if success:
                print(f"✅ {experiment_name} 完成")
            else:
                print(f"❌ {experiment_name} 失败")
                # 实验失败不中断，继续其他实验
        
        end_time = datetime.now()
        duration = end_time - start_time
        self.results["experiments"]["duration"] = str(duration)
        
        print(f"\n✅ 所有实验完成，耗时: {duration}")
        return True
    
    def _run_plotting(self):
        """运行作图阶段"""
        print("📊 开始生成所有图表...")
        
        start_time = datetime.now()
        
        # 运行图表生成脚本
        success = self._run_script("plot/generate_all_figures.py", timeout=1800)  # 30分钟超时
        
        end_time = datetime.now()
        duration = end_time - start_time
        self.results["plotting"]["duration"] = str(duration)
        
        if success:
            print(f"✅ 所有图表生成完成，耗时: {duration}")
            return True
        else:
            print(f"❌ 图表生成失败，耗时: {duration}")
            return False
    
    def _run_script(self, script_path, timeout=3600):
        """运行单个脚本"""
        try:
            print(f"   🚀 执行: {script_path}")
            
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=os.path.dirname(__file__)
            )
            
            if result.returncode == 0:
                print(f"   ✅ 执行成功")
                return True
            else:
                print(f"   ❌ 执行失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ 执行超时")
            return False
        except Exception as e:
            print(f"   💥 执行异常: {e}")
            return False
    
    def _show_summary(self):
        """显示实验总结"""
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        print(f"\n{'='*60}")
        print(f"📊 完整实验总结")
        print(f"{'='*60}")
        
        print(f"⏰ 总耗时: {total_duration}")
        print(f"🏋️ 训练阶段: {'✅ 成功' if self.results['training']['success'] else '❌ 失败'} "
              f"({self.results['training']['duration']})")
        print(f"🧪 实验阶段: {'✅ 成功' if self.results['experiments']['success'] else '❌ 失败'} "
              f"({self.results['experiments']['duration']})")
        print(f"📊 作图阶段: {'✅ 成功' if self.results['plotting']['success'] else '❌ 失败'} "
              f"({self.results['plotting']['duration']})")
        
        success_count = sum(1 for result in self.results.values() if result["success"])
        
        if success_count == 3:
            print(f"\n🎉 完整实验全部成功！")
            print(f"📊 现在可以查看以下结果:")
            print(f"   • 训练模型: results/*/model_final.pkl")
            print(f"   • 实验数据: results/*/")
            print(f"   • 论文图表: results/figures/")
            print(f"\n🏆 所有数据都基于真实训练，符合学术标准！")
        else:
            print(f"\n⚠️ 部分实验失败 ({success_count}/3 成功)")
            print(f"📋 建议检查失败的阶段并重新运行")

def main():
    """主函数"""
    print("🚀 一键运行完整实验")
    print("🎯 DWA-RL巡飞弹路径规划算法完整验证")
    
    # 创建运行器
    runner = CompleteExperimentRunner()
    
    # 运行完整实验
    success = runner.run_complete_experiments()
    
    if success:
        print(f"\n🎉 完整实验成功完成！")
        print(f"🏆 可以开始撰写学术论文了！")
    else:
        print(f"\n⚠️ 实验部分完成，请检查失败的部分")

if __name__ == "__main__":
    main()
