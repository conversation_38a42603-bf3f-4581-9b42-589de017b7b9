"""
性能指标收集模块
Performance Metrics Collection Module

收集和分析实验的关键性能指标
"""

import numpy as np
import json
import time
from datetime import datetime

class PerformanceMetricsCollector:
    """性能指标收集器"""
    
    def __init__(self):
        """初始化指标收集器"""
        self.metrics_data = {}
        self.start_time = None
        
        # 定义关键指标
        self.key_metrics = [
            "success_rate",           # 成功率
            "average_reward",         # 平均奖励
            "training_time",          # 训练时间
            "constraint_violations",  # 约束违反次数
            "convergence_episodes",   # 收敛episode数
            "computational_efficiency", # 计算效率
            "safety_score",           # 安全评分
            "adaptation_speed"        # 适应速度
        ]
        
        print("📊 性能指标收集器初始化完成")
    
    def start_collection(self, experiment_name):
        """开始收集指定实验的指标"""
        self.start_time = time.time()
        self.metrics_data[experiment_name] = {
            "start_time": self.start_time,
            "metrics": {},
            "episode_data": [],
            "training_progress": []
        }
        
        print(f"📈 开始收集实验 {experiment_name} 的性能指标")
    
    def record_episode_metrics(self, experiment_name, episode, metrics):
        """记录单个episode的指标"""
        if experiment_name not in self.metrics_data:
            self.start_collection(experiment_name)
        
        episode_data = {
            "episode": episode,
            "timestamp": time.time(),
            "metrics": metrics
        }
        
        self.metrics_data[experiment_name]["episode_data"].append(episode_data)
    
    def record_training_progress(self, experiment_name, progress_data):
        """记录训练进度"""
        if experiment_name not in self.metrics_data:
            self.start_collection(experiment_name)
        
        progress_entry = {
            "timestamp": time.time(),
            "progress": progress_data
        }
        
        self.metrics_data[experiment_name]["training_progress"].append(progress_entry)
    
    def finalize_experiment_metrics(self, experiment_name, final_results):
        """完成实验指标收集"""
        if experiment_name not in self.metrics_data:
            print(f"⚠️ 实验 {experiment_name} 未开始指标收集")
            return
        
        end_time = time.time()
        experiment_data = self.metrics_data[experiment_name]
        
        # 计算总训练时间
        training_time = end_time - experiment_data["start_time"]
        
        # 提取关键指标
        key_metrics = self._extract_key_metrics(final_results, training_time)
        
        # 计算统计指标
        statistical_metrics = self._calculate_statistical_metrics(experiment_data["episode_data"])
        
        # 合并所有指标
        experiment_data["metrics"] = {
            **key_metrics,
            **statistical_metrics,
            "training_time": training_time,
            "end_time": end_time
        }
        
        print(f"✅ 实验 {experiment_name} 指标收集完成")
        print(f"   训练时间: {training_time/60:.1f}分钟")
        print(f"   成功率: {key_metrics.get('success_rate', 0):.2f}")
        print(f"   约束违反: {key_metrics.get('constraint_violations', 0)}")
    
    def _extract_key_metrics(self, results, training_time):
        """提取关键指标"""
        metrics = {}
        
        # 基础性能指标
        if isinstance(results, dict):
            metrics["success_rate"] = results.get("success_rate", 0)
            metrics["average_reward"] = results.get("average_reward", 0)
            metrics["constraint_violations"] = results.get("total_constraint_violations", 0)
            metrics["convergence_episodes"] = results.get("convergence_episodes", 0)
            
            # 计算安全评分
            violations_per_episode = results.get("average_violations_per_episode", 0)
            metrics["safety_score"] = max(0, 1.0 - violations_per_episode)
            
            # 计算计算效率（episodes per minute）
            total_episodes = results.get("total_episodes", 1)
            metrics["computational_efficiency"] = total_episodes / (training_time / 60)
            
            # 适应速度（基于收敛速度）
            convergence_ratio = metrics["convergence_episodes"] / total_episodes if total_episodes > 0 else 1
            metrics["adaptation_speed"] = 1.0 - convergence_ratio
        
        return metrics
    
    def _calculate_statistical_metrics(self, episode_data):
        """计算统计指标"""
        if not episode_data:
            return {}
        
        # 提取奖励序列
        rewards = [ep["metrics"].get("episode_reward", 0) for ep in episode_data]
        
        if not rewards:
            return {}
        
        # 计算统计量
        metrics = {
            "reward_mean": np.mean(rewards),
            "reward_std": np.std(rewards),
            "reward_min": np.min(rewards),
            "reward_max": np.max(rewards),
            "reward_trend": self._calculate_trend(rewards),
            "stability_score": self._calculate_stability(rewards)
        }
        
        return metrics
    
    def _calculate_trend(self, data):
        """计算数据趋势"""
        if len(data) < 2:
            return 0
        
        # 简单线性趋势
        x = np.arange(len(data))
        slope = np.polyfit(x, data, 1)[0]
        return slope
    
    def _calculate_stability(self, data):
        """计算稳定性评分"""
        if len(data) < 2:
            return 1.0
        
        # 基于变异系数的稳定性
        mean_val = np.mean(data)
        if mean_val == 0:
            return 0.0
        
        cv = np.std(data) / abs(mean_val)
        stability = max(0, 1.0 - cv)
        return stability
    
    def get_experiment_summary(self, experiment_name):
        """获取实验指标摘要"""
        if experiment_name not in self.metrics_data:
            return None
        
        data = self.metrics_data[experiment_name]
        metrics = data.get("metrics", {})
        
        summary = {
            "experiment_name": experiment_name,
            "training_time_minutes": metrics.get("training_time", 0) / 60,
            "success_rate": metrics.get("success_rate", 0),
            "average_reward": metrics.get("average_reward", 0),
            "constraint_violations": metrics.get("constraint_violations", 0),
            "safety_score": metrics.get("safety_score", 0),
            "computational_efficiency": metrics.get("computational_efficiency", 0),
            "adaptation_speed": metrics.get("adaptation_speed", 0),
            "stability_score": metrics.get("stability_score", 0)
        }
        
        return summary
    
    def get_all_experiments_summary(self):
        """获取所有实验的指标摘要"""
        summaries = {}
        
        for exp_name in self.metrics_data:
            summaries[exp_name] = self.get_experiment_summary(exp_name)
        
        return summaries
    
    def generate_metrics_comparison(self, experiment_names=None):
        """生成指标对比"""
        if experiment_names is None:
            experiment_names = list(self.metrics_data.keys())
        
        comparison = {
            "experiments": experiment_names,
            "metrics_comparison": {},
            "rankings": {}
        }
        
        # 对比各项指标
        for metric in self.key_metrics:
            metric_values = {}
            
            for exp_name in experiment_names:
                if exp_name in self.metrics_data:
                    metrics = self.metrics_data[exp_name].get("metrics", {})
                    metric_values[exp_name] = metrics.get(metric, 0)
            
            comparison["metrics_comparison"][metric] = metric_values
            
            # 排名（数值越大越好的指标）
            if metric in ["success_rate", "safety_score", "computational_efficiency", "adaptation_speed", "stability_score"]:
                sorted_exps = sorted(metric_values.items(), key=lambda x: x[1], reverse=True)
            else:  # 数值越小越好的指标
                sorted_exps = sorted(metric_values.items(), key=lambda x: x[1])
            
            comparison["rankings"][metric] = [exp[0] for exp in sorted_exps]
        
        return comparison
    
    def save_metrics_report(self, output_path):
        """保存指标报告"""
        report_data = {
            "collection_timestamp": datetime.now().isoformat(),
            "experiments_summary": self.get_all_experiments_summary(),
            "detailed_metrics": self.metrics_data,
            "comparison": self.generate_metrics_comparison()
        }
        
        # 转换numpy类型
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, (np.integer, np.floating)):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        report_data = convert_numpy(report_data)
        
        # 保存JSON报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"📊 性能指标报告已保存: {output_path}")
        
        return report_data
    
    def generate_metrics_table(self):
        """生成指标对比表格"""
        summaries = self.get_all_experiments_summary()
        
        if not summaries:
            return "无实验数据"
        
        # 表格标题
        table = "| 实验名称 | 成功率 | 平均奖励 | 约束违反 | 安全评分 | 计算效率 | 适应速度 |\n"
        table += "|----------|--------|----------|----------|----------|----------|----------|\n"
        
        # 表格内容
        for exp_name, summary in summaries.items():
            if summary:
                table += f"| {exp_name} | {summary['success_rate']:.3f} | "
                table += f"{summary['average_reward']:.1f} | "
                table += f"{summary['constraint_violations']} | "
                table += f"{summary['safety_score']:.3f} | "
                table += f"{summary['computational_efficiency']:.1f} | "
                table += f"{summary['adaptation_speed']:.3f} |\n"
        
        return table

# 全局指标收集器实例
metrics_collector = PerformanceMetricsCollector()

def get_metrics_collector():
    """获取全局指标收集器"""
    return metrics_collector

def collect_experiment_metrics(experiment_name, results):
    """收集实验指标的便捷函数"""
    collector = get_metrics_collector()
    collector.finalize_experiment_metrics(experiment_name, results)
    return collector.get_experiment_summary(experiment_name)
