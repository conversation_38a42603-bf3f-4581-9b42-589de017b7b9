#!/usr/bin/env python3
"""
测试完整episode是否能跑到终点
"""

def test_complete_episode():
    """测试完整episode"""
    print("🎯 测试完整episode是否能跑到终点")
    print("=" * 60)
    
    try:
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment('stage3_dynamic')
        config = {
            'state_dim': 6,
            'action_dim': 3,
            'dwa_enabled': True,
            'resband_enabled': True,
            'mlacf_enabled': True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = 'resband_adaptive'
        
        print(f"✅ 环境和智能体创建成功")
        
        # 运行完整episode
        state = env.reset()
        print(f"初始状态: {state[:3]}")
        print(f"目标位置: {env.target_pos}")
        
        initial_distance = np.linalg.norm(env.target_pos - state[:3])
        print(f"初始距离: {initial_distance:.1f}米")
        
        max_steps = 2000
        step = 0
        total_distance_moved = 0
        
        while step < max_steps:
            # 执行一步
            step_info = agent.train_step(state, training_progress=step/max_steps)
            
            next_state = step_info['next_state']
            action = step_info['action']
            reward = step_info['reward']
            done = step_info['done']
            
            # 计算移动距离
            movement = np.linalg.norm(next_state[:3] - state[:3])
            total_distance_moved += movement
            
            # 计算到目标的距离
            distance_to_goal = np.linalg.norm(env.target_pos - next_state[:3])
            
            # 每100步打印一次进度
            if step % 100 == 0 or step < 10:
                print(f"步骤 {step:4d}: 位置={next_state[:3]}, "
                      f"距目标={distance_to_goal:.1f}米, "
                      f"移动={movement:.2f}米, "
                      f"动作={action}, "
                      f"奖励={reward:.3f}")
            
            # 检查是否到达目标
            if distance_to_goal < 100:  # 100米内算到达
                print(f"\n🎉 成功到达目标！")
                print(f"   总步数: {step + 1}")
                print(f"   最终距离: {distance_to_goal:.1f}米")
                print(f"   总移动距离: {total_distance_moved:.1f}米")
                print(f"   平均每步移动: {total_distance_moved/(step+1):.2f}米")
                return True
            
            # 检查是否异常结束
            if done:
                print(f"\n⚠️ Episode在第{step + 1}步结束")
                print(f"   最终距离: {distance_to_goal:.1f}米")
                print(f"   总移动距离: {total_distance_moved:.1f}米")
                
                if step < 10:
                    print(f"   ❌ 过早结束（前10步）")
                    return False
                elif distance_to_goal > 500:
                    print(f"   ❌ 距离目标太远")
                    return False
                else:
                    print(f"   ✅ 正常结束")
                    return True
            
            # 检查是否有异常移动
            if movement > 50:  # 单步移动超过50米
                print(f"\n❌ 异常移动: {movement:.1f}米")
                return False
            
            state = next_state
            step += 1
        
        # 达到最大步数
        final_distance = np.linalg.norm(env.target_pos - state[:3])
        print(f"\n⏰ 达到最大步数 {max_steps}")
        print(f"   最终距离: {final_distance:.1f}米")
        print(f"   总移动距离: {total_distance_moved:.1f}米")
        
        if final_distance < 200:
            print(f"   ✅ 接近目标")
            return True
        else:
            print(f"   ❌ 距离目标太远")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 完整Episode测试")
    
    success = test_complete_episode()
    
    if success:
        print("\n🎉 测试成功！智能体能够跑到终点")
        print("现在可以运行完整的ResBand验证实验")
    else:
        print("\n❌ 测试失败！需要进一步调试")

if __name__ == "__main__":
    main()
