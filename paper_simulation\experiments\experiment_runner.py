#!/usr/bin/env python3
"""
实验运行器 - 负责执行各种实验配置
"""

import os
import sys
import time
import json
import logging
import numpy as np
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from paper_simulation.experiments.experiment_configs import MAIN_EXPERIMENTS
from paper_simulation.experiments.fast_training_protocol import FastTrainingProtocol
from paper_simulation.experiments.performance_metrics import PerformanceMetrics
from paper_simulation.core.dwa_td3_agent import DWA_TD3_Agent
from paper_simulation.core.patrol_environment import PatrolEnvironment

class ExperimentRunner:
    """实验运行器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.fast_protocol = FastTrainingProtocol()
        self.metrics = PerformanceMetrics()

        # 创建完整的输出目录结构
        self.timestamp = time.strftime("%Y%m%d_%H%M%S")
        self.base_results_dir = Path("paper_simulation/results")
        self.experiment_dir = self.base_results_dir / f"experiment_run_{self.timestamp}"

        # 创建子目录
        self.dirs = {
            "models": self.experiment_dir / "models",
            "plots": self.experiment_dir / "plots",
            "data": self.experiment_dir / "data",
            "logs": self.experiment_dir / "logs",
            "checkpoints": self.experiment_dir / "checkpoints",
            "csv_reports": self.experiment_dir / "csv_reports",
            "trajectories": self.experiment_dir / "trajectories"
        }

        # 创建所有目录
        for dir_path in self.dirs.values():
            dir_path.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"实验输出目录: {self.experiment_dir}")
        
    def run_experiment(self, experiment_name: str) -> Dict[str, Any]:
        """运行指定实验"""
        if experiment_name not in MAIN_EXPERIMENTS:
            raise ValueError(f"未知实验: {experiment_name}")
            
        experiment_config = MAIN_EXPERIMENTS[experiment_name]
        self.logger.info(f"🚀 开始实验: {experiment_config['name']}")
        
        results = {}
        methods = experiment_config["methods"]
        
        for method_name, method_config in methods.items():
            self.logger.info(f"📊 运行方法: {method_config['name']}")
            
            # 运行单个方法
            method_results = self._run_single_method(
                method_name, 
                method_config,
                experiment_name
            )
            
            results[method_name] = method_results
            
        # 保存完整的实验结果
        self._save_complete_experiment_results(experiment_name, results)
        
        self.logger.info(f"✅ 实验完成: {experiment_config['name']}")
        return results
    
    def run_single_experiment(self, exp_name: str, exp_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个实验配置"""
        self.logger.info(f"🚀 开始单个实验: {exp_config['name']}")
        
        # 创建环境和智能体
        env = self._create_environment(exp_config["config"])
        agent = self._create_agent(exp_config["config"])
        
        # 运行训练
        results = self._run_training(
            agent, 
            env, 
            exp_config["episodes"],
            exp_name
        )
        
        self.logger.info(f"✅ 单个实验完成: {exp_config['name']}")
        return results
    
    def _run_single_method(self, method_name: str, method_config: Dict[str, Any], experiment_name: str) -> Dict[str, Any]:
        """运行单个方法"""

        # 创建环境
        env = self._create_environment(method_config["config"])

        # 创建智能体
        agent = self._create_agent(method_config["config"])

        # 检查是否是传统DWA（无需训练）
        if "test_episodes" in method_config:
            # 传统DWA直接测试
            results = self._run_traditional_dwa_test(
                env,
                method_config["test_episodes"],
                f"{experiment_name}_{method_name}"
            )
        elif "training_protocol" in method_config:
            # 分阶段训练
            results = self._run_staged_training(
                agent,
                env,
                method_config["training_protocol"],
                f"{experiment_name}_{method_name}"
            )
        else:
            # 简单训练（向后兼容）
            episodes = method_config.get("episodes", 100)
            results = self._run_training(
                agent,
                env,
                episodes,
                f"{experiment_name}_{method_name}"
            )

        return results
    
    def _create_environment(self, config: Dict[str, Any]) -> PatrolEnvironment:
        """创建环境"""
        env_type = config.get("environment", "stage1_simple")
        
        # 根据环境类型设置参数
        if env_type == "stage1_simple":
            env_config = {
                "num_static_obstacles": 3,
                "num_dynamic_obstacles": 1,
                "obstacle_speed_range": (0.5, 1.0),
                "map_size": (100, 100, 50)
            }
        elif env_type == "stage2_complex":
            env_config = {
                "num_static_obstacles": 6,
                "num_dynamic_obstacles": 2,
                "obstacle_speed_range": (0.8, 1.5),
                "map_size": (120, 120, 60)
            }
        elif env_type == "stage3_dynamic":
            env_config = {
                "num_static_obstacles": 4,
                "num_dynamic_obstacles": 4,
                "obstacle_speed_range": (1.0, 2.0),
                "map_size": (100, 100, 50)
            }
        else:
            env_config = {}
            
        return PatrolEnvironment(**env_config)
    
    def _create_agent(self, config: Dict[str, Any]) -> DWA_TD3_Agent:
        """创建智能体"""
        agent_config = {
            "state_dim": 15,  # 6D状态 + 5D场景特征 + 4D目标信息
            "action_dim": 3,  # [a_T, a_N, μ]
            "dwa_enabled": config.get("dwa_enabled", True),
            "resband_enabled": config.get("resband_enabled", True),
            "mlacf_enabled": config.get("mlacf_enabled", False),
            "traditional_mode": config.get("traditional_mode", False),
            "fixed_resolution": config.get("fixed_resolution", None),
            "heuristic_scheduling": config.get("heuristic_scheduling", False)
        }
        
        return DWA_TD3_Agent(**agent_config)
    
    def _run_training(self, agent: DWA_TD3_Agent, env: PatrolEnvironment, episodes: int, exp_id: str) -> Dict[str, Any]:
        """运行训练过程"""
        
        # 训练记录
        episode_rewards = []
        episode_lengths = []
        constraint_violations = []
        success_rates = []
        convergence_episodes = []
        
        # 快速训练协议
        early_stopping_patience = 20
        min_improvement = 0.01
        best_avg_reward = -float('inf')
        patience_counter = 0
        
        self.logger.info(f"开始训练 {episodes} episodes...")
        
        for episode in range(episodes):
            state = env.reset()
            episode_reward = 0
            episode_length = 0
            violations = 0
            
            done = False
            while not done:
                # 选择动作
                action = agent.select_action(state)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 记录约束违反
                if info.get("constraint_violation", False):
                    violations += 1
                
                # 存储经验
                agent.store_transition(state, action, reward, next_state, done)
                
                # 更新智能体
                if len(agent.replay_buffer) > agent.batch_size:
                    agent.update()
                
                state = next_state
                episode_reward += reward
                episode_length += 1
                
                if episode_length > 1000:  # 最大步数限制
                    break
            
            # 记录结果
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            constraint_violations.append(violations)
            
            # 计算成功率（最近10个episode）
            if len(episode_rewards) >= 10:
                recent_rewards = episode_rewards[-10:]
                success_rate = sum(1 for r in recent_rewards if r > 1000) / len(recent_rewards)
                success_rates.append(success_rate)
            
            # 早停检查
            if episode >= 20:  # 至少训练20个episode
                recent_avg = np.mean(episode_rewards[-10:])
                if recent_avg > best_avg_reward + min_improvement:
                    best_avg_reward = recent_avg
                    patience_counter = 0
                else:
                    patience_counter += 1
                
                if patience_counter >= early_stopping_patience:
                    self.logger.info(f"早停触发，episode {episode}")
                    convergence_episodes.append(episode)
                    break
            
            # 定期日志
            if episode % 10 == 0:
                avg_reward = np.mean(episode_rewards[-10:]) if len(episode_rewards) >= 10 else np.mean(episode_rewards)
                self.logger.info(f"Episode {episode}: 平均奖励 = {avg_reward:.2f}")
        
        # 计算最终指标
        results = {
            "episode_rewards": episode_rewards,
            "episode_lengths": episode_lengths,
            "constraint_violations": constraint_violations,
            "success_rates": success_rates,
            "final_performance": {
                "avg_reward": np.mean(episode_rewards[-20:]) if len(episode_rewards) >= 20 else np.mean(episode_rewards),
                "success_rate": np.mean(success_rates[-10:]) if len(success_rates) >= 10 else 0.0,
                "total_violations": sum(constraint_violations),
                "convergence_episode": convergence_episodes[0] if convergence_episodes else episodes
            },
            "training_info": {
                "total_episodes": len(episode_rewards),
                "early_stopped": len(convergence_episodes) > 0,
                "experiment_id": exp_id
            }
        }
        
        return results
    
    def _save_complete_experiment_results(self, experiment_name: str, results: Dict[str, Any]):
        """保存完整的实验结果"""

        # 1. 保存JSON格式的详细结果
        json_file = self.dirs["data"] / f"{experiment_name}_results.json"
        serializable_results = self._make_json_serializable(results)
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        self.logger.info(f"📄 JSON结果已保存: {json_file}")

        # 2. 保存CSV格式的数据
        self._save_csv_reports(experiment_name, results)

        # 3. 生成可视化图表
        self._generate_experiment_plots(experiment_name, results)

        # 4. 生成文本报告
        self._generate_text_report(experiment_name, results)

        # 5. 保存训练好的模型（如果有）
        self._save_trained_models(experiment_name, results)
    
    def _make_json_serializable(self, obj):
        """使对象可JSON序列化"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        else:
            return obj

    def _run_staged_training(self, agent: DWA_TD3_Agent, env: PatrolEnvironment,
                           training_protocol: Dict[str, int], exp_id: str) -> Dict[str, Any]:
        """运行分阶段训练（借鉴巡飞简化ver设计）"""

        random_episodes = training_protocol["random_episodes"]
        fixed_episodes = training_protocol["fixed_episodes"]
        total_episodes = training_protocol["total_episodes"]

        self.logger.info(f"开始分阶段训练: 随机{random_episodes} + 固定{fixed_episodes} = 总计{total_episodes} episodes")

        # 训练记录
        all_rewards = []
        all_lengths = []
        all_violations = []
        phase_data = {
            "random_phase": {"rewards": [], "violations": [], "success_count": 0},
            "fixed_phase": {"rewards": [], "violations": [], "success_count": 0}
        }

        # 第一阶段：随机场景探索
        self.logger.info(f"🎲 随机场景探索阶段 ({random_episodes} episodes)")
        scenario_candidates = []

        for episode in range(random_episodes):
            state = env.reset()
            scenario_data = env.save_scenario() if hasattr(env, 'save_scenario') else None

            episode_reward = 0
            episode_length = 0
            violations = 0

            done = False
            while not done:
                action = agent.select_action(state)
                next_state, reward, done, info = env.step(action)

                if info.get("constraint_violation", False):
                    violations += 1

                agent.store_transition(state, action, reward, next_state, done)

                if len(agent.replay_buffer) > agent.batch_size:
                    agent.update()

                state = next_state
                episode_reward += reward
                episode_length += 1

                if episode_length > 1000:
                    break

            # 记录场景复杂度（简化版）
            complexity_score = violations + episode_length / 1000.0
            if scenario_data:
                scenario_candidates.append({
                    "scenario": scenario_data,
                    "complexity_score": complexity_score,
                    "reward": episode_reward
                })

            # 记录数据
            all_rewards.append(episode_reward)
            all_lengths.append(episode_length)
            all_violations.append(violations)
            phase_data["random_phase"]["rewards"].append(episode_reward)
            phase_data["random_phase"]["violations"].append(violations)

            if episode_reward > 1000:  # 简化的成功判断
                phase_data["random_phase"]["success_count"] += 1

            if episode % 20 == 0:
                avg_reward = np.mean(phase_data["random_phase"]["rewards"][-20:])
                self.logger.info(f"  随机阶段 Episode {episode}: 平均奖励 = {avg_reward:.2f}")

        # 选择最具挑战性的场景
        if scenario_candidates:
            scenario_candidates.sort(key=lambda x: x['complexity_score'], reverse=True)
            selected_scenario = scenario_candidates[0]["scenario"]
        else:
            selected_scenario = None

        # 第二阶段：固定场景强化训练
        self.logger.info(f"📌 固定场景强化训练 ({fixed_episodes} episodes)")

        for episode in range(fixed_episodes):
            if selected_scenario:
                env.load_scenario(selected_scenario) if hasattr(env, 'load_scenario') else env.reset()
            else:
                state = env.reset()

            episode_reward = 0
            episode_length = 0
            violations = 0

            done = False
            while not done:
                action = agent.select_action(state)
                next_state, reward, done, info = env.step(action)

                if info.get("constraint_violation", False):
                    violations += 1

                agent.store_transition(state, action, reward, next_state, done)

                if len(agent.replay_buffer) > agent.batch_size:
                    agent.update()

                state = next_state
                episode_reward += reward
                episode_length += 1

                if episode_length > 1000:
                    break

            # 记录数据
            all_rewards.append(episode_reward)
            all_lengths.append(episode_length)
            all_violations.append(violations)
            phase_data["fixed_phase"]["rewards"].append(episode_reward)
            phase_data["fixed_phase"]["violations"].append(violations)

            if episode_reward > 1000:
                phase_data["fixed_phase"]["success_count"] += 1

            if episode % 20 == 0:
                avg_reward = np.mean(phase_data["fixed_phase"]["rewards"][-20:])
                self.logger.info(f"  固定阶段 Episode {episode}: 平均奖励 = {avg_reward:.2f}")

        # 计算最终结果
        results = {
            "episode_rewards": all_rewards,
            "episode_lengths": all_lengths,
            "constraint_violations": all_violations,
            "phase_data": phase_data,
            "final_performance": {
                "avg_reward": np.mean(all_rewards[-20:]) if len(all_rewards) >= 20 else np.mean(all_rewards),
                "success_rate": (phase_data["random_phase"]["success_count"] +
                               phase_data["fixed_phase"]["success_count"]) / total_episodes,
                "total_violations": sum(all_violations),
                "random_phase_success_rate": phase_data["random_phase"]["success_count"] / random_episodes,
                "fixed_phase_success_rate": phase_data["fixed_phase"]["success_count"] / fixed_episodes
            },
            "training_info": {
                "total_episodes": total_episodes,
                "random_episodes": random_episodes,
                "fixed_episodes": fixed_episodes,
                "experiment_id": exp_id,
                "training_type": "staged_training"
            }
        }

        return results

    def _run_traditional_dwa_test(self, env: PatrolEnvironment, test_episodes: int, exp_id: str) -> Dict[str, Any]:
        """运行传统DWA测试（无需训练）"""

        self.logger.info(f"开始传统DWA测试 ({test_episodes} episodes)")

        # 测试记录
        test_rewards = []
        test_lengths = []
        test_violations = []
        success_count = 0

        for episode in range(test_episodes):
            state = env.reset()
            episode_reward = 0
            episode_length = 0
            violations = 0

            done = False
            while not done:
                # 传统DWA算法（简化实现）
                action = self._traditional_dwa_action(state, env)
                next_state, reward, done, info = env.step(action)

                if info.get("constraint_violation", False):
                    violations += 1

                state = next_state
                episode_reward += reward
                episode_length += 1

                if episode_length > 1000:
                    break

            # 记录数据
            test_rewards.append(episode_reward)
            test_lengths.append(episode_length)
            test_violations.append(violations)

            if episode_reward > 1000:
                success_count += 1

            if episode % 10 == 0:
                avg_reward = np.mean(test_rewards[-10:]) if len(test_rewards) >= 10 else np.mean(test_rewards)
                self.logger.info(f"  测试 Episode {episode}: 平均奖励 = {avg_reward:.2f}")

        # 计算结果
        results = {
            "episode_rewards": test_rewards,
            "episode_lengths": test_lengths,
            "constraint_violations": test_violations,
            "final_performance": {
                "avg_reward": np.mean(test_rewards),
                "success_rate": success_count / test_episodes,
                "total_violations": sum(test_violations)
            },
            "training_info": {
                "total_episodes": test_episodes,
                "experiment_id": exp_id,
                "training_type": "traditional_test"
            }
        }

        return results

    def _traditional_dwa_action(self, state, env):
        """传统DWA动作选择（简化实现）"""
        # 这里应该实现传统DWA算法
        # 简化版本：返回随机安全动作
        return np.random.uniform(-1, 1, 3)  # [a_T, a_N, μ]

    def _save_csv_reports(self, experiment_name: str, results: Dict[str, Any]):
        """保存CSV格式的详细数据"""
        import csv

        for method_name, method_results in results.items():
            csv_file = self.dirs["csv_reports"] / f"{experiment_name}_{method_name}.csv"

            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # 写入标题
                writer.writerow(['Episode', 'Reward', 'Length', 'Violations', 'Success', 'Phase'])

                # 写入数据
                episode_rewards = method_results.get("episode_rewards", [])
                episode_lengths = method_results.get("episode_lengths", [])
                violations = method_results.get("constraint_violations", [])

                for i, (reward, length, viol) in enumerate(zip(episode_rewards, episode_lengths, violations)):
                    success = reward > 1000  # 简化的成功判断
                    phase = "Random" if i < len(episode_rewards) // 2 else "Fixed"
                    writer.writerow([i+1, f"{reward:.2f}", length, viol, success, phase])

            self.logger.info(f"📊 CSV报告已保存: {csv_file}")

    def _generate_experiment_plots(self, experiment_name: str, results: Dict[str, Any]):
        """生成实验可视化图表"""
        try:
            import matplotlib.pyplot as plt

            # 1. 收敛曲线对比图
            self._plot_convergence_curves(experiment_name, results)

            # 2. 性能对比柱状图
            self._plot_performance_comparison(experiment_name, results)

            # 3. 约束违反对比图
            self._plot_constraint_violations(experiment_name, results)

            # 4. 成功率对比图
            self._plot_success_rates(experiment_name, results)

        except Exception as e:
            self.logger.error(f"生成图表失败: {e}")

    def _plot_convergence_curves(self, experiment_name: str, results: Dict[str, Any]):
        """绘制收敛曲线"""
        try:
            import matplotlib.pyplot as plt

            plt.figure(figsize=(12, 8))

            for method_name, method_results in results.items():
                rewards = method_results.get("episode_rewards", [])
                if rewards:
                    # 计算移动平均
                    window_size = min(20, len(rewards) // 5)
                    if window_size > 1:
                        moving_avg = np.convolve(rewards, np.ones(window_size)/window_size, mode='valid')
                        episodes = range(window_size-1, len(rewards))
                        plt.plot(episodes, moving_avg, label=f"{method_name} (Moving Avg)", linewidth=2)

                    # 原始数据（透明）
                    plt.plot(range(len(rewards)), rewards, alpha=0.3, linewidth=0.5)

            plt.title(f"{experiment_name} - Convergence Curves")
            plt.xlabel("Episode")
            plt.ylabel("Reward")
            plt.legend()
            plt.grid(True, alpha=0.3)

            plot_file = self.dirs["plots"] / f"{experiment_name}_convergence.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"📈 收敛曲线已保存: {plot_file}")

        except Exception as e:
            self.logger.error(f"绘制收敛曲线失败: {e}")

    def _plot_performance_comparison(self, experiment_name: str, results: Dict[str, Any]):
        """绘制性能对比图"""
        try:
            import matplotlib.pyplot as plt

            methods = []
            avg_rewards = []
            success_rates = []

            for method_name, method_results in results.items():
                methods.append(method_name)

                final_perf = method_results.get("final_performance", {})
                avg_rewards.append(final_perf.get("avg_reward", 0))
                success_rates.append(final_perf.get("success_rate", 0))

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # 平均奖励对比
            bars1 = ax1.bar(methods, avg_rewards, alpha=0.7, color='skyblue')
            ax1.set_title("Average Reward Comparison")
            ax1.set_ylabel("Average Reward")
            ax1.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars1, avg_rewards):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(avg_rewards)*0.01,
                        f'{value:.1f}', ha='center', va='bottom')

            # 成功率对比
            bars2 = ax2.bar(methods, success_rates, alpha=0.7, color='lightcoral')
            ax2.set_title("Success Rate Comparison")
            ax2.set_ylabel("Success Rate")
            ax2.set_ylim(0, 1)
            ax2.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars2, success_rates):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{value:.2%}', ha='center', va='bottom')

            plt.tight_layout()

            plot_file = self.dirs["plots"] / f"{experiment_name}_performance.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"📊 性能对比图已保存: {plot_file}")

        except Exception as e:
            self.logger.error(f"绘制性能对比图失败: {e}")

    def _plot_constraint_violations(self, experiment_name: str, results: Dict[str, Any]):
        """绘制约束违反对比图"""
        try:
            import matplotlib.pyplot as plt

            methods = []
            total_violations = []

            for method_name, method_results in results.items():
                methods.append(method_name)
                violations = method_results.get("constraint_violations", [])
                total_violations.append(sum(violations))

            plt.figure(figsize=(10, 6))
            bars = plt.bar(methods, total_violations, alpha=0.7, color='orange')
            plt.title(f"{experiment_name} - Constraint Violations Comparison")
            plt.ylabel("Total Constraint Violations")
            plt.xticks(rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, total_violations):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(total_violations)*0.01,
                        f'{value}', ha='center', va='bottom')

            plt.tight_layout()

            plot_file = self.dirs["plots"] / f"{experiment_name}_violations.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"⚠️ 约束违反图已保存: {plot_file}")

        except Exception as e:
            self.logger.error(f"绘制约束违反图失败: {e}")

    def _plot_success_rates(self, experiment_name: str, results: Dict[str, Any]):
        """绘制成功率趋势图"""
        try:
            import matplotlib.pyplot as plt

            plt.figure(figsize=(12, 8))

            for method_name, method_results in results.items():
                rewards = method_results.get("episode_rewards", [])
                if rewards:
                    # 计算滑动成功率
                    window_size = 20
                    success_rates = []
                    episodes = []

                    for i in range(window_size, len(rewards)):
                        window_rewards = rewards[i-window_size:i]
                        success_rate = sum(1 for r in window_rewards if r > 1000) / window_size
                        success_rates.append(success_rate)
                        episodes.append(i)

                    if success_rates:
                        plt.plot(episodes, success_rates, label=method_name, linewidth=2)

            plt.title(f"{experiment_name} - Success Rate Trends")
            plt.xlabel("Episode")
            plt.ylabel("Success Rate (20-episode window)")
            plt.ylim(0, 1)
            plt.legend()
            plt.grid(True, alpha=0.3)

            plot_file = self.dirs["plots"] / f"{experiment_name}_success_trends.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"📈 成功率趋势图已保存: {plot_file}")

        except Exception as e:
            self.logger.error(f"绘制成功率趋势图失败: {e}")

    def _generate_text_report(self, experiment_name: str, results: Dict[str, Any]):
        """生成详细的文本报告"""
        try:
            report_file = self.dirs["data"] / f"{experiment_name}_report.txt"

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"# {experiment_name} 实验报告\n")
                f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                f.write("## 实验概述\n")
                f.write(f"实验名称: {experiment_name}\n")
                f.write(f"参与方法数量: {len(results)}\n\n")

                f.write("## 各方法详细结果\n\n")

                for method_name, method_results in results.items():
                    f.write(f"### {method_name}\n")

                    final_perf = method_results.get("final_performance", {})
                    training_info = method_results.get("training_info", {})

                    f.write(f"- 平均奖励: {final_perf.get('avg_reward', 0):.2f}\n")
                    f.write(f"- 成功率: {final_perf.get('success_rate', 0):.2%}\n")
                    f.write(f"- 总约束违反: {final_perf.get('total_violations', 0)}\n")
                    f.write(f"- 总Episodes: {training_info.get('total_episodes', 0)}\n")
                    f.write(f"- 训练类型: {training_info.get('training_type', 'unknown')}\n\n")

                f.write("## 统计分析\n")
                # 这里可以添加统计分析结果
                f.write("详细统计分析请参考统计分析模块的输出。\n\n")

                f.write("## 文件说明\n")
                f.write("- JSON结果: 包含完整的训练数据\n")
                f.write("- CSV报告: 逐episode的详细数据\n")
                f.write("- 图表文件: 可视化分析结果\n")
                f.write("- 模型文件: 训练好的神经网络权重\n")

            self.logger.info(f"📄 文本报告已保存: {report_file}")

        except Exception as e:
            self.logger.error(f"生成文本报告失败: {e}")

    def _save_trained_models(self, experiment_name: str, results: Dict[str, Any]):
        """保存训练好的模型"""
        try:
            # 这里应该保存每个方法训练好的模型
            # 由于当前实现中没有实际的模型对象，这里只是占位符

            model_info_file = self.dirs["models"] / f"{experiment_name}_model_info.txt"

            with open(model_info_file, 'w', encoding='utf-8') as f:
                f.write(f"# {experiment_name} 模型信息\n")
                f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                for method_name, method_results in results.items():
                    training_info = method_results.get("training_info", {})
                    if training_info.get("training_type") != "traditional_test":
                        f.write(f"## {method_name}\n")
                        f.write(f"- 模型类型: TD3神经网络\n")
                        f.write(f"- 训练Episodes: {training_info.get('total_episodes', 0)}\n")
                        f.write(f"- 模型文件: {method_name}_model.pth (待实现)\n\n")

            self.logger.info(f"💾 模型信息已保存: {model_info_file}")

        except Exception as e:
            self.logger.error(f"保存模型信息失败: {e}")

    def save_checkpoint(self, experiment_name: str, method_name: str, episode: int,
                       agent=None, results: Dict[str, Any] = None):
        """保存训练检查点"""
        try:
            checkpoint_name = f"{experiment_name}_{method_name}_ep{episode}_{self.timestamp}"
            checkpoint_dir = self.dirs["checkpoints"] / checkpoint_name
            checkpoint_dir.mkdir(exist_ok=True)

            # 保存训练状态
            checkpoint_data = {
                "experiment_name": experiment_name,
                "method_name": method_name,
                "episode": episode,
                "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
                "results": results
            }

            checkpoint_file = checkpoint_dir / "checkpoint_data.json"
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(self._make_json_serializable(checkpoint_data), f, indent=2)

            # 保存模型（如果有）
            if agent and hasattr(agent, 'save'):
                model_file = checkpoint_dir / "model.pth"
                agent.save(str(model_file))

            self.logger.info(f"💾 检查点已保存: {checkpoint_name}")
            return str(checkpoint_dir)

        except Exception as e:
            self.logger.error(f"保存检查点失败: {e}")
            return None

    def load_checkpoint(self, checkpoint_path: str):
        """加载训练检查点"""
        try:
            checkpoint_file = Path(checkpoint_path) / "checkpoint_data.json"

            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)

            self.logger.info(f"✅ 检查点已加载: {checkpoint_path}")
            return checkpoint_data

        except Exception as e:
            self.logger.error(f"加载检查点失败: {e}")
            return None

    def find_latest_checkpoint(self, experiment_name: str = None, method_name: str = None):
        """查找最新的检查点"""
        try:
            checkpoints = []

            for checkpoint_dir in self.dirs["checkpoints"].iterdir():
                if checkpoint_dir.is_dir():
                    checkpoint_file = checkpoint_dir / "checkpoint_data.json"
                    if checkpoint_file.exists():
                        # 检查是否匹配条件
                        if experiment_name and experiment_name not in checkpoint_dir.name:
                            continue
                        if method_name and method_name not in checkpoint_dir.name:
                            continue

                        checkpoints.append(checkpoint_dir)

            if checkpoints:
                # 按修改时间排序，返回最新的
                latest = max(checkpoints, key=lambda x: x.stat().st_mtime)
                return str(latest)

            return None

        except Exception as e:
            self.logger.error(f"查找检查点失败: {e}")
            return None
