#!/usr/bin/env python3
"""
最终的ResBand验证实验
完全匹配巡飞简化ver的设置和流程
"""

def main():
    """运行最终的ResBand验证实验"""
    print("🎰 ResBand算法验证实验 - 最终版本")
    print("=" * 80)
    print("🎯 完全匹配巡飞简化ver的设置和流程")
    print("📋 实验设计:")
    print("   - 起点: [200, 200, 200] (左下角)")
    print("   - 终点: [1800, 1800, 1800] (右上角)")
    print("   - 4种对比方法:")
    print("     1. ResBand自适应分辨率")
    print("     2. 固定粗分辨率（三个阶段都用粗分辨率）")
    print("     3. 固定细分辨率（三个阶段都用细分辨率）")
    print("     4. 启发式分辨率调度")
    print("   - 3个训练阶段:")
    print("     阶段1: 6-8个大型静态障碍物")
    print("     阶段2: 12-15个大型静态障碍物")
    print("     阶段3: 8-10个静态 + 4-6个动态大型障碍物")
    print("   - 每阶段每种方法: 200个episodes")
    print("   - 总计: 4方法 × 3阶段 × 200episodes = 2400个episodes")
    print("   - 预计时间: 2-3小时")
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建实验
        experiment = ResBandVerificationExperiment("resband_final_results")
        
        # 确认配置
        print(f"\n✅ 实验配置验证:")
        print(f"   起点: {experiment.config['start_pos']}")
        print(f"   终点: {experiment.config['goal_pos']}")
        print(f"   每阶段episodes: {experiment.config['episodes_per_stage']}")
        print(f"   最大步数: {experiment.config['max_steps_per_episode']}")
        print(f"   对比方法: {experiment.config['comparison_methods']}")
        
        # 开始实验
        print(f"\n🚀 开始ResBand验证实验...")
        print(f"💾 结果将保存到: {experiment.output_dir}")
        print(f"🔄 支持中止后重启：重新运行此脚本即可从断点继续")
        
        # 运行实验
        results = experiment.run_experiment()
        
        if results:
            print(f"\n🎉 ResBand验证实验完成！")
            print(f"📊 实验结果摘要:")
            
            # 显示各方法的最终性能
            for method in experiment.config["comparison_methods"]:
                if method in results.get("performance_comparison", {}):
                    perf = results["performance_comparison"][method]["final_performance"]
                    print(f"\n   📈 {method}:")
                    print(f"     - 平均奖励: {perf.get('avg_reward', 0):.2f}")
                    print(f"     - 成功率: {perf.get('success_rate', 0):.2%}")
                    print(f"     - 约束违反: {perf.get('constraint_violations', 0):.1f}")
                    print(f"     - 平均计算时间: {results['efficiency_analysis'][method].get('avg_computation_time', 0):.2f}ms")
            
            # 显示ResBand的分辨率选择分析
            if "resolution_selection" in results and "resband_adaptive" in results["resolution_selection"]:
                print(f"\n🎯 ResBand分辨率选择分析:")
                selections = results["resolution_selection"]["resband_adaptive"].get("selections", [])
                if selections:
                    from collections import Counter
                    selection_counts = Counter(selections)
                    total = len(selections)
                    resolution_names = {0: "粗分辨率", 1: "中等分辨率", 2: "精细分辨率"}
                    for resolution, count in selection_counts.items():
                        percentage = count / total * 100
                        print(f"     - {resolution_names.get(resolution, f'分辨率{resolution}')}: {percentage:.1f}% ({count}/{total})")
            
            print(f"\n📁 详细结果文件:")
            print(f"   - 收敛分析图: {experiment.output_dir}/resband_convergence_analysis.png")
            print(f"   - 性能对比表: {experiment.output_dir}/resband_performance_comparison.csv")
            print(f"   - 分辨率选择分析: {experiment.output_dir}/resband_selection_analysis.png")
            print(f"   - 完整实验报告: {experiment.output_dir}/experiment_2_report.md")
            
        else:
            print(f"\n❌ 实验被取消或失败")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断实验")
        print(f"🔄 重新运行此脚本可从断点继续")
        return False
        
    except Exception as e:
        print(f"\n❌ 实验失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 ResBand算法验证实验 - 论文级别完整实验")
    print("📖 验证ResBand自适应分辨率选择相对于固定分辨率的优势")
    print("🏗️ 基于正确的DWA-RL架构：ResBand选择分辨率 → DWA生成安全动作集 → RL选择最优动作")
    print("🎮 完全匹配巡飞简化ver的起止点、训练流程和场景设置")
    
    success = main()
    
    if success:
        print(f"\n🏆 ResBand验证实验成功完成！")
        print(f"📊 实验验证了:")
        print(f"   ✅ ResBand自适应分辨率选择的优势")
        print(f"   ✅ 与固定分辨率方法的性能对比")
        print(f"   ✅ 渐进式训练的有效性")
        print(f"   ✅ 正确的DWA-RL集成架构")
        print(f"   ✅ 巡飞弹场景的完整实现")
    else:
        print(f"\n💡 如需重新开始或从断点继续，请重新运行此脚本")
