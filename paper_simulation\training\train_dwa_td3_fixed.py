"""
DWA-TD3固定分辨率基线算法训练脚本
DWA-TD3 Fixed Resolution Baseline Training Script

训练使用固定分辨率的DWA-TD3组合算法
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pickle

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from environments.paper_environment import PaperSimulationEnvironment
from algorithms.lightweight_dwa_rl import LightweightDWARL

class DWATd3FixedTrainer:
    """DWA-TD3固定分辨率训练器"""
    
    def __init__(self, config=None, output_dir="results/dwa_td3_fixed"):
        """
        初始化训练器
        
        Args:
            config: 训练配置
            output_dir: 输出目录
        """
        # 默认配置
        default_config = {
            "total_episodes": 500,
            "max_steps_per_episode": 500,
            "save_interval": 50,
            "eval_interval": 25,
            "eval_episodes": 10,
            "learning_rate": 3e-4,
            "batch_size": 256,
            "buffer_size": 100000,
            "environment_stages": ["stage1_simple", "stage2_complex", "stage3_dynamic"],
            "progressive_training": True,
            # 固定分辨率配置
            "fixed_resolution": {
                "delta_a_T": 4.0,      # 中等分辨率
                "delta_a_N": 15.0,
                "delta_mu": 0.5,
                "name": "中等分辨率"
            }
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"training_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 训练统计
        self.training_stats = {
            "episode_rewards": [],
            "episode_lengths": [],
            "success_rates": [],
            "constraint_violations": [],
            "dwa_action_counts": [],
            "td3_selections": [],
            "eval_results": []
        }
        
        print(f"🚀 DWA-TD3固定分辨率训练器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 训练配置: {self.config}")
        print(f"🔧 固定分辨率: {self.config['fixed_resolution']}")
    
    def create_environment(self):
        """创建训练环境"""
        return PaperSimulationEnvironment(self.config["environment_stage"])
    
    def create_framework(self, environment):
        """创建DWA-TD3框架"""
        framework_config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": False,  # 禁用ResBand
            "mlacf_enabled": False,    # 禁用MLACF
            "fixed_resolution": self.config["fixed_resolution"],
            "learning_rate": self.config["learning_rate"],
            "batch_size": self.config["batch_size"],
            "buffer_size": self.config["buffer_size"]
        }
        
        return LightweightDWARL(environment, framework_config)
    
    def train(self):
        """训练DWA-TD3算法"""
        print("\n🎯 开始训练DWA-TD3固定分辨率算法")
        
        # 创建环境和框架
        env = self.create_environment()
        framework = self.create_framework(env)
        
        print(f"🏗️ 环境: {self.config['environment_stage']}")
        print(f"🤖 框架: DWA-TD3 (固定分辨率)")
        print(f"🔧 分辨率: {self.config['fixed_resolution']['name']}")
        
        for episode in range(self.config["total_episodes"]):
            episode_reward = 0
            episode_length = 0
            constraint_violations = 0
            dwa_actions_generated = 0
            td3_selections_made = 0
            
            # 重置环境
            state = env.reset()
            done = False
            
            while not done and episode_length < self.config["max_steps_per_episode"]:
                # 训练一步
                step_info = framework.train_step(
                    state, 
                    training_progress=(episode + 1) / self.config["total_episodes"]
                )
                
                # 获取下一状态和奖励
                next_state = step_info["next_state"]
                reward = step_info["reward"]
                done = step_info["done"]
                info = step_info["info"]
                
                # 统计DWA和TD3的使用情况
                if "dwa_actions_count" in step_info:
                    dwa_actions_generated += step_info["dwa_actions_count"]
                if "td3_selection" in step_info:
                    td3_selections_made += 1
                
                # 检查约束违反
                if info.get("collision", False) or info.get("boundary_violation", False):
                    constraint_violations += 1
                
                state = next_state
                episode_reward += reward
                episode_length += 1
            
            # 记录episode统计
            self.training_stats["episode_rewards"].append(episode_reward)
            self.training_stats["episode_lengths"].append(episode_length)
            self.training_stats["constraint_violations"].append(constraint_violations)
            self.training_stats["dwa_action_counts"].append(dwa_actions_generated)
            self.training_stats["td3_selections"].append(td3_selections_made)
            
            # 定期评估
            if (episode + 1) % self.config["eval_interval"] == 0:
                eval_result = self.evaluate_framework(framework, env, episode)
                self.training_stats["success_rates"].append(eval_result["success_rate"])
                
                print(f"Episode {episode + 1}: "
                      f"奖励={episode_reward:.1f}, "
                      f"长度={episode_length}, "
                      f"成功率={eval_result['success_rate']:.2%}, "
                      f"违反={constraint_violations}, "
                      f"DWA动作={dwa_actions_generated}, "
                      f"TD3选择={td3_selections_made}")
            
            # 定期保存
            if (episode + 1) % self.config["save_interval"] == 0:
                self.save_checkpoint(framework, episode)
        
        print(f"\n✅ 训练完成")

        # 保存最终模型
        self.save_final_model(framework)

        return self.training_stats
    
    def evaluate_framework(self, framework, env, episode):
        """评估框架性能"""
        eval_rewards = []
        eval_successes = []
        eval_violations = []
        eval_lengths = []
        eval_dwa_actions = []
        
        for eval_ep in range(self.config["eval_episodes"]):
            eval_reward = 0
            eval_length = 0
            eval_constraint_violations = 0
            eval_dwa_actions_count = 0
            
            state = env.reset()
            done = False
            
            while not done and eval_length < self.config["max_steps_per_episode"]:
                # 评估一步（不更新网络）
                step_info = framework.evaluate_step(state)
                
                next_state = step_info["next_state"]
                reward = step_info["reward"]
                done = step_info["done"]
                info = step_info["info"]
                
                if "dwa_actions_count" in step_info:
                    eval_dwa_actions_count += step_info["dwa_actions_count"]
                
                if info.get("collision", False) or info.get("boundary_violation", False):
                    eval_constraint_violations += 1
                
                state = next_state
                eval_reward += reward
                eval_length += 1
            
            # 判断是否成功
            success = info.get("success", False) and eval_constraint_violations == 0
            
            eval_rewards.append(eval_reward)
            eval_successes.append(success)
            eval_violations.append(eval_constraint_violations)
            eval_lengths.append(eval_length)
            eval_dwa_actions.append(eval_dwa_actions_count)
        
        eval_result = {
            "episode": episode,
            "avg_reward": np.mean(eval_rewards),
            "success_rate": np.mean(eval_successes),
            "avg_violations": np.mean(eval_violations),
            "avg_length": np.mean(eval_lengths),
            "avg_dwa_actions": np.mean(eval_dwa_actions),
            "std_reward": np.std(eval_rewards)
        }
        
        self.training_stats["eval_results"].append(eval_result)
        return eval_result
    
    def save_checkpoint(self, framework, episode):
        """保存训练检查点"""
        checkpoint_path = os.path.join(self.output_dir, f"checkpoint_episode_{episode}.pkl")
        
        checkpoint_data = {
            "episode": episode,
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)

    def save_final_model(self, framework):
        """保存最终训练模型"""
        model_path = os.path.join(self.output_dir, "model_final.pkl")

        model_data = {
            "framework_state": framework.get_state() if hasattr(framework, 'get_state') else None,
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat(),
            "model_type": "final"
        }

        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"   💾 保存最终模型: {model_path}")

    def save_final_results(self):
        """保存最终训练结果"""
        # 保存训练统计
        stats_path = os.path.join(self.output_dir, "training_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(self.training_stats, f, indent=2, default=str)
        
        # 保存配置
        config_path = os.path.join(self.output_dir, "training_config.json")
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # 生成训练曲线图
        self.plot_training_curves()
        
        print(f"📊 训练结果保存至: {self.output_dir}")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        
        # 奖励曲线
        if self.training_stats["episode_rewards"]:
            axes[0, 0].plot(self.training_stats["episode_rewards"])
            axes[0, 0].set_title("Episode Rewards")
            axes[0, 0].set_xlabel("Episode")
            axes[0, 0].set_ylabel("Reward")
        
        # 成功率曲线
        if self.training_stats["eval_results"]:
            eval_episodes = [r["episode"] for r in self.training_stats["eval_results"]]
            success_rates = [r["success_rate"] for r in self.training_stats["eval_results"]]
            axes[0, 1].plot(eval_episodes, success_rates)
            axes[0, 1].set_title("Success Rate")
            axes[0, 1].set_xlabel("Episode")
            axes[0, 1].set_ylabel("Success Rate")
        
        # 约束违反
        if self.training_stats["constraint_violations"]:
            axes[0, 2].plot(self.training_stats["constraint_violations"])
            axes[0, 2].set_title("Constraint Violations")
            axes[0, 2].set_xlabel("Episode")
            axes[0, 2].set_ylabel("Violations")
        
        # Episode长度
        if self.training_stats["episode_lengths"]:
            axes[1, 0].plot(self.training_stats["episode_lengths"])
            axes[1, 0].set_title("Episode Lengths")
            axes[1, 0].set_xlabel("Episode")
            axes[1, 0].set_ylabel("Steps")
        
        # DWA动作数量
        if self.training_stats["dwa_action_counts"]:
            axes[1, 1].plot(self.training_stats["dwa_action_counts"])
            axes[1, 1].set_title("DWA Actions Generated")
            axes[1, 1].set_xlabel("Episode")
            axes[1, 1].set_ylabel("Action Count")
        
        # TD3选择次数
        if self.training_stats["td3_selections"]:
            axes[1, 2].plot(self.training_stats["td3_selections"])
            axes[1, 2].set_title("TD3 Selections Made")
            axes[1, 2].set_xlabel("Episode")
            axes[1, 2].set_ylabel("Selection Count")
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "training_curves.png"), dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    print("🚀 开始训练DWA-TD3固定分辨率基线算法")
    
    # 训练配置
    config = {
        "total_episodes": 500,
        "max_steps_per_episode": 500,
        "save_interval": 50,
        "eval_interval": 25,
        "eval_episodes": 10,
        "environment_stage": "stage2_complex"
    }
    
    # 创建训练器
    trainer = DWATd3FixedTrainer(config)
    
    # 开始训练
    start_time = datetime.now()
    training_stats = trainer.train()
    end_time = datetime.now()
    training_duration = end_time - start_time
    
    # 保存结果
    trainer.save_final_results()
    
    print(f"\n✅ DWA-TD3固定分辨率训练完成!")
    print(f"⏱️ 训练时间: {training_duration}")
    print(f"📊 最终统计:")
    
    if training_stats["episode_rewards"]:
        print(f"   平均奖励: {np.mean(training_stats['episode_rewards'][-50:]):.2f}")
    
    if training_stats["eval_results"]:
        final_success_rate = training_stats["eval_results"][-1]["success_rate"]
        print(f"   最终成功率: {final_success_rate:.2%}")
    
    if training_stats["constraint_violations"]:
        avg_violations = np.mean(training_stats["constraint_violations"][-50:])
        print(f"   平均约束违反: {avg_violations:.2f}")

if __name__ == "__main__":
    main()
