#!/usr/bin/env python3
"""
详细调试物理模拟过程
"""

def debug_physics_detailed():
    """详细调试物理模拟"""
    print("🔬 详细调试物理模拟过程")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        
        # 重置环境
        state = env.reset()
        print(f"初始状态: {state}")
        print(f"起点: {env.start_pos}")
        print(f"终点: {env.target_pos}")
        print(f"空间大小: {env.space_size}")
        
        # 手动调用物理模拟，逐步分析
        print(f"\n🔬 手动调用物理模拟:")
        print(f"调用前状态: {env.state}")
        
        # 使用零动作
        a_T, a_N, mu = 0.0, 0.0, 0.0
        print(f"动作: a_T={a_T}, a_N={a_N}, mu={mu}")
        
        # 获取物理参数
        dt = env.physics_config["time_step"]
        g = env.physics_config["g"]
        print(f"时间步长: {dt}秒")
        print(f"重力加速度: {g} m/s²")
        
        # 当前状态
        V, gamma, psi = env.state[3], env.state[4], env.state[5]
        print(f"当前速度: {V} m/s")
        print(f"倾斜角: {gamma} rad ({np.degrees(gamma):.1f}°)")
        print(f"偏航角: {psi} rad ({np.degrees(psi):.1f}°)")
        
        # 计算运动学方程
        x_dot = V * np.cos(gamma) * np.cos(psi)
        y_dot = V * np.cos(gamma) * np.sin(psi)
        z_dot = V * np.sin(gamma)
        
        print(f"\n速度分量:")
        print(f"x_dot: {x_dot:.3f} m/s")
        print(f"y_dot: {y_dot:.3f} m/s")
        print(f"z_dot: {z_dot:.3f} m/s")
        
        # 计算位置变化
        dx = x_dot * dt
        dy = y_dot * dt
        dz = z_dot * dt
        
        print(f"\n位置变化:")
        print(f"dx: {dx:.3f} m")
        print(f"dy: {dy:.3f} m")
        print(f"dz: {dz:.3f} m")
        
        # 计算新位置（未约束）
        new_x = env.state[0] + dx
        new_y = env.state[1] + dy
        new_z = env.state[2] + dz
        
        print(f"\n新位置（未约束）:")
        print(f"新x: {new_x:.3f}")
        print(f"新y: {new_y:.3f}")
        print(f"新z: {new_z:.3f}")
        
        # 检查边界约束
        print(f"\n边界约束检查:")
        print(f"x边界: [50, {env.space_size - 50}]")
        print(f"y边界: [50, {env.space_size - 50}]")
        print(f"z边界: [50, {env.space_size - 50}]")
        
        constrained_x = np.clip(new_x, 50, env.space_size - 50)
        constrained_y = np.clip(new_y, 50, env.space_size - 50)
        constrained_z = np.clip(new_z, 50, env.space_size - 50)
        
        print(f"约束后x: {constrained_x:.3f}")
        print(f"约束后y: {constrained_y:.3f}")
        print(f"约束后z: {constrained_z:.3f}")
        
        # 检查是否有约束发生
        if constrained_x != new_x:
            print(f"⚠️ x坐标被约束: {new_x:.3f} → {constrained_x:.3f}")
        if constrained_y != new_y:
            print(f"⚠️ y坐标被约束: {new_y:.3f} → {constrained_y:.3f}")
        if constrained_z != new_z:
            print(f"⚠️ z坐标被约束: {new_z:.3f} → {constrained_z:.3f}")
        
        # 现在实际调用物理模拟
        print(f"\n🚀 实际调用物理模拟:")
        env._update_six_dof_dynamics(a_T, a_N, mu)
        
        print(f"调用后状态: {env.state}")
        
        # 计算实际移动距离
        movement = np.linalg.norm(env.state[:3] - state[:3])
        print(f"实际移动距离: {movement:.3f}米")
        
        # 检查是否有异常
        if movement > 10:
            print(f"🚨 移动距离异常！预期约2.5米，实际{movement:.1f}米")
            return False
        
        if env.state[0] == 50 or env.state[2] == 50:
            print(f"🚨 位置被约束到边界！")
            return False
        
        print(f"✅ 物理模拟正常")
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔬 详细物理模拟调试")
    
    success = debug_physics_detailed()
    
    if success:
        print("\n🎯 物理模拟调试完成！")
        print("物理模拟正常工作")
    else:
        print("\n❌ 发现物理模拟问题")
        print("需要修复物理模拟逻辑")

if __name__ == "__main__":
    main()
