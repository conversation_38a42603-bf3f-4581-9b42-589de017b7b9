#!/usr/bin/env python3
"""
测试正确的DWA-RL架构
验证：ResBand选择分辨率 → DWA生成安全动作集 → RL选择最优动作
"""

def test_dwa_rl_architecture():
    """测试DWA-RL架构"""
    print("🏗️ 测试正确的DWA-RL架构")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        print(f"✅ 环境创建成功")
        
        # 测试ResBand自适应方法
        print("\n🎰 测试ResBand自适应方法...")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        resband_agent = LightweightDWARL(env, config)
        resband_agent._method_type = "resband_adaptive"
        print("   ✅ ResBand智能体创建成功")
        
        # 测试固定分辨率方法
        print("\n🔧 测试固定粗分辨率方法...")
        fixed_agent = LightweightDWARL(env, config)
        fixed_agent._method_type = "fixed_coarse"
        print("   ✅ 固定分辨率智能体创建成功")
        
        # 测试单步DWA-RL流程
        print("\n🚀 测试单步DWA-RL流程...")
        state = env.reset()
        print(f"   初始状态: {state[:3]} (位置)")
        print(f"   目标位置: {env.target_pos}")
        print(f"   初始距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        
        # ResBand方法的一步
        print("\n   --- ResBand方法 ---")
        step_info = resband_agent.train_step(state, training_progress=0.1)
        print(f"   安全动作数量: {step_info['safe_actions_count']}")
        print(f"   选择的动作: {step_info['action']}")
        print(f"   分辨率配置: {step_info['resolution_config']}")
        print(f"   奖励: {step_info['reward']:.3f}")
        print(f"   新位置: {step_info['next_state'][:3]}")
        
        # 重置环境
        state = env.reset()
        
        # 固定分辨率方法的一步
        print("\n   --- 固定粗分辨率方法 ---")
        step_info = fixed_agent.train_step(state, training_progress=0.1)
        print(f"   安全动作数量: {step_info['safe_actions_count']}")
        print(f"   选择的动作: {step_info['action']}")
        print(f"   分辨率配置: {step_info['resolution_config']}")
        print(f"   奖励: {step_info['reward']:.3f}")
        print(f"   新位置: {step_info['next_state'][:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 架构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_episode():
    """测试完整episode"""
    print("\n🎯 测试完整episode（从起点到终点）")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        # 运行完整episode
        state = env.reset()
        total_reward = 0
        steps = 0
        done = False
        max_steps = 100  # 限制步数便于测试
        
        print(f"起点: {state[:3]}")
        print(f"终点: {env.target_pos}")
        print(f"初始距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        total_obstacles = len(env.static_obstacles) + len(env.dynamic_obstacles)
        print(f"障碍物数量: {total_obstacles} (静态: {len(env.static_obstacles)}, 动态: {len(env.dynamic_obstacles)})")
        
        print("\n开始episode...")
        
        while not done and steps < max_steps:
            step_info = agent.train_step(state, training_progress=steps/max_steps)
            
            next_state = step_info["next_state"]
            reward = step_info["reward"]
            done = step_info["done"]
            safe_actions_count = step_info["safe_actions_count"]
            
            # 计算距离变化
            current_distance = np.linalg.norm(state[:3] - env.target_pos)
            new_distance = np.linalg.norm(next_state[:3] - env.target_pos)
            distance_change = current_distance - new_distance
            
            state = next_state
            total_reward += reward
            steps += 1
            
            # 每10步打印一次进度
            if steps % 10 == 0:
                print(f"   步骤 {steps}: 位置={state[:3]}, 距离={new_distance:.1f}m, "
                      f"安全动作={safe_actions_count}, 奖励={reward:.2f}")
        
        print(f"\nEpisode结束:")
        print(f"   总步数: {steps}")
        print(f"   总奖励: {total_reward:.2f}")
        print(f"   最终位置: {state[:3]}")
        print(f"   最终距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        print(f"   成功到达: {done and step_info.get('info', {}).get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整episode测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 测试正确的DWA-RL架构")
    
    # 测试架构
    arch_success = test_dwa_rl_architecture()
    
    # 测试完整episode
    episode_success = test_complete_episode()
    
    if arch_success and episode_success:
        print("\n" + "=" * 80)
        print("🎉 DWA-RL架构测试全部通过！")
        print("✅ 验证了正确的流程：")
        print("   1. ResBand选择分辨率配置")
        print("   2. DWA生成安全动作集")
        print("   3. RL从安全集中选择最优动作")
        print("   4. 执行动作，更新状态")
        print("   5. 重复直到到达目标")
        print("\n🚀 现在可以运行正确的ResBand验证实验：")
        print("   python experiments/individual/experiment_2_resband_verification.py --auto")
    else:
        print("\n❌ 架构测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
