#!/usr/bin/env python3
"""
运行完整的ResBand验证实验
耐心等待每个episode完整训练，生成轨迹图和详细分析
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time

# 添加路径
sys.path.append(os.path.dirname(__file__))

def run_single_episode_with_trajectory(method_name, scenario="stage2_complex"):
    """运行单个episode并记录完整轨迹"""
    print(f"\n🎯 运行{method_name}的单个episode (场景: {scenario})")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建环境
        env = PaperSimulationEnvironment(scenario)
        
        # 创建实验实例
        experiment = ResBandVerificationExperiment()
        
        # 创建智能体
        agent = experiment._create_agent(method_name, env)
        
        print(f"✅ 环境和智能体创建成功")
        print(f"起点: {env.start_pos}")
        print(f"终点: {env.target_pos}")
        initial_distance = np.linalg.norm(env.target_pos - env.start_pos)
        print(f"初始距离: {initial_distance:.1f}米")
        
        # 重置环境
        state = env.reset()
        
        # 记录轨迹数据
        trajectory = [state[:3].copy()]
        rewards = []
        actions = []
        resolution_selections = []
        
        total_reward = 0
        step = 0
        start_time = time.time()
        
        print(f"\n📍 Episode进行中 (最大{experiment.config['max_steps_per_episode']}步):")
        print("步骤 | 位置 | 距目标 | 移动 | 奖励 | 累积奖励 | 分辨率")
        print("-" * 80)
        
        while step < experiment.config["max_steps_per_episode"]:
            # 执行一步
            if hasattr(agent, 'train_step'):
                step_info = agent.train_step(state, training_progress=step/1000)
                next_state = step_info['next_state']
                action = step_info['action']
                reward = step_info['reward']
                done = step_info['done']
                info = step_info.get('info', {})
            else:
                action = agent.select_action(state, add_noise=True)
                next_state, reward, done, info = env.step(action)
            
            # 计算移动距离
            movement = np.linalg.norm(next_state[:3] - state[:3])
            distance_to_goal = np.linalg.norm(env.target_pos - next_state[:3])
            total_reward += reward
            
            # 记录数据
            trajectory.append(next_state[:3].copy())
            rewards.append(reward)
            actions.append(action.copy())
            
            # 记录分辨率选择
            if method_name == "resband_adaptive" and hasattr(agent, 'current_resolution_config'):
                res_name = agent.current_resolution_config.get('name', 'Unknown')
                resolution_selections.append(res_name)
            else:
                resolution_selections.append(method_name)
            
            step += 1
            
            # 每10步或重要事件打印进度
            if step % 10 == 0 or done or step <= 5:
                res_display = resolution_selections[-1][:8] if resolution_selections else "N/A"
                print(f"{step:4d} | {next_state[:3]} | {distance_to_goal:6.1f} | {movement:4.1f} | {reward:6.2f} | {total_reward:8.1f} | {res_display}")
            
            # 检查episode结束
            if done:
                end_time = time.time()
                episode_time = end_time - start_time
                
                print(f"\n🏁 Episode结束!")
                print(f"   结束原因: {info}")
                print(f"   总步数: {step}")
                print(f"   总时间: {episode_time:.1f}秒")
                print(f"   总奖励: {total_reward:.2f}")
                print(f"   最终距离: {distance_to_goal:.1f}米")
                
                total_movement = sum(np.linalg.norm(trajectory[i] - trajectory[i-1]) 
                                   for i in range(1, len(trajectory)))
                print(f"   总移动距离: {total_movement:.1f}米")
                print(f"   平均每步移动: {total_movement/step:.1f}米")
                
                if info.get('success'):
                    print(f"   ✅ 成功到达目标!")
                elif info.get('collision'):
                    print(f"   💥 发生碰撞")
                else:
                    print(f"   ⚠️ 其他原因结束")
                
                break
            
            state = next_state
        
        if step >= experiment.config["max_steps_per_episode"]:
            print(f"\n⏰ Episode达到最大步数限制")
            distance_to_goal = np.linalg.norm(env.target_pos - state[:3])
            print(f"   最终距离: {distance_to_goal:.1f}米")
        
        # 生成轨迹可视化
        visualize_episode_trajectory(trajectory, env.start_pos, env.target_pos, 
                                   method_name, scenario, step, total_reward)
        
        return {
            'method': method_name,
            'scenario': scenario,
            'steps': step,
            'total_reward': total_reward,
            'final_distance': distance_to_goal,
            'trajectory': trajectory,
            'rewards': rewards,
            'actions': actions,
            'resolution_selections': resolution_selections,
            'success': info.get('success', False) if 'info' in locals() else False
        }
        
    except Exception as e:
        print(f"❌ Episode运行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def visualize_episode_trajectory(trajectory, start_pos, target_pos, method_name, scenario, steps, total_reward):
    """可视化episode轨迹"""
    print(f"\n📊 生成轨迹可视化图...")
    
    try:
        trajectory = np.array(trajectory)
        
        # 创建3D图
        fig = plt.figure(figsize=(15, 10))
        
        # 3D轨迹图
        ax1 = fig.add_subplot(221, projection='3d')
        
        # 绘制轨迹
        ax1.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
                'b-', linewidth=2, label='轨迹', alpha=0.8)
        
        # 标记起点和终点
        ax1.scatter(*start_pos, color='green', s=200, label='起点', marker='o')
        ax1.scatter(*target_pos, color='red', s=200, label='终点', marker='*')
        
        # 标记轨迹点（每20步一个点）
        step_markers = trajectory[::20]
        ax1.scatter(step_markers[:, 0], step_markers[:, 1], step_markers[:, 2], 
                  color='blue', s=30, alpha=0.6)
        
        ax1.set_xlabel('X (米)')
        ax1.set_ylabel('Y (米)')
        ax1.set_zlabel('Z (米)')
        ax1.set_title(f'{method_name} - 3D轨迹\n{scenario} ({steps}步, 奖励:{total_reward:.1f})')
        ax1.legend()
        
        # XY平面投影
        ax2 = fig.add_subplot(222)
        ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=2, alpha=0.8)
        ax2.scatter(start_pos[0], start_pos[1], color='green', s=100, label='起点')
        ax2.scatter(target_pos[0], target_pos[1], color='red', s=100, label='终点')
        ax2.scatter(step_markers[:, 0], step_markers[:, 1], color='blue', s=20, alpha=0.6)
        ax2.set_xlabel('X (米)')
        ax2.set_ylabel('Y (米)')
        ax2.set_title('XY平面投影')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 距离目标变化
        ax3 = fig.add_subplot(223)
        distances = [np.linalg.norm(target_pos - pos) for pos in trajectory]
        steps_range = list(range(len(distances)))
        ax3.plot(steps_range, distances, 'r-', linewidth=2)
        ax3.set_xlabel('步骤')
        ax3.set_ylabel('距离目标 (米)')
        ax3.set_title('距离目标变化')
        ax3.grid(True, alpha=0.3)
        
        # 移动速度
        ax4 = fig.add_subplot(224)
        movements = [0] + [np.linalg.norm(trajectory[i] - trajectory[i-1]) 
                          for i in range(1, len(trajectory))]
        ax4.plot(steps_range, movements, 'g-', linewidth=2)
        ax4.set_xlabel('步骤')
        ax4.set_ylabel('移动距离 (米)')
        ax4.set_title('每步移动距离')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        filename = f'trajectory_{method_name}_{scenario}_{steps}steps.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"   轨迹图已保存: {filename}")
        
        # 显示统计信息
        total_distance = sum(movements)
        final_distance = distances[-1]
        
        print(f"   轨迹统计:")
        print(f"     总步数: {len(trajectory)}")
        print(f"     总移动距离: {total_distance:.1f}米")
        print(f"     最终距目标: {final_distance:.1f}米")
        print(f"     平均每步: {total_distance/(len(trajectory)-1):.1f}米")
        print(f"     距离减少: {distances[0] - final_distance:.1f}米")
        
        plt.close()
        
    except Exception as e:
        print(f"   ⚠️ 可视化失败: {e}")

def run_multiple_methods_comparison():
    """运行多种方法的对比实验"""
    print(f"\n🎯 运行多种方法对比实验")
    print("=" * 80)
    
    methods = [
        "resband_adaptive",
        "fixed_coarse", 
        "fixed_medium",
        "heuristic_schedule"
    ]
    
    scenarios = ["stage1_simple", "stage2_complex"]
    
    results = []
    
    for scenario in scenarios:
        print(f"\n🏗️ 场景: {scenario}")
        print("=" * 40)
        
        for method in methods:
            print(f"\n🔬 测试方法: {method}")
            result = run_single_episode_with_trajectory(method, scenario)
            if result:
                results.append(result)
                
                # 简要总结
                print(f"   📊 {method} 结果:")
                print(f"      步数: {result['steps']}")
                print(f"      奖励: {result['total_reward']:.1f}")
                print(f"      最终距离: {result['final_distance']:.1f}米")
                print(f"      成功: {result['success']}")
    
    # 生成对比总结
    generate_comparison_summary(results)
    
    return results

def generate_comparison_summary(results):
    """生成对比总结"""
    print(f"\n📊 实验对比总结")
    print("=" * 80)
    
    print("方法 | 场景 | 步数 | 奖励 | 最终距离 | 成功")
    print("-" * 60)
    
    for result in results:
        success_mark = "✅" if result['success'] else "❌"
        print(f"{result['method']:15s} | {result['scenario']:12s} | {result['steps']:4d} | {result['total_reward']:6.1f} | {result['final_distance']:8.1f} | {success_mark}")
    
    # 按方法分组统计
    method_stats = {}
    for result in results:
        method = result['method']
        if method not in method_stats:
            method_stats[method] = []
        method_stats[method].append(result)
    
    print(f"\n📈 方法性能统计:")
    for method, method_results in method_stats.items():
        avg_reward = np.mean([r['total_reward'] for r in method_results])
        avg_steps = np.mean([r['steps'] for r in method_results])
        success_rate = np.mean([r['success'] for r in method_results])
        
        print(f"   {method}:")
        print(f"     平均奖励: {avg_reward:.1f}")
        print(f"     平均步数: {avg_steps:.0f}")
        print(f"     成功率: {success_rate:.1%}")

def main():
    """主函数"""
    print("🎰 完整ResBand验证实验")
    print("耐心等待每个episode完整训练，生成详细轨迹分析")
    print("=" * 80)
    
    # 确认开始
    response = input("\n是否开始完整实验? 这将需要较长时间 (y/n): ")
    if response.lower() != 'y':
        print("实验已取消")
        return
    
    # 运行对比实验
    results = run_multiple_methods_comparison()
    
    print(f"\n🎉 完整实验完成!")
    print(f"📁 轨迹图已保存到当前目录")
    print(f"📊 共完成 {len(results)} 个episode的详细分析")

if __name__ == "__main__":
    main()
