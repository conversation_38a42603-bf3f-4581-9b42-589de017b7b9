#!/usr/bin/env python3
"""
调试完整的训练流程
模拟实验中的训练过程
"""

def debug_training_flow():
    """调试完整的训练流程"""
    print("🔍 调试完整的训练流程")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体（完全模拟实验）
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        print(f"✅ 环境和智能体创建成功")
        
        # 模拟训练episode（完全模拟实验的_train_episode方法）
        max_steps_per_episode = 2000
        
        for episode in range(3):  # 测试3个episodes
            print(f"\n🎯 Episode {episode + 1}")
            print("=" * 40)
            
            # 重置环境
            state = env.reset()
            total_reward = 0
            steps = 0
            done = False
            
            print(f"初始状态: {state[:3]}")
            print(f"目标位置: {env.target_pos}")
            print(f"初始距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
            
            # Episode循环
            while not done and steps < max_steps_per_episode:
                print(f"\n--- 步骤 {steps + 1} ---")
                
                # 调用agent.train_step（完全模拟实验）
                training_progress = (episode + 1) / 600  # 模拟训练进度
                step_info = agent.train_step(state, training_progress=training_progress)
                
                next_state = step_info["next_state"]
                reward = step_info["reward"]
                done = step_info["done"]
                info = step_info.get("info", {})
                action = step_info["action"]
                safe_actions_count = step_info["safe_actions_count"]
                
                # 计算移动距离
                movement = np.linalg.norm(next_state[:3] - state[:3])
                distance_to_goal = np.linalg.norm(next_state[:3] - env.target_pos)
                
                print(f"安全动作数: {safe_actions_count}")
                print(f"选择动作: {action}")
                print(f"新位置: {next_state[:3]}")
                print(f"移动距离: {movement:.3f}m")
                print(f"目标距离: {distance_to_goal:.1f}m")
                print(f"奖励: {reward:.3f}")
                print(f"完成: {done}")
                print(f"信息: {info}")
                
                # 更新状态
                state = next_state
                total_reward += reward
                steps += 1
                
                # 如果done，分析原因
                if done:
                    print(f"\n🔍 Episode结束分析:")
                    if info.get('success'):
                        print(f"   🎯 成功到达目标！")
                    elif info.get('collision'):
                        print(f"   💥 发生碰撞")
                    elif info.get('timeout'):
                        print(f"   ⏰ 超时")
                    elif info.get('stall'):
                        print(f"   🛑 失速")
                    else:
                        print(f"   ❓ 未知原因")
                    break
                
                # 限制调试输出
                if steps >= 5:
                    print(f"   ... (继续运行，限制输出)")
                    break
            
            print(f"\n📊 Episode {episode + 1} 结果:")
            print(f"   总步数: {steps}")
            print(f"   总奖励: {total_reward:.2f}")
            print(f"   最终距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
            print(f"   完成状态: {done}")
            
            # 如果第一个episode就有问题，立即停止
            if episode == 0 and steps == 1 and done:
                print(f"\n❌ 发现问题：第一个episode在第1步就结束！")
                print(f"这与实验中观察到的现象一致")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 训练流程调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_environment_step():
    """调试环境的step方法"""
    print("\n🔍 调试环境的step方法")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        
        # 重置环境
        state = env.reset()
        print(f"初始状态: {state[:3]}")
        print(f"目标位置: {env.target_pos}")
        print(f"初始距离: {np.linalg.norm(state[:3] - env.target_pos):.1f}m")
        print(f"初始步数: {env.step_count}")
        
        # 测试不同的动作
        test_actions = [
            np.array([0.0, 0.0, 0.0]),    # 静止
            np.array([2.0, 5.0, 0.1]),    # 朝向目标
            np.array([4.0, 10.0, 0.2]),   # 中等强度
            np.array([8.0, 25.0, 0.5]),   # 较强动作
        ]
        
        for i, action in enumerate(test_actions):
            print(f"\n--- 测试动作 {i+1}: {action} ---")
            
            # 重置环境
            state = env.reset()
            print(f"重置后状态: {state[:3]}")
            print(f"重置后步数: {env.step_count}")
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            movement = np.linalg.norm(next_state[:3] - state[:3])
            distance = np.linalg.norm(next_state[:3] - env.target_pos)
            
            print(f"执行后状态: {next_state[:3]}")
            print(f"执行后步数: {env.step_count}")
            print(f"移动距离: {movement:.3f}m")
            print(f"目标距离: {distance:.1f}m")
            print(f"奖励: {reward:.3f}")
            print(f"完成: {done}")
            print(f"信息: {info}")
            
            if done:
                print(f"⚠️ 动作 {i+1} 导致episode结束！")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境step调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 完整训练流程调试")
    
    # 调试训练流程
    flow_success = debug_training_flow()
    
    # 调试环境step
    step_success = debug_environment_step()
    
    if flow_success and step_success:
        print("\n" + "=" * 80)
        print("🎯 训练流程调试完成！")
        print("如果发现问题，请查看上述详细输出")
    else:
        print("\n❌ 训练流程调试发现问题")

if __name__ == "__main__":
    main()
