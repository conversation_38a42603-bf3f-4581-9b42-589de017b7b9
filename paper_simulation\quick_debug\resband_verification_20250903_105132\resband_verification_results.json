{"convergence_analysis": {"resband_adaptive": {"episodes": [0, 1, 2], "rewards": [1417.5233935477488, 4.025000000000001, 1.0250000000000001], "success_rates": []}}, "performance_comparison": {"resband_adaptive": {"final_performance": {"avg_reward": 4324.9321306611955, "success_rate": 0.0, "avg_steps": 1001.0, "constraint_violations": 0}, "learning_efficiency": {"convergence_episode": 3, "learning_rate": 0}}}, "efficiency_analysis": {"resband_adaptive": {"computation_times": [4448.986530303955, 4.620790481567383, 13.872385025024414], "memory_usage": [], "avg_computation_time": 1489.1599019368489}}, "resolution_selection": {"resband_adaptive": {"selections": [], "performance_by_resolution": {}}}, "statistical_tests": {"performance_ranking": [["resband_adaptive", 1.7299728522644782]], "convergence_ranking": [["resband_adaptive", 0.25]], "efficiency_ranking": [], "resband_advantages": {}}}