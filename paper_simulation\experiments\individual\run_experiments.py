#!/usr/bin/env python3
"""
实验快速启动脚本
提供交互式菜单，方便选择和运行各个独立实验

运行方式:
python run_experiments.py
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header():
    """打印标题"""
    print("🎯 独立实验快速启动")
    print("=" * 60)
    print("📋 选择要运行的实验模块")
    print("=" * 60)

def print_experiments():
    """打印实验列表"""
    experiments = [
        {
            "id": "1",
            "name": "DWA-TD3算法",
            "file": "01_dwa_td3.py",
            "description": "本文核心方法，DWA安全约束+TD3强化学习",
            "time": "2-4小时",
            "episodes": "250"
        },
        {
            "id": "2", 
            "name": "纯TD3算法",
            "file": "02_pure_td3.py",
            "description": "基线对比，纯强化学习无安全约束",
            "time": "2-4小时",
            "episodes": "250"
        },
        {
            "id": "3",
            "name": "传统DWA算法", 
            "file": "03_traditional_dwa.py",
            "description": "经典方法，无强化学习组件",
            "time": "30-60分钟",
            "episodes": "50次测试"
        },
        {
            "id": "4",
            "name": "PPO约束算法",
            "file": "04_ppo_constrained.py", 
            "description": "基线对比，PPO+约束优化",
            "time": "2-4小时",
            "episodes": "250"
        },
        {
            "id": "5",
            "name": "ResBand算法",
            "file": "05_resband_ucb.py",
            "description": "本文方法，UCB多臂老虎机分辨率选择",
            "time": "1.5-3小时", 
            "episodes": "200"
        },
        {
            "id": "6",
            "name": "固定粗分辨率",
            "file": "06_fixed_coarse.py",
            "description": "ResBand对比基线",
            "time": "1.5-3小时",
            "episodes": "200"
        },
        {
            "id": "7",
            "name": "固定中等分辨率",
            "file": "07_fixed_medium.py", 
            "description": "ResBand对比基线",
            "time": "1.5-3小时",
            "episodes": "200"
        },
        {
            "id": "8",
            "name": "固定精细分辨率",
            "file": "08_fixed_fine.py",
            "description": "ResBand对比基线", 
            "time": "1.5-3小时",
            "episodes": "200"
        },
        {
            "id": "9",
            "name": "启发式调度",
            "file": "09_heuristic.py",
            "description": "ResBand对比基线",
            "time": "1.5-3小时",
            "episodes": "200"
        },
        {
            "id": "10",
            "name": "Stage2复杂场景",
            "file": "10_stage2_complex.py",
            "description": "高密度静态障碍物环境",
            "time": "2-3小时",
            "episodes": "180"
        },
        {
            "id": "11", 
            "name": "Stage3动态环境",
            "file": "11_stage3_dynamic.py",
            "description": "高密度动态障碍物环境",
            "time": "2-3小时", 
            "episodes": "140"
        }
    ]
    
    print("\n📋 可用实验:")
    for exp in experiments:
        print(f"  {exp['id']:2s}. {exp['name']:<20s} - {exp['description']}")
        print(f"      文件: {exp['file']:<25s} 时间: {exp['time']:<15s} Episodes: {exp['episodes']}")
        print()
    
    return experiments

def print_batch_options():
    """打印批量运行选项"""
    print("🚀 批量运行选项:")
    print("  b1. 基线对比实验 (1,2,3,4) - 约8-12小时")
    print("  b2. ResBand验证实验 (5,6,7,8,9) - 约7-15小时") 
    print("  b3. 复杂度验证实验 (10,11) - 约4-6小时")
    print("  b4. 核心方法验证 (1,5) - 约3-7小时")
    print("  ba. 所有实验 (1-11) - 约20-30小时")
    print()

def run_experiment(exp_file: str, resume: bool = False):
    """运行单个实验"""
    script_path = Path(__file__).parent / exp_file
    
    if not script_path.exists():
        print(f"❌ 实验文件不存在: {exp_file}")
        return False
    
    cmd = [sys.executable, str(script_path)]
    if resume:
        cmd.append("--resume")
    
    print(f"🚀 启动实验: {exp_file}")
    print(f"📝 命令: {' '.join(cmd)}")
    print("⚠️  按Ctrl+C可以优雅中断训练")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, cwd=script_path.parent)
        if result.returncode == 0:
            print(f"✅ 实验完成: {exp_file}")
            return True
        else:
            print(f"❌ 实验失败: {exp_file} (返回码: {result.returncode})")
            return False
    except KeyboardInterrupt:
        print(f"\n⚠️  实验被中断: {exp_file}")
        print(f"🔄 恢复命令: python {exp_file} --resume")
        return False
    except Exception as e:
        print(f"❌ 运行实验时发生错误: {e}")
        return False

def run_batch_experiments(exp_ids: list, experiments: list, resume: bool = False):
    """批量运行实验"""
    print(f"🚀 开始批量运行 {len(exp_ids)} 个实验")
    print("⚠️  注意: 批量运行可能需要很长时间")
    print("⚠️  建议在后台运行或使用screen/tmux")
    
    confirm = input("\n确认开始批量运行? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 取消批量运行")
        return
    
    success_count = 0
    failed_experiments = []
    
    for i, exp_id in enumerate(exp_ids, 1):
        exp = next((e for e in experiments if e["id"] == exp_id), None)
        if not exp:
            print(f"❌ 找不到实验ID: {exp_id}")
            continue
        
        print(f"\n📊 进度: {i}/{len(exp_ids)} - {exp['name']}")
        
        success = run_experiment(exp["file"], resume)
        if success:
            success_count += 1
        else:
            failed_experiments.append(exp["name"])
        
        print(f"✅ 已完成: {success_count}/{i}")
        if failed_experiments:
            print(f"❌ 失败: {failed_experiments}")
    
    print(f"\n🎉 批量运行完成!")
    print(f"✅ 成功: {success_count}/{len(exp_ids)}")
    if failed_experiments:
        print(f"❌ 失败: {failed_experiments}")

def main():
    """主函数"""
    print_header()
    
    while True:
        experiments = print_experiments()
        print_batch_options()
        
        print("🔧 其他选项:")
        print("  r. 恢复模式 - 查找并恢复中断的训练")
        print("  q. 退出")
        print()
        
        choice = input("请选择实验 (1-11, b1-ba, r, q): ").strip().lower()
        
        if choice == 'q':
            print("👋 再见!")
            break
        elif choice == 'r':
            print("🔄 恢复模式")
            exp_choice = input("选择要恢复的实验 (1-11): ").strip()
            exp = next((e for e in experiments if e["id"] == exp_choice), None)
            if exp:
                run_experiment(exp["file"], resume=True)
            else:
                print("❌ 无效的实验选择")
        elif choice in [str(i) for i in range(1, 12)]:
            # 单个实验
            exp = next((e for e in experiments if e["id"] == choice), None)
            if exp:
                resume = input(f"是否恢复训练 {exp['name']}? (y/N): ").strip().lower() == 'y'
                run_experiment(exp["file"], resume)
            else:
                print("❌ 无效的实验选择")
        elif choice == 'b1':
            # 基线对比实验
            run_batch_experiments(["1", "2", "3", "4"], experiments)
        elif choice == 'b2':
            # ResBand验证实验
            run_batch_experiments(["5", "6", "7", "8", "9"], experiments)
        elif choice == 'b3':
            # 复杂度验证实验
            run_batch_experiments(["10", "11"], experiments)
        elif choice == 'b4':
            # 核心方法验证
            run_batch_experiments(["1", "5"], experiments)
        elif choice == 'ba':
            # 所有实验
            run_batch_experiments([str(i) for i in range(1, 12)], experiments)
        else:
            print("❌ 无效的选择，请重新输入")
        
        print("\n" + "="*60)
        continue_choice = input("继续选择其他实验? (Y/n): ").strip().lower()
        if continue_choice == 'n':
            break

if __name__ == "__main__":
    main()
