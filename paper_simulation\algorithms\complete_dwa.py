#!/usr/bin/env python3
"""
完整的DWA实现 - 直接照搬巡飞简化ver的设计
确保每个episode都能跑到终点
"""

import numpy as np

class CompleteDWA:
    """完整的DWA实现 - 照搬巡飞简化ver"""
    
    def __init__(self, resolution_config=None):
        """初始化DWA"""
        # 物理参数 - 完全匹配巡飞简化ver
        self.V_min = 15.0
        self.V_max = 60.0
        self.V_cruise = 25.0
        self.a_T_max = 8.0
        self.a_N_max = 39.24
        self.gamma_max = 1.047  # 60°
        self.g = 9.81
        
        # 分辨率配置
        if resolution_config:
            self.a_T_resolution = resolution_config.get("delta_a_T", 4.0)
            self.a_N_resolution = resolution_config.get("delta_a_N", 10.0)
            self.mu_resolution = resolution_config.get("delta_mu", 0.5)
        else:
            self.a_T_resolution = 4.0
            self.a_N_resolution = 10.0
            self.mu_resolution = 0.5
        
        # 预测参数 - 增加预测时间以提高安全性
        self.predict_time = 1.5  # 1.5秒预测时间，提高安全性
        self.dt = 0.1           # 0.1秒时间步长
        
        # 评价权重 - 调整为更安全的配置
        self.alpha = 0.3   # 目标方向权重（降低）
        self.beta = 0.2    # 速度权重（鼓励巡航速度）
        self.distance_weight = 0.2   # 距离权重（降低）
        self.delta = 0.3   # 障碍物权重（大幅提高，确保安全优先）
    
    def generate_safe_control_set(self, current_state, obstacles, goal, max_actions=10, bounds=None):
        """
        生成安全控制输入集合 - 完全照搬巡飞简化ver
        
        Args:
            current_state: [x, y, z, V, γ, ψ] - 当前状态
            obstacles: 障碍物列表
            goal: 目标位置
            max_actions: 最大动作数量
            bounds: 环境边界
            
        Returns:
            安全的控制输入列表，按评价分数排序
        """
        x, y, z, V, gamma, psi = current_state
        
        # 计算动态窗口
        dw = self._calc_dynamic_window(current_state)
        
        safe_controls_with_scores = []

        # 遍历控制输入空间 - 完全照搬巡飞简化ver的逻辑
        a_T_range = np.arange(dw['a_T_min'], dw['a_T_max'] + self.a_T_resolution/2,
                             self.a_T_resolution)
        a_N_range = np.arange(dw['a_N_min'], dw['a_N_max'] + self.a_N_resolution/2,
                             self.a_N_resolution)
        mu_range = np.arange(dw['mu_min'], dw['mu_max'] + self.mu_resolution/2,
                            self.mu_resolution)

        # 确保不超出约束范围
        a_T_range = a_T_range[a_T_range <= dw['a_T_max']]
        a_N_range = a_N_range[a_N_range <= dw['a_N_max']]
        mu_range = mu_range[mu_range <= dw['mu_max']]

        # 计算候选控制总数
        total_candidates = len(a_T_range) * len(a_N_range) * len(mu_range)

        for a_T in a_T_range:
            for a_N in a_N_range:
                for mu in mu_range:
                    control = np.array([a_T, a_N, mu])

                    # 第一步：硬性安全筛选 - 绝对杜绝不安全动作
                    if self._is_safe_control(control, current_state, obstacles, bounds):
                        # 第二步：只对安全动作计算评价分数
                        score = self.evaluate_control(control, current_state, goal, obstacles)
                        safe_controls_with_scores.append((control, score))

        # 按分数排序（降序）- 完全照搬巡飞简化ver
        safe_controls_with_scores.sort(key=lambda x: x[1], reverse=True)

        # 返回前max_actions个控制输入
        result = [control for control, score in safe_controls_with_scores[:max_actions]]
        
        # 如果没有安全控制，返回紧急控制
        if not result:
            emergency_control = np.array([0.0, 0.0, 0.0])
            result = [emergency_control]
        
        return result
    
    def _calc_dynamic_window(self, current_state):
        """计算动态窗口 - 照搬巡飞简化ver"""
        x, y, z, V, gamma, psi = current_state
        
        # 基于当前状态和约束计算可达控制输入范围
        dw = {
            'a_T_min': -self.a_T_max,
            'a_T_max': self.a_T_max,
            'a_N_min': -self.a_N_max,
            'a_N_max': self.a_N_max,
            'mu_min': -np.pi/2,
            'mu_max': np.pi/2
        }
        
        # 考虑速度约束对切向加速度的限制
        if V <= self.V_min + 1.0:  # 接近失速速度
            dw['a_T_min'] = max(dw['a_T_min'], 0.0)  # 只能加速
        elif V >= self.V_max - 1.0:  # 接近最大速度
            dw['a_T_max'] = min(dw['a_T_max'], 0.0)  # 只能减速
        
        # 考虑航迹倾斜角约束
        if gamma >= self.gamma_max - 0.1:  # 接近最大倾斜角
            pass  # 可以添加更多约束
        elif gamma <= -self.gamma_max + 0.1:  # 接近最小倾斜角
            pass  # 可以添加更多约束
        
        return dw
    
    def _is_safe_control(self, control, current_state, obstacles, bounds):
        """检查控制输入是否安全 - 完全照搬巡飞简化ver的严格安全检查"""
        # 预测轨迹
        predicted_trajectory = self._predict_trajectory(control, current_state, self.predict_time)

        # 分离静态和动态障碍物 - 照搬巡飞简化ver
        static_obstacles = []
        dynamic_obstacles = []

        for obs in obstacles:
            if obs.get('type') == 'dynamic':
                dynamic_obstacles.append(obs)
            else:
                static_obstacles.append(obs)

        # 检查轨迹是否与障碍物碰撞 - 完全照搬巡飞简化ver
        for i, state_point in enumerate(predicted_trajectory):
            pos = state_point[:3]  # 提取位置部分 [x, y, z]
            time_step = i * self.dt  # 当前预测时间点

            # 检查静态障碍物碰撞
            for obs in static_obstacles:
                if obs.get('type') == 'static':
                    obs_center = np.array(obs['position'])
                    obs_radius = obs['radius']
                else:
                    # 兼容其他格式
                    obs_center = np.array(obs.get('center', obs.get('position', [0, 0, 0])))
                    obs_radius = obs.get('radius', 50)

                dist = np.linalg.norm(pos - obs_center)
                # 使用比环境更保守的安全距离：障碍物半径 + 55米（增加5米裕度）
                if dist <= obs_radius + 55.0:
                    return False

            # 检查动态障碍物碰撞（预测障碍物在time_step时刻的位置）
            for obs in dynamic_obstacles:
                # 预测动态障碍物在time_step时刻的位置
                predicted_obs_center = self._predict_dynamic_obstacle_position(obs, time_step)
                obs_radius = obs['obstacle_radius']

                dist = np.linalg.norm(pos - predicted_obs_center)
                # 使用比环境更保守的安全距离：障碍物半径 + 55米（增加5米裕度）
                if dist <= obs_radius + 55.0:
                    return False

            # 检查边界约束 - 照搬巡飞简化ver但使用我们的边界
            if bounds is not None:
                if (pos[0] < 50 or pos[0] > bounds[0] - 50 or
                    pos[1] < 50 or pos[1] > bounds[1] - 50 or
                    pos[2] < 50 or pos[2] > bounds[2] - 50):
                    return False

        return True

    def _predict_dynamic_obstacle_position(self, obstacle, future_time):
        """预测动态障碍物在未来时刻的位置 - 完全照搬巡飞简化ver"""
        motion_type = obstacle.get('motion_type', 'static')

        if motion_type == 'circular_3d':
            # 3D圆周运动预测
            circle_center = np.array(obstacle['circle_center'])
            motion_radius = obstacle['motion_radius']
            angular_speed = obstacle['angular_speed']
            initial_angle = obstacle['initial_angle']
            perpendicular1 = np.array(obstacle['perpendicular1'])
            perpendicular2 = np.array(obstacle['perpendicular2'])

            # 计算当前角度
            current_angle = initial_angle + angular_speed * future_time

            # 计算在圆周上的位置
            predicted_pos = (circle_center +
                           motion_radius * np.cos(current_angle) * perpendicular1 +
                           motion_radius * np.sin(current_angle) * perpendicular2)

            return predicted_pos

        elif motion_type == 'linear':
            # 线性运动预测（如果有的话）
            current_center = np.array(obstacle['circle_center'])
            velocity = obstacle.get('velocity', np.array([0, 0, 0]))
            predicted_pos = current_center + velocity * future_time
            return predicted_pos

        else:
            # 静态或未知类型，返回当前位置
            return np.array(obstacle['circle_center'])
    
    def _predict_trajectory(self, control, current_state, predict_time):
        """预测轨迹 - 照搬巡飞简化ver"""
        trajectory = []
        state = current_state.copy()
        
        steps = int(predict_time / self.dt)
        a_T, a_N, mu = control
        
        for _ in range(steps):
            x, y, z, V, gamma, psi = state
            
            # 运动学方程
            x += V * np.cos(gamma) * np.cos(psi) * self.dt
            y += V * np.cos(gamma) * np.sin(psi) * self.dt
            z += V * np.sin(gamma) * self.dt
            
            # 动力学方程
            V += a_T * self.dt
            V = np.clip(V, self.V_min, self.V_max)
            
            if V > 0.1:
                gamma += (a_N * np.cos(mu) - self.g * np.cos(gamma)) / V * self.dt
                psi += (a_N * np.sin(mu)) / (V * np.cos(gamma)) * self.dt
            
            gamma = np.clip(gamma, -self.gamma_max, self.gamma_max)
            psi = psi % (2 * np.pi)
            
            state = np.array([x, y, z, V, gamma, psi])
            trajectory.append(state.copy())
        
        return trajectory
    
    def evaluate_control(self, control, current_state, goal, obstacles):
        """评价控制输入 - 照搬巡飞简化ver"""
        # 预测最终状态
        final_state = self._predict_final_state(control, current_state, self.predict_time)
        final_pos = final_state[:3]
        final_V = final_state[3]
        
        # 计算到目标的距离
        goal_dist = np.linalg.norm(final_pos - goal)
        
        # 1. 方向评价（朝向目标）- 完全照搬巡飞简化ver
        goal_direction = goal - current_state[:3]
        if np.linalg.norm(goal_direction) > 0:
            goal_direction = goal_direction / np.linalg.norm(goal_direction)

        # 预测运动方向（使用预测轨迹）- 照搬巡飞简化ver
        predicted_trajectory = self._predict_trajectory(control, current_state, self.predict_time)
        if len(predicted_trajectory) > 1:
            # 计算预测的运动方向（只使用位置部分）
            movement = predicted_trajectory[-1][:3] - predicted_trajectory[0][:3]
            if np.linalg.norm(movement) > 1e-6:
                movement_direction = movement / np.linalg.norm(movement)
                heading_score = max(0, np.dot(movement_direction, goal_direction))
            else:
                heading_score = 0.0  # 没有运动
        else:
            heading_score = 0.0  # 预测失败
        
        # 2. 速度评价（符合现实巡飞弹运动规律的速度策略）- 完全照搬巡飞简化ver
        optimal_speed = self._calculate_realistic_optimal_speed(goal_dist)
        speed_score = 1.0 - abs(final_V - optimal_speed) / (self.V_max - self.V_min)
        
        # 3. 距离评价
        distance_score = 1.0 / (1.0 + goal_dist / 100.0)
        
        # 4. 安全评价
        min_obs_dist = float('inf')
        for obs in obstacles:
            # 处理不同类型的障碍物
            if obs.get('type') == 'static':
                obs_center = np.array(obs['position'])
                obs_radius = obs['radius']
            elif obs.get('type') == 'dynamic':
                obs_center = np.array(obs['circle_center'])
                obs_radius = obs['obstacle_radius']
            else:
                obs_center = np.array(obs.get('center', obs.get('position', [0, 0, 0])))
                obs_radius = obs.get('radius', obs.get('obstacle_radius', 50))

            dist = np.linalg.norm(final_pos - obs_center) - obs_radius
            min_obs_dist = min(min_obs_dist, dist)
        
        safety_score = min(min_obs_dist / 50.0, 1.0)
        
        # 综合评价
        total_score = (self.alpha * heading_score +
                      self.beta * speed_score +
                      self.distance_weight * distance_score +
                      self.delta * safety_score)
        
        return total_score
    
    def _predict_final_state(self, control, current_state, predict_time):
        """预测最终状态"""
        trajectory = self._predict_trajectory(control, current_state, predict_time)
        return trajectory[-1] if trajectory else current_state
    
    def _calculate_realistic_optimal_speed(self, goal_dist):
        """计算符合现实巡飞弹运动规律的最优速度 - 完全照搬巡飞简化ver"""
        # 基于实际巡飞弹任务的速度策略
        if goal_dist > 1500:
            return 45.0  # 远距离高速巡航，快速接近
        elif goal_dist > 800:
            return 35.0  # 中远距离中高速
        elif goal_dist > 400:
            return 28.0  # 中距离正常速度
        elif goal_dist > 150:
            return 22.0  # 近距离减速，准备精确制导
        else:
            return 18.0  # 很近时慢速精确接近
