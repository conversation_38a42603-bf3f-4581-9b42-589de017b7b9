"""
测试ResBand实验脚本
Test ResBand Experiment Script

验证导入和基本功能是否正常
"""

import os
import sys

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        print("   ✅ PaperSimulationEnvironment 导入成功")
    except Exception as e:
        print(f"   ❌ PaperSimulationEnvironment 导入失败: {e}")
        return False
    
    try:
        from algorithms.simplified_resband import EnhancedResBand
        print("   ✅ EnhancedResBand 导入成功")
    except Exception as e:
        print(f"   ❌ EnhancedResBand 导入失败: {e}")
        return False
    
    try:
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        print("   ✅ LightweightDWARL 导入成功")
    except Exception as e:
        print(f"   ❌ LightweightDWARL 导入失败: {e}")
        return False
    
    try:
        from algorithms.correct_reward_system import CorrectRewardSystem
        print("   ✅ CorrectRewardSystem 导入成功")
    except Exception as e:
        print(f"   ❌ CorrectRewardSystem 导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    try:
        # 测试环境创建
        from environments.paper_environment import PaperSimulationEnvironment
        env = PaperSimulationEnvironment("stage1_simple")
        print("   ✅ 环境创建成功")
        
        # 测试环境重置
        state = env.reset()
        print(f"   ✅ 环境重置成功，状态维度: {len(state)}")
        
        # 测试环境步骤
        import numpy as np
        action = np.array([1.0, 0.5, 0.1])
        next_state, reward, done, info = env.step(action)
        print(f"   ✅ 环境步骤成功，奖励: {reward:.3f}")
        
        # 测试ResBand创建
        from algorithms.simplified_resband import EnhancedResBand
        resband = EnhancedResBand()
        print("   ✅ ResBand创建成功")
        
        # 测试分辨率选择
        scene_features = np.array([0.3, 0.5, 0.7, 0.2, 0.4])
        resolution_config = resband.select_resolution(scene_features)
        print(f"   ✅ 分辨率选择成功: {resolution_config}")
        
        # 测试框架创建
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        framework = LightweightDWARL(env, config)
        print("   ✅ 框架创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_experiment_class():
    """测试实验类"""
    print("\n🧪 测试实验类...")
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建实验实例
        experiment = ResBandVerificationExperiment("test_output")
        print("   ✅ 实验类创建成功")
        
        # 测试检查点功能
        experiment._save_checkpoint("test_method")
        print("   ✅ 检查点保存成功")
        
        completed = experiment._load_checkpoint()
        print(f"   ✅ 检查点加载成功: {completed}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 实验类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试ResBand实验脚本")
    print("=" * 60)
    
    # 测试导入
    imports_ok = test_imports()
    
    if not imports_ok:
        print("\n❌ 导入测试失败，请检查依赖")
        return
    
    # 测试基本功能
    functionality_ok = test_basic_functionality()
    
    if not functionality_ok:
        print("\n❌ 基本功能测试失败")
        return
    
    # 测试实验类
    experiment_ok = test_experiment_class()
    
    if not experiment_ok:
        print("\n❌ 实验类测试失败")
        return
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("✅ ResBand实验脚本准备就绪")
    print("\n📋 运行实验的方式:")
    print("   1. 交互模式: python experiments/individual/experiment_2_resband_verification.py")
    print("   2. 自动模式: python experiments/individual/experiment_2_resband_verification.py --auto")
    print("   3. 中止后重启: 重新运行相同命令即可从中断处继续")

if __name__ == "__main__":
    main()
