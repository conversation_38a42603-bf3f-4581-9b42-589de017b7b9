#!/usr/bin/env python3
"""
合理的ResBand测试
"""

def run_proper_resband_test():
    """运行合理的ResBand测试"""
    print("🎰 运行合理的ResBand测试")
    print("=" * 60)
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建合理的实验配置
        experiment = ResBandVerificationExperiment("proper_test_output")
        
        # 修改配置为合理的测试参数
        experiment.config.update({
            "training_episodes": 50,  # 增加到50个episodes
            "evaluation_episodes": 10,  # 10个评估episodes
            "max_steps_per_episode": 200,  # 增加到200步
            "episodes_per_stage": 15,  # 每个阶段15个episodes
            "comparison_methods": ["resband_adaptive", "fixed_coarse", "fixed_medium"]  # 3种方法
        })
        
        print(f"📋 合理配置:")
        print(f"   - 训练episodes: {experiment.config['training_episodes']}")
        print(f"   - 每阶段episodes: {experiment.config['episodes_per_stage']}")
        print(f"   - 最大步数: {experiment.config['max_steps_per_episode']}")
        print(f"   - 对比方法: {experiment.config['comparison_methods']}")
        print(f"   - 预计时间: 约15-20分钟")
        
        # 确认运行
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == '--auto':
            print("🤖 自动模式：开始实验")
        else:
            response = input("\n是否开始合理规模的实验？(y/N): ")
            if response.lower() != 'y':
                print("❌ 用户取消实验")
                return False
        
        # 运行实验
        print("\n🚀 开始合理规模实验...")
        results = experiment.run_experiment()
        
        if results:
            print("\n🎉 合理规模实验完成！")
            print("📊 结果摘要:")
            
            for method in experiment.config["comparison_methods"]:
                if method in results.get("performance_comparison", {}):
                    perf = results["performance_comparison"][method]["final_performance"]
                    print(f"   {method}:")
                    print(f"     - 平均奖励: {perf.get('avg_reward', 0):.2f}")
                    print(f"     - 成功率: {perf.get('success_rate', 0):.2%}")
                    print(f"     - 约束违反: {perf.get('constraint_violations', 0):.1f}")
            
            # 显示ResBand的分辨率选择分析
            if "resolution_analysis" in results:
                print(f"\n🎯 ResBand分辨率选择分析:")
                selections = results["resolution_analysis"].get("selections", [])
                if selections:
                    from collections import Counter
                    selection_counts = Counter(selections)
                    total = len(selections)
                    for resolution, count in selection_counts.items():
                        percentage = count / total * 100
                        resolution_names = {0: "粗分辨率", 1: "中等分辨率", 2: "精细分辨率"}
                        print(f"     - {resolution_names.get(resolution, f'分辨率{resolution}')}: {percentage:.1f}% ({count}/{total})")
        else:
            print("❌ 实验被取消或失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 合理规模实验失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = run_proper_resband_test()
    
    if success:
        print("\n✅ 合理规模测试完成！")
        print("\n🚀 如需运行完整实验（300 episodes，2-3小时）:")
        print("   python experiments/individual/experiment_2_resband_verification.py --auto")
        print("\n📊 实验验证了:")
        print("   ✅ ResBand自适应分辨率选择")
        print("   ✅ 渐进式训练（简单→复杂→动态）")
        print("   ✅ 与固定分辨率方法的对比")
        print("   ✅ 正确的巡飞弹场景和奖励函数")
    else:
        print("\n❌ 合理规模测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
