"""
批量训练脚本
Batch Training Script

按顺序训练所有基线算法和本文方法，生成完整的对比实验数据
"""

import os
import sys
import subprocess
import json
from datetime import datetime
import time

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class BatchTrainer:
    """批量训练管理器"""
    
    def __init__(self, output_dir="results/batch_training"):
        """
        初始化批量训练器
        
        Args:
            output_dir: 输出目录
        """
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"batch_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 训练脚本配置
        self.training_scripts = [
            {
                "name": "传统DWA",
                "script": "train_traditional_dwa.py",
                "description": "固定参数的传统动态窗口法",
                "estimated_time": "30分钟"
            },
            {
                "name": "纯TD3",
                "script": "train_pure_td3.py", 
                "description": "不带约束的纯强化学习",
                "estimated_time": "2小时"
            },
            {
                "name": "DWA-TD3固定",
                "script": "train_dwa_td3_fixed.py",
                "description": "固定分辨率的DWA-TD3组合",
                "estimated_time": "2.5小时"
            },
            {
                "name": "PPO约束",
                "script": "train_ppo_constrained.py",
                "description": "基于约束的PPO算法",
                "estimated_time": "2小时"
            },
            {
                "name": "本文方法",
                "script": "train_our_method.py",
                "description": "DWA-RL + ResBand + MLACF集成框架",
                "estimated_time": "3小时"
            }
        ]
        
        # 训练结果
        self.training_results = {
            "start_time": None,
            "end_time": None,
            "total_duration": None,
            "results": []
        }
        
        print(f"🚀 批量训练管理器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📋 计划训练 {len(self.training_scripts)} 个算法")
    
    def run_all_training(self):
        """运行所有训练脚本"""
        print(f"\n🎯 开始批量训练所有算法")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.training_results["start_time"] = datetime.now().isoformat()
        
        # 显示训练计划
        self.show_training_plan()
        
        # 逐个运行训练脚本
        for i, script_config in enumerate(self.training_scripts):
            print(f"\n{'='*60}")
            print(f"🔄 训练进度: {i+1}/{len(self.training_scripts)}")
            print(f"📝 当前算法: {script_config['name']}")
            print(f"📄 脚本: {script_config['script']}")
            print(f"📖 描述: {script_config['description']}")
            print(f"⏱️ 预计时间: {script_config['estimated_time']}")
            print(f"{'='*60}")
            
            # 运行训练脚本
            result = self.run_single_training(script_config)
            self.training_results["results"].append(result)
            
            # 显示结果
            if result["success"]:
                print(f"✅ {script_config['name']} 训练完成")
                print(f"⏱️ 实际用时: {result['duration']}")
            else:
                print(f"❌ {script_config['name']} 训练失败")
                print(f"🔍 错误信息: {result['error']}")
        
        self.training_results["end_time"] = datetime.now().isoformat()
        
        # 计算总时间
        start_time = datetime.fromisoformat(self.training_results["start_time"])
        end_time = datetime.fromisoformat(self.training_results["end_time"])
        total_duration = end_time - start_time
        self.training_results["total_duration"] = str(total_duration)
        
        # 保存结果
        self.save_batch_results()
        
        # 显示总结
        self.show_training_summary()
        
        return self.training_results
    
    def show_training_plan(self):
        """显示训练计划"""
        print(f"\n📋 训练计划:")
        total_estimated_hours = 0
        
        for i, script_config in enumerate(self.training_scripts):
            print(f"   {i+1}. {script_config['name']}")
            print(f"      脚本: {script_config['script']}")
            print(f"      描述: {script_config['description']}")
            print(f"      预计时间: {script_config['estimated_time']}")
            print()
            
            # 简单的时间估算（仅供参考）
            if "小时" in script_config['estimated_time']:
                hours = float(script_config['estimated_time'].split('小时')[0])
                total_estimated_hours += hours
            elif "分钟" in script_config['estimated_time']:
                minutes = float(script_config['estimated_time'].split('分钟')[0])
                total_estimated_hours += minutes / 60
        
        print(f"📊 预计总时间: 约 {total_estimated_hours:.1f} 小时")
        print(f"🎯 预计完成时间: {(datetime.now() + datetime.timedelta(hours=total_estimated_hours)).strftime('%Y-%m-%d %H:%M:%S')}")
    
    def run_single_training(self, script_config):
        """运行单个训练脚本"""
        script_path = os.path.join(os.path.dirname(__file__), script_config["script"])
        
        result = {
            "name": script_config["name"],
            "script": script_config["script"],
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "duration": None,
            "success": False,
            "error": None,
            "output": None
        }
        
        try:
            print(f"🚀 开始运行: {script_config['script']}")
            
            # 运行训练脚本
            start_time = datetime.now()
            
            process = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=4*3600  # 4小时超时
            )
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            result["end_time"] = end_time.isoformat()
            result["duration"] = str(duration)
            result["output"] = process.stdout
            
            if process.returncode == 0:
                result["success"] = True
                print(f"✅ {script_config['name']} 训练成功完成")
            else:
                result["success"] = False
                result["error"] = process.stderr
                print(f"❌ {script_config['name']} 训练失败")
                print(f"错误输出: {process.stderr}")
        
        except subprocess.TimeoutExpired:
            result["error"] = "训练超时（4小时）"
            print(f"⏰ {script_config['name']} 训练超时")
        
        except Exception as e:
            result["error"] = str(e)
            print(f"💥 {script_config['name']} 运行异常: {e}")
        
        return result
    
    def save_batch_results(self):
        """保存批量训练结果"""
        # 保存详细结果
        results_path = os.path.join(self.output_dir, "batch_training_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_results, f, indent=2, ensure_ascii=False)
        
        # 生成简化报告
        self.generate_training_report()
        
        print(f"📊 批量训练结果保存至: {self.output_dir}")
    
    def generate_training_report(self):
        """生成训练报告"""
        report_path = os.path.join(self.output_dir, "training_report.md")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 批量训练报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 训练概述\n\n")
            f.write(f"- **开始时间**: {self.training_results['start_time']}\n")
            f.write(f"- **结束时间**: {self.training_results['end_time']}\n")
            f.write(f"- **总耗时**: {self.training_results['total_duration']}\n")
            f.write(f"- **训练算法数**: {len(self.training_scripts)}\n\n")
            
            # 成功率统计
            successful_count = sum(1 for r in self.training_results["results"] if r["success"])
            success_rate = successful_count / len(self.training_results["results"]) * 100
            
            f.write(f"- **成功训练**: {successful_count}/{len(self.training_scripts)} ({success_rate:.1f}%)\n\n")
            
            f.write("## 训练结果详情\n\n")
            
            for i, result in enumerate(self.training_results["results"]):
                f.write(f"### {i+1}. {result['name']}\n\n")
                f.write(f"- **脚本**: {result['script']}\n")
                f.write(f"- **状态**: {'✅ 成功' if result['success'] else '❌ 失败'}\n")
                f.write(f"- **开始时间**: {result['start_time']}\n")
                f.write(f"- **结束时间**: {result['end_time']}\n")
                f.write(f"- **耗时**: {result['duration']}\n")
                
                if not result['success'] and result['error']:
                    f.write(f"- **错误信息**: {result['error']}\n")
                
                f.write("\n")
            
            f.write("## 下一步建议\n\n")
            
            if successful_count == len(self.training_scripts):
                f.write("🎉 **所有算法训练成功完成！**\n\n")
                f.write("建议下一步操作：\n")
                f.write("1. 运行性能对比分析\n")
                f.write("2. 生成论文图表\n")
                f.write("3. 进行统计显著性检验\n")
                f.write("4. 撰写实验结果章节\n")
            else:
                f.write("⚠️ **部分算法训练失败**\n\n")
                f.write("建议检查失败的算法并重新训练。\n")
    
    def show_training_summary(self):
        """显示训练总结"""
        print(f"\n{'='*60}")
        print(f"📊 批量训练总结")
        print(f"{'='*60}")
        
        successful_count = sum(1 for r in self.training_results["results"] if r["success"])
        success_rate = successful_count / len(self.training_results["results"]) * 100
        
        print(f"⏰ 总耗时: {self.training_results['total_duration']}")
        print(f"✅ 成功训练: {successful_count}/{len(self.training_scripts)} ({success_rate:.1f}%)")
        
        print(f"\n📋 详细结果:")
        for result in self.training_results["results"]:
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {result['name']}: {result['duration']}")
        
        if successful_count == len(self.training_scripts):
            print(f"\n🎉 所有算法训练完成！可以开始对比分析。")
        else:
            print(f"\n⚠️ 部分算法训练失败，请检查错误日志。")
        
        print(f"\n📁 结果保存位置: {self.output_dir}")

def main():
    """主函数"""
    print("🚀 开始批量训练所有基线算法和本文方法")
    
    # 创建批量训练器
    trainer = BatchTrainer()
    
    # 确认开始训练
    print(f"\n⚠️ 注意：批量训练预计需要约10小时完成")
    print(f"📋 将依次训练以下算法:")
    for i, script in enumerate(trainer.training_scripts):
        print(f"   {i+1}. {script['name']} ({script['estimated_time']})")
    
    response = input(f"\n是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 用户取消训练")
        return
    
    # 开始批量训练
    results = trainer.run_all_training()
    
    # 显示最终结果
    successful_count = sum(1 for r in results["results"] if r["success"])
    if successful_count == len(trainer.training_scripts):
        print(f"\n🎉 批量训练全部完成！")
        print(f"📊 现在可以运行对比分析和生成论文图表。")
    else:
        print(f"\n⚠️ 批量训练部分完成，请检查失败的算法。")

if __name__ == "__main__":
    main()
