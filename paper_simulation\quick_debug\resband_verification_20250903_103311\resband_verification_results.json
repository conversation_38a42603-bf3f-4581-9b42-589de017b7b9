{"convergence_analysis": {"resband_adaptive": {"episodes": [0, 1, 2], "rewards": [1454.6075084923432, 1.3050002123258582, 1.302931569130044], "success_rates": []}}, "performance_comparison": {"resband_adaptive": {"final_performance": {"avg_reward": 4335.843475698575, "success_rate": 0.0, "avg_steps": 1001.0, "constraint_violations": 0}, "learning_efficiency": {"convergence_episode": 3, "learning_rate": 0}}}, "efficiency_analysis": {"resband_adaptive": {"computation_times": [3980.8130264282227, 2.713918685913086, 4.178524017333984], "memory_usage": [], "avg_computation_time": 1329.2351563771565}}, "resolution_selection": {"resband_adaptive": {"selections": [], "performance_by_resolution": {}}}, "statistical_tests": {"performance_ranking": [["resband_adaptive", 1.7343373902794301]], "convergence_ranking": [["resband_adaptive", 0.25]], "efficiency_ranking": [], "resband_advantages": {}}}