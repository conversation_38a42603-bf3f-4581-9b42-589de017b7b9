"""
运行所有论文实验
Run All Paper Experiments

按顺序运行所有论文相关的实验，生成完整的实验数据
包括：主动安全保障验证、ResBand算法验证、反直觉场景验证等
"""

import os
import sys
import subprocess
import json
from datetime import datetime
import time

class PaperExperimentRunner:
    """论文实验运行器"""
    
    def __init__(self, output_dir="results/paper_experiments"):
        """
        初始化实验运行器
        
        Args:
            output_dir: 输出目录
        """
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"all_experiments_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 实验配置
        self.experiments = [
            {
                "name": "实验1：主动安全保障验证",
                "script": "experiments/individual/experiment_1_safety_verification.py",
                "description": "验证DWA-RL分层架构的零约束违反能力",
                "estimated_time": "30分钟",
                "generates": ["表10：约束违反统计", "表11：安全性能指标", "图4：轨迹平滑度"]
            },
            {
                "name": "实验2：ResBand算法验证",
                "script": "experiments/individual/experiment_2_resband_verification.py",
                "description": "验证ResBand分辨率自适应选择算法的有效性",
                "estimated_time": "45分钟",
                "generates": ["收敛速度对比", "性能对比表", "分辨率选择分析"]
            },
            {
                "name": "实验3：反直觉场景验证",
                "script": "experiments/individual/counterintuitive_scenario_validation.py",
                "description": "验证算法发现反直觉现象的能力",
                "estimated_time": "20分钟",
                "generates": ["反直觉场景统计", "统计显著性检验"]
            }
        ]
        
        # 结果存储
        self.experiment_results = {
            "start_time": None,
            "end_time": None,
            "total_duration": None,
            "experiments": []
        }
        
        print(f"📊 论文实验运行器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🧪 计划运行 {len(self.experiments)} 个实验")
    
    def run_all_experiments(self):
        """运行所有论文实验"""
        print(f"\n🚀 开始运行所有论文实验")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        self.experiment_results["start_time"] = datetime.now().isoformat()
        
        # 显示实验计划
        self._show_experiment_plan()
        
        # 确认开始
        response = input(f"\n是否开始运行所有实验？预计总时间约1.5小时 (y/N): ")
        if response.lower() != 'y':
            print("❌ 用户取消实验")
            return None
        
        # 逐个运行实验
        for i, experiment in enumerate(self.experiments):
            print(f"\n{'='*80}")
            print(f"🔬 实验进度: {i+1}/{len(self.experiments)}")
            print(f"📝 实验名称: {experiment['name']}")
            print(f"📄 脚本文件: {experiment['script']}")
            print(f"📖 实验描述: {experiment['description']}")
            print(f"⏱️ 预计时间: {experiment['estimated_time']}")
            print(f"📊 生成内容: {', '.join(experiment['generates'])}")
            print(f"{'='*80}")
            
            # 运行实验
            result = self._run_single_experiment(experiment)
            self.experiment_results["experiments"].append(result)
            
            # 显示结果
            if result["success"]:
                print(f"✅ {experiment['name']} 完成")
                print(f"⏱️ 实际用时: {result['duration']}")
            else:
                print(f"❌ {experiment['name']} 失败")
                print(f"🔍 错误信息: {result['error']}")
                
                # 询问是否继续
                response = input(f"\n实验失败，是否继续运行剩余实验？ (y/N): ")
                if response.lower() != 'y':
                    print("⏹️ 用户选择停止实验")
                    break
        
        self.experiment_results["end_time"] = datetime.now().isoformat()
        
        # 计算总时间
        start_time = datetime.fromisoformat(self.experiment_results["start_time"])
        end_time = datetime.fromisoformat(self.experiment_results["end_time"])
        total_duration = end_time - start_time
        self.experiment_results["total_duration"] = str(total_duration)
        
        # 保存结果
        self._save_experiment_results()
        
        # 显示总结
        self._show_experiment_summary()
        
        # 生成论文数据汇总
        self._generate_paper_data_summary()
        
        return self.experiment_results
    
    def _show_experiment_plan(self):
        """显示实验计划"""
        print(f"\n📋 实验计划:")
        
        for i, experiment in enumerate(self.experiments):
            print(f"\n   {i+1}. {experiment['name']}")
            print(f"      📄 脚本: {experiment['script']}")
            print(f"      📖 描述: {experiment['description']}")
            print(f"      ⏱️ 预计时间: {experiment['estimated_time']}")
            print(f"      📊 生成内容:")
            for content in experiment['generates']:
                print(f"         • {content}")
        
        total_time = sum([30, 45, 20])  # 简化的时间估算
        print(f"\n📊 预计总时间: 约 {total_time} 分钟")
        print(f"🎯 预计完成时间: {(datetime.now() + datetime.timedelta(minutes=total_time)).strftime('%Y-%m-%d %H:%M:%S')}")
    
    def _run_single_experiment(self, experiment):
        """运行单个实验"""
        script_path = os.path.join(os.path.dirname(__file__), experiment["script"])
        
        result = {
            "name": experiment["name"],
            "script": experiment["script"],
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "duration": None,
            "success": False,
            "error": None,
            "output": None,
            "generates": experiment["generates"]
        }
        
        try:
            print(f"🚀 开始运行: {experiment['script']}")
            
            # 运行实验脚本
            start_time = datetime.now()
            
            process = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=3600,  # 1小时超时
                cwd=os.path.dirname(__file__)
            )
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            result["end_time"] = end_time.isoformat()
            result["duration"] = str(duration)
            result["output"] = process.stdout
            
            if process.returncode == 0:
                result["success"] = True
                print(f"✅ {experiment['name']} 执行成功")
            else:
                result["success"] = False
                result["error"] = process.stderr
                print(f"❌ {experiment['name']} 执行失败")
                print(f"错误输出: {process.stderr}")
        
        except subprocess.TimeoutExpired:
            result["error"] = "实验超时（1小时）"
            print(f"⏰ {experiment['name']} 执行超时")
        
        except Exception as e:
            result["error"] = str(e)
            print(f"💥 {experiment['name']} 运行异常: {e}")
        
        return result
    
    def _save_experiment_results(self):
        """保存实验结果"""
        # 保存详细结果
        results_file = os.path.join(self.output_dir, "all_experiments_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.experiment_results, f, indent=2, ensure_ascii=False)
        
        # 生成实验报告
        self._generate_experiment_report()
        
        print(f"💾 实验结果保存至: {self.output_dir}")
    
    def _generate_experiment_report(self):
        """生成实验报告"""
        report_file = os.path.join(self.output_dir, "paper_experiments_report.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 论文实验完整报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 实验概述\n\n")
            f.write(f"- **开始时间**: {self.experiment_results['start_time']}\n")
            f.write(f"- **结束时间**: {self.experiment_results['end_time']}\n")
            f.write(f"- **总耗时**: {self.experiment_results['total_duration']}\n")
            f.write(f"- **实验数量**: {len(self.experiments)}\n\n")
            
            # 成功率统计
            successful_count = sum(1 for r in self.experiment_results["experiments"] if r["success"])
            success_rate = successful_count / len(self.experiment_results["experiments"]) * 100
            
            f.write(f"- **成功实验**: {successful_count}/{len(self.experiments)} ({success_rate:.1f}%)\n\n")
            
            f.write("## 实验结果详情\n\n")
            
            for i, result in enumerate(self.experiment_results["experiments"]):
                f.write(f"### {i+1}. {result['name']}\n\n")
                f.write(f"- **脚本**: {result['script']}\n")
                f.write(f"- **状态**: {'✅ 成功' if result['success'] else '❌ 失败'}\n")
                f.write(f"- **开始时间**: {result['start_time']}\n")
                f.write(f"- **结束时间**: {result['end_time']}\n")
                f.write(f"- **耗时**: {result['duration']}\n")
                f.write(f"- **生成内容**:\n")
                for content in result['generates']:
                    f.write(f"  - {content}\n")
                
                if not result['success'] and result['error']:
                    f.write(f"- **错误信息**: {result['error']}\n")
                
                f.write("\n")
            
            f.write("## 论文数据生成情况\n\n")
            
            if successful_count == len(self.experiments):
                f.write("🎉 **所有实验成功完成！**\n\n")
                f.write("已生成的论文支撑数据：\n")
                f.write("- 表10：约束违反统计表\n")
                f.write("- 表11：安全性能指标表\n")
                f.write("- 图4：轨迹平滑度对比图\n")
                f.write("- ResBand收敛速度分析\n")
                f.write("- ResBand性能对比表\n")
                f.write("- 反直觉场景验证结果\n")
                f.write("- 统计显著性检验报告\n\n")
                
                f.write("## 下一步建议\n\n")
                f.write("1. 查看各实验的详细结果文件\n")
                f.write("2. 将生成的表格和图表插入论文\n")
                f.write("3. 根据统计分析结果撰写实验结果章节\n")
                f.write("4. 进行论文的最终校对和完善\n")
            else:
                f.write("⚠️ **部分实验失败**\n\n")
                f.write("建议检查失败的实验并重新运行。\n")
    
    def _show_experiment_summary(self):
        """显示实验总结"""
        print(f"\n{'='*80}")
        print(f"📊 论文实验总结")
        print(f"{'='*80}")
        
        successful_count = sum(1 for r in self.experiment_results["experiments"] if r["success"])
        success_rate = successful_count / len(self.experiment_results["experiments"]) * 100
        
        print(f"⏰ 总耗时: {self.experiment_results['total_duration']}")
        print(f"✅ 成功实验: {successful_count}/{len(self.experiments)} ({success_rate:.1f}%)")
        
        print(f"\n📋 详细结果:")
        for result in self.experiment_results["experiments"]:
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {result['name']}: {result['duration']}")
        
        if successful_count == len(self.experiments):
            print(f"\n🎉 所有论文实验完成！可以开始撰写论文。")
            print(f"\n📊 已生成的论文数据:")
            print(f"   • 表10、表11：安全保障验证数据")
            print(f"   • ResBand算法验证数据")
            print(f"   • 反直觉场景验证数据")
            print(f"   • 统计显著性检验结果")
        else:
            print(f"\n⚠️ 部分实验失败，请检查错误日志。")
        
        print(f"\n📁 结果保存位置: {self.output_dir}")
    
    def _generate_paper_data_summary(self):
        """生成论文数据汇总"""
        summary_file = os.path.join(self.output_dir, "paper_data_summary.json")
        
        paper_data = {
            "generation_time": datetime.now().isoformat(),
            "experiment_success_rate": 0,
            "generated_tables": [],
            "generated_figures": [],
            "statistical_results": {},
            "data_locations": {}
        }
        
        successful_count = sum(1 for r in self.experiment_results["experiments"] if r["success"])
        paper_data["experiment_success_rate"] = successful_count / len(self.experiments)
        
        # 根据成功的实验添加生成的数据
        for result in self.experiment_results["experiments"]:
            if result["success"]:
                if "表10" in str(result["generates"]):
                    paper_data["generated_tables"].append("表10：约束违反统计")
                if "表11" in str(result["generates"]):
                    paper_data["generated_tables"].append("表11：安全性能指标")
                if "图4" in str(result["generates"]):
                    paper_data["generated_figures"].append("图4：轨迹平滑度对比")
                if "收敛速度" in str(result["generates"]):
                    paper_data["generated_figures"].append("ResBand收敛分析图")
                if "反直觉" in str(result["generates"]):
                    paper_data["statistical_results"]["counterintuitive_validation"] = True
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(paper_data, f, indent=2, ensure_ascii=False)
        
        print(f"📋 论文数据汇总已生成")

def main():
    """主函数"""
    print("📊 论文实验完整运行系统")
    print("=" * 60)
    print("🎯 本系统将运行以下实验:")
    print("   1. 实验1：主动安全保障验证")
    print("   2. 实验2：ResBand算法验证")
    print("   3. 实验3：反直觉场景验证")
    print("=" * 60)
    
    # 创建实验运行器
    runner = PaperExperimentRunner()
    
    # 运行所有实验
    results = runner.run_all_experiments()
    
    if results:
        successful_count = sum(1 for r in results["experiments"] if r["success"])
        if successful_count == len(runner.experiments):
            print(f"\n🎉 所有论文实验成功完成！")
            print(f"📊 现在可以使用生成的数据撰写论文。")
        else:
            print(f"\n⚠️ 部分实验完成，请检查失败的实验。")

if __name__ == "__main__":
    main()
