#!/usr/bin/env python3
"""
调试实验流程
完全模拟实验中的智能体创建和训练过程
"""

def debug_experiment_flow():
    """调试实验流程"""
    print("🔍 调试实验流程")
    print("=" * 60)
    
    try:
        # 完全模拟实验中的导入和创建过程
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        
        # 创建实验实例
        experiment = ResBandVerificationExperiment("debug_experiment")
        
        print(f"✅ 实验实例创建成功")
        
        # 模拟实验中的环境和智能体创建
        print(f"\n🌍 创建环境...")
        env = experiment._create_environment("stage3_dynamic")
        print(f"   环境类型: {type(env)}")
        print(f"   环境配置: {env.scenario}")
        
        print(f"\n🤖 创建智能体...")
        agent = experiment._create_agent("resband_adaptive", env)
        print(f"   智能体类型: {type(agent)}")
        print(f"   智能体配置: {agent.config}")
        print(f"   方法类型: {agent._method_type}")
        
        # 测试智能体的各个组件
        print(f"\n🔧 测试智能体组件...")
        print(f"   DWA启用: {agent.config.get('dwa_enabled', False)}")
        print(f"   ResBand启用: {agent.config.get('resband_enabled', False)}")
        print(f"   MLACF启用: {agent.config.get('mlacf_enabled', False)}")
        
        # 测试ResBand组件
        if hasattr(agent, 'resband'):
            print(f"   ResBand组件: {type(agent.resband)}")
        else:
            print(f"   ❌ ResBand组件未找到")
        
        # 测试DWA组件
        if hasattr(agent, 'dwa'):
            print(f"   DWA组件: {type(agent.dwa)}")
        else:
            print(f"   ❌ DWA组件未找到")
        
        # 模拟训练episode的第一步
        print(f"\n🎯 模拟训练episode的第一步...")
        
        # 重置环境
        state = env.reset()
        print(f"   初始状态: {state[:3]}")
        print(f"   环境step_count: {env.step_count}")
        
        # 调用智能体的train_step方法
        print(f"   调用agent.train_step...")
        step_info = agent.train_step(state, training_progress=0.1)
        
        print(f"   返回的动作: {step_info['action']}")
        print(f"   返回的next_state: {step_info['next_state'][:3]}")
        print(f"   返回的reward: {step_info['reward']:.3f}")
        print(f"   返回的done: {step_info['done']}")
        print(f"   返回的info: {step_info.get('info', {})}")
        
        # 分析动作来源
        print(f"\n🔍 分析动作来源...")
        action = step_info['action']
        
        # 检查动作格式
        if len(action) == 3:
            print(f"   动作维度: 3D ✅")
            print(f"   a_T: {action[0]:.3f}")
            print(f"   a_N: {action[1]:.3f}")
            print(f"   mu: {action[2]:.3f}")
            
            # 检查是否是环境生成的安全动作
            if action[2] == 0:
                print(f"   可能来源: 环境的get_safe_actions方法 ✅")
            elif action[0] == 0 and action[1] == 0:
                print(f"   可能来源: 紧急停止动作")
            else:
                print(f"   可能来源: 其他动作选择逻辑")
        else:
            print(f"   ❌ 动作维度异常: {len(action)}")
        
        # 检查移动距离
        movement = np.linalg.norm(step_info['next_state'][:3] - state[:3])
        print(f"   移动距离: {movement:.3f}米")
        
        if movement > 10:
            print(f"   🚨 移动距离异常")
            return False
        
        if step_info['done'] and env.step_count == 1:
            print(f"   🚨 第1步就结束")
            return False
        
        print(f"   ✅ 第一步正常")
        
        # 测试多个episodes
        print(f"\n🔄 测试多个episodes...")
        for episode in range(3):
            print(f"   Episode {episode + 1}:")
            
            state = env.reset()
            step_info = agent.train_step(state, training_progress=episode/10)
            
            movement = np.linalg.norm(step_info['next_state'][:3] - state[:3])
            print(f"     动作: {step_info['action']}")
            print(f"     移动: {movement:.3f}米")
            print(f"     完成: {step_info['done']}")
            
            if movement > 10 or (step_info['done'] and env.step_count == 1):
                print(f"     ❌ Episode {episode + 1} 异常")
                return False
            else:
                print(f"     ✅ Episode {episode + 1} 正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 实验流程调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_action_generation():
    """专门调试动作生成过程"""
    print("\n🎯 专门调试动作生成过程")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage3_dynamic")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True,
            "mlacf_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        # 重置环境
        state = env.reset()
        print(f"初始状态: {state[:3]}")
        
        # 手动调用动作生成的各个步骤
        print(f"\n🔍 手动调用动作生成步骤:")
        
        # 步骤1: ResBand选择分辨率
        print(f"步骤1: ResBand选择分辨率")
        if hasattr(agent, 'resband'):
            # 模拟ResBand选择
            resolution_config = {
                "delta_a_T": 4.0,
                "delta_a_N": 10.0,
                "resolution_level": 1
            }
            print(f"   选择的分辨率配置: {resolution_config}")
        else:
            print(f"   ❌ ResBand组件未找到")
            return False
        
        # 步骤2: 环境生成安全动作
        print(f"步骤2: 环境生成安全动作")
        safe_actions = env.get_safe_actions(resolution_config, num_actions=10)
        print(f"   生成的安全动作数量: {len(safe_actions)}")
        
        if len(safe_actions) > 0:
            print(f"   前3个安全动作:")
            for i, action in enumerate(safe_actions[:3]):
                print(f"     动作{i+1}: {action}")
        else:
            print(f"   ❌ 没有生成安全动作")
            return False
        
        # 步骤3: 智能体选择动作
        print(f"步骤3: 智能体选择动作")
        if len(safe_actions) > 0:
            selected_action = safe_actions[0]  # 简单选择第一个
            print(f"   选择的动作: {selected_action}")
        else:
            selected_action = np.array([0.0, 0.0, 0.0])
            print(f"   使用紧急动作: {selected_action}")
        
        # 步骤4: 执行动作
        print(f"步骤4: 执行动作")
        next_state, reward, done, info = env.step(selected_action)
        
        movement = np.linalg.norm(next_state[:3] - state[:3])
        print(f"   新状态: {next_state[:3]}")
        print(f"   移动距离: {movement:.3f}米")
        print(f"   奖励: {reward:.3f}")
        print(f"   完成: {done}")
        print(f"   信息: {info}")
        
        if movement > 10:
            print(f"   🚨 移动距离异常")
            return False
        
        if done and env.step_count == 1:
            print(f"   🚨 第1步就结束")
            return False
        
        print(f"   ✅ 动作执行正常")
        return True
        
    except Exception as e:
        print(f"❌ 动作生成调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 实验流程专项调试")
    
    # 调试实验流程
    flow_success = debug_experiment_flow()
    
    # 调试动作生成
    action_success = debug_action_generation()
    
    if flow_success and action_success:
        print("\n" + "=" * 80)
        print("🎯 实验流程调试完成！")
        print("实验流程正常工作")
    else:
        print("\n❌ 发现实验流程问题")
        print("需要进一步修复")

if __name__ == "__main__":
    main()
