"""
正确的奖励系统设计
Correct Reward System Design

基于巡飞简化ver的设计原则：
- DWA: 局部安全保障，生成安全动作集
- RL: 全局策略优化，从安全动作集中选择最优动作
- 环境奖励: 专注于RL需要优化的长期指标
"""

import numpy as np
from typing import Dict, List, Tuple, Any

class CorrectRewardSystem:
    """正确的奖励系统"""
    
    def __init__(self, config=None):
        """
        初始化奖励系统
        
        Args:
            config: 奖励配置
        """
        default_config = {
            # 环境奖励权重（专注于RL优化的长期指标）
            "energy_weight": 0.3,      # 能量效率权重
            "smoothness_weight": 0.25, # 路径平滑性权重
            "strategy_weight": 0.25,   # 全局策略权重
            "progress_weight": 0.2,    # 进度权重
            
            # DWA安全评价权重（专注于局部安全保障）
            "collision_risk_weight": 0.5,    # 碰撞风险权重
            "boundary_safety_weight": 0.2,   # 边界安全权重
            "kinematic_weight": 0.2,          # 运动学约束权重
            "basic_guidance_weight": 0.1,     # 基本导向权重
            
            # ResBand奖励权重
            "performance_weight": 0.8,        # 性能权重
            "efficiency_weight": 0.2,         # 计算效率权重
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 初始化追踪器
        self.dwa_tracker = {
            "energy_efficiency_scores": [],
            "path_smoothness_scores": [],
            "global_strategy_scores": [],
            "total_energy_consumed": 0,
            "step_count": 0,
            "prev_control": None
        }
        
        print("🎯 正确奖励系统初始化完成")
        print(f"   - 环境奖励专注于: 能量效率、路径平滑性、全局策略")
        print(f"   - DWA专注于: 局部安全保障")
    
    def environment_reward(self, state, action, next_state, info, dwa_tracker=None):
        """
        环境奖励函数 - 专注于RL优化的长期指标
        DWA已经保证了安全性，RL专注于全局优化
        
        Args:
            state: 当前状态
            action: 执行的动作
            next_state: 下一状态
            info: 环境信息
            dwa_tracker: DWA追踪信息
        
        Returns:
            float: 环境奖励
        """
        if dwa_tracker is None:
            dwa_tracker = self.dwa_tracker
        
        # 1. 能量效率奖励（DWA无法优化的长期指标）
        energy_reward = self._calculate_energy_efficiency_reward(action, dwa_tracker)
        
        # 2. 路径平滑性奖励（DWA的短视性无法优化）
        smoothness_reward = self._calculate_path_smoothness_reward(action, dwa_tracker)
        
        # 3. 全局策略质量奖励（TD3从安全动作集中的选择质量）
        strategy_reward = self._calculate_global_strategy_reward(state, action, info)
        
        # 4. 进度奖励（在保持效率前提下的目标接近）
        progress_reward = self._calculate_progress_reward(
            state, next_state, energy_reward, smoothness_reward
        )
        
        # 5. 时间惩罚（鼓励效率）
        time_penalty = -0.3
        
        # 组合总奖励
        total_reward = (
            self.config["energy_weight"] * energy_reward +
            self.config["smoothness_weight"] * smoothness_reward +
            self.config["strategy_weight"] * strategy_reward +
            self.config["progress_weight"] * progress_reward +
            time_penalty
        )
        
        # 更新追踪器
        self._update_tracker(dwa_tracker, action, energy_reward, smoothness_reward, strategy_reward)
        
        return total_reward
    
    def dwa_safety_evaluation(self, control, current_state, obstacles, goal):
        """
        DWA安全评价函数 - 专注于局部安全保障
        不是奖励函数，而是安全性评价函数
        
        Args:
            control: 控制输入
            current_state: 当前状态
            obstacles: 障碍物列表
            goal: 目标位置
        
        Returns:
            float: 安全性评分（0-1，越高越安全）
        """
        # 1. 碰撞风险评价（最重要）
        collision_risk = self._calculate_collision_risk(control, current_state, obstacles)
        
        # 2. 边界安全评价
        boundary_safety = self._calculate_boundary_safety(control, current_state)
        
        # 3. 运动学约束评价
        kinematic_feasibility = self._calculate_kinematic_feasibility(control, current_state)
        
        # 4. 基本导向性评价（确保不会完全偏离目标）
        basic_guidance = self._calculate_basic_guidance(control, current_state, goal)
        
        # 安全性评价（不是奖励，是安全筛选标准）
        safety_score = (
            self.config["collision_risk_weight"] * collision_risk +
            self.config["boundary_safety_weight"] * boundary_safety +
            self.config["kinematic_weight"] * kinematic_feasibility +
            self.config["basic_guidance_weight"] * basic_guidance
        )
        
        return safety_score
    
    def resband_reward_calculation(self, arm, episode_reward, computational_metrics):
        """
        ResBand奖励计算 - 基于环境奖励和计算效率
        
        Args:
            arm: 选择的分辨率臂
            episode_reward: episode总奖励
            computational_metrics: 计算指标
        
        Returns:
            float: ResBand奖励
        """
        # 使用环境奖励作为性能指标
        performance_score = self._normalize_episode_reward(episode_reward)
        
        # 计算效率指标
        efficiency_score = self._calculate_resolution_efficiency(arm, computational_metrics)
        
        # ResBand专用的组合奖励
        resband_reward = (
            self.config["performance_weight"] * performance_score +
            self.config["efficiency_weight"] * efficiency_score
        )
        
        return resband_reward
    
    def mlacf_performance_feedback(self, scene_features, episode_reward, strategy_info):
        """
        MLACF性能反馈 - 基于环境奖励进行元学习
        
        Args:
            scene_features: 场景特征
            episode_reward: episode奖励
            strategy_info: 策略信息
        
        Returns:
            dict: 性能反馈
        """
        performance_feedback = {
            "episode_reward": episode_reward,
            "success_rate": strategy_info.get("success_rate", 0),
            "energy_efficiency": np.mean(self.dwa_tracker["energy_efficiency_scores"]) if self.dwa_tracker["energy_efficiency_scores"] else 0,
            "path_smoothness": np.mean(self.dwa_tracker["path_smoothness_scores"]) if self.dwa_tracker["path_smoothness_scores"] else 0,
            "global_strategy": np.mean(self.dwa_tracker["global_strategy_scores"]) if self.dwa_tracker["global_strategy_scores"] else 0,
            "constraint_violations": strategy_info.get("violations", 0)
        }
        
        return performance_feedback
    
    def reset_tracker(self):
        """重置追踪器"""
        self.dwa_tracker = {
            "energy_efficiency_scores": [],
            "path_smoothness_scores": [],
            "global_strategy_scores": [],
            "total_energy_consumed": 0,
            "step_count": 0,
            "prev_control": None
        }
    
    def _calculate_energy_efficiency_reward(self, action, dwa_tracker):
        """计算能量效率奖励"""
        # 计算控制能量消耗
        control_energy = np.sum(np.abs(action))
        max_energy = 3.0  # 假设最大能量消耗
        
        energy_efficiency = max(0, 1.0 - control_energy / max_energy)
        dwa_tracker["energy_efficiency_scores"].append(energy_efficiency)
        dwa_tracker["total_energy_consumed"] += control_energy
        
        return energy_efficiency * 15.0
    
    def _calculate_path_smoothness_reward(self, action, dwa_tracker):
        """计算路径平滑性奖励"""
        if dwa_tracker["prev_control"] is not None and dwa_tracker["step_count"] > 1:
            control_change = np.linalg.norm(action - dwa_tracker["prev_control"])
            max_change = 2.0  # 假设最大变化
            smoothness = max(0, 1.0 - control_change / max_change)
            
            dwa_tracker["path_smoothness_scores"].append(smoothness)
            smoothness_reward = smoothness * 12.0
        else:
            smoothness = 0.5
            smoothness_reward = 6.0
        
        dwa_tracker["prev_control"] = action.copy()
        return smoothness_reward
    
    def _calculate_global_strategy_reward(self, state, action, info):
        """计算全局策略质量奖励"""
        # 评估TD3选择的动作相对于所有可能安全动作的全局优化程度
        # 这里简化为基于任务完成情况的评估
        
        if info.get("success", False):
            global_strategy_score = 1.0
        elif info.get("collision", False):
            global_strategy_score = 0.0
        else:
            # 基于距离目标的改善程度
            goal_improvement = info.get("distance_improvement", 0)
            global_strategy_score = max(0, min(1, 0.5 + goal_improvement / 100.0))
        
        return global_strategy_score * 12.0
    
    def _calculate_progress_reward(self, state, next_state, energy_reward, smoothness_reward):
        """计算进度奖励"""
        # 计算距离改善
        if len(state) >= 6 and len(next_state) >= 6:
            # 假设目标在原点附近
            current_dist = np.linalg.norm(state[:3])
            next_dist = np.linalg.norm(next_state[:3])
            distance_improvement = current_dist - next_dist
            
            # 只有在保持效率的前提下才奖励进度
            if energy_reward > 7.5 and smoothness_reward > 6.0:  # 效率合理时
                progress_reward = distance_improvement * 2.0
            else:
                progress_reward = distance_improvement * 0.5  # 效率不佳时减少进度奖励
        else:
            progress_reward = 0
        
        return progress_reward
    
    def _calculate_collision_risk(self, control, current_state, obstacles):
        """计算碰撞风险"""
        # 简化的碰撞风险计算
        # 实际实现中应该基于轨迹预测
        min_distance = float('inf')
        
        for obs in obstacles:
            if isinstance(obs, dict) and 'center' in obs:
                distance = np.linalg.norm(current_state[:3] - obs['center']) - obs.get('radius', 10)
                min_distance = min(min_distance, distance)
        
        if min_distance == float('inf'):
            return 1.0
        
        # 距离越远，风险越小，评分越高
        return min(1.0, max(0.0, min_distance / 50.0))
    
    def _calculate_boundary_safety(self, control, current_state):
        """计算边界安全性"""
        # 简化的边界安全计算
        pos = current_state[:3]
        bounds = np.array([1000, 1000, 100])  # 假设边界
        
        min_boundary_dist = np.min(bounds - np.abs(pos))
        return min(1.0, max(0.0, min_boundary_dist / 100.0))
    
    def _calculate_kinematic_feasibility(self, control, current_state):
        """计算运动学可行性"""
        # 检查控制输入是否在合理范围内
        max_control = np.array([10.0, 5.0, np.pi/2])  # 假设最大控制量
        
        feasibility = 1.0 - np.mean(np.abs(control) / max_control)
        return max(0.0, feasibility)
    
    def _calculate_basic_guidance(self, control, current_state, goal):
        """计算基本导向性"""
        # 简化的导向性计算
        if len(current_state) >= 3:
            to_goal = goal - current_state[:3]
            to_goal_norm = np.linalg.norm(to_goal)
            
            if to_goal_norm > 0:
                to_goal_unit = to_goal / to_goal_norm
                # 假设控制输入的第一个分量是前向推力
                guidance_score = max(0, control[0]) * max(0, np.dot(to_goal_unit, [1, 0, 0]))
                return min(1.0, guidance_score)
        
        return 0.5
    
    def _normalize_episode_reward(self, episode_reward):
        """归一化episode奖励"""
        # 简单的归一化，实际中可能需要更复杂的方法
        return max(0, min(1, (episode_reward + 100) / 200))
    
    def _calculate_resolution_efficiency(self, arm, computational_metrics):
        """计算分辨率效率"""
        # 基于计算时间和精度的效率评估
        computation_time = computational_metrics.get("computation_time", 0.1)
        max_time = 0.5  # 假设最大可接受时间
        
        time_efficiency = max(0, 1.0 - computation_time / max_time)
        return time_efficiency
    
    def _update_tracker(self, dwa_tracker, action, energy_reward, smoothness_reward, strategy_reward):
        """更新追踪器"""
        dwa_tracker["step_count"] += 1
        
        # 记录策略评分
        strategy_score = strategy_reward / 12.0  # 归一化
        dwa_tracker["global_strategy_scores"].append(strategy_score)

def main():
    """测试正确奖励系统"""
    print("🧪 测试正确奖励系统")
    
    reward_system = CorrectRewardSystem()
    
    # 测试环境奖励
    state = np.array([100, 50, 20, 10, 0.5, 0.2])
    action = np.array([2.0, 1.0, 0.1])
    next_state = np.array([98, 49, 19.5, 10.2, 0.5, 0.2])
    info = {"distance_improvement": 2.0}
    
    reward = reward_system.environment_reward(state, action, next_state, info)
    print(f"环境奖励: {reward:.2f}")
    
    # 测试DWA安全评价
    obstacles = [{"center": np.array([80, 40, 15]), "radius": 10}]
    goal = np.array([0, 0, 0])
    
    safety_score = reward_system.dwa_safety_evaluation(action, state, obstacles, goal)
    print(f"DWA安全评分: {safety_score:.2f}")
    
    print("✅ 正确奖励系统测试完成")

if __name__ == "__main__":
    main()
