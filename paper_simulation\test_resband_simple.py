#!/usr/bin/env python3
"""
简单ResBand测试
"""

def main():
    print("🎰 测试ResBand实验脚本...")
    
    try:
        # 测试导入
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        print("   ✅ 实验类导入成功")
        
        # 创建实验实例
        experiment = ResBandVerificationExperiment("test_output")
        print("   ✅ 实验实例创建成功")
        
        # 测试配置
        print(f"   📋 对比方法: {experiment.config['comparison_methods']}")
        print(f"   📋 测试场景: {experiment.config['test_scenarios']}")
        print(f"   📋 渐进式训练: {experiment.config['progressive_training']}")
        
        # 测试单个方法创建
        from environments.paper_environment import PaperSimulationEnvironment
        env = PaperSimulationEnvironment("stage1_simple")
        
        agent = experiment._create_agent("resband_adaptive", env)
        print("   ✅ ResBand智能体创建成功")
        
        agent = experiment._create_agent("fixed_coarse", env)
        print("   ✅ 固定粗分辨率智能体创建成功")
        
        # 测试检查点功能
        experiment._save_checkpoint("test_method")
        completed = experiment._load_checkpoint()
        print(f"   ✅ 检查点功能正常: {completed}")
        
        print("\n🎉 ResBand实验脚本测试通过！")
        print("✅ 可以运行完整实验")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n📋 运行完整实验:")
        print("   python experiments/individual/experiment_2_resband_verification.py --auto")
        print("\n📋 运行特性:")
        print("   ✅ 支持中止后重启")
        print("   ✅ 渐进式训练（简单→复杂→动态）")
        print("   ✅ 5种对比方法")
        print("   ✅ 自动生成图表和报告")
    else:
        print("\n❌ 请先修复问题")
