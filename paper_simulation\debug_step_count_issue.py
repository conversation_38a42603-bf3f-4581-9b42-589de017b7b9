#!/usr/bin/env python3
"""
专门调试step_count问题
模拟完整的训练流程来找出step_count不一致的根源
"""

def debug_step_count_issue():
    """调试step_count问题"""
    print("🔍 专门调试step_count问题")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        import numpy as np
        
        # 创建环境和智能体
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        agent = LightweightDWARL(env, config)
        agent._method_type = "resband_adaptive"
        
        print(f"✅ 环境和智能体创建成功")
        
        # 模拟3个episodes的训练流程
        for episode in range(3):
            print(f"\n🎯 Episode {episode + 1}")
            print("=" * 40)
            
            # 步骤1: 训练器调用env.reset()
            print(f"步骤1: 训练器调用env.reset()")
            state = env.reset()
            print(f"   重置后step_count: {env.step_count}")
            print(f"   重置后状态: {state[:3]}")
            
            # 步骤2: 检查初始终止条件
            done, info = env._check_termination()
            print(f"   初始终止检查: done={done}, info={info}")
            
            if done:
                print(f"   ❌ 环境在重置后就被标记为完成！")
                continue
            
            # 步骤3: 调用agent.train_step()
            print(f"步骤3: 调用agent.train_step()")
            print(f"   调用前env.step_count: {env.step_count}")
            print(f"   调用前env.state: {env.state[:3]}")
            
            step_info = agent.train_step(state, training_progress=0.1)
            
            print(f"   调用后env.step_count: {env.step_count}")
            print(f"   调用后env.state: {env.state[:3]}")
            print(f"   返回的next_state: {step_info['next_state'][:3]}")
            print(f"   返回的done: {step_info['done']}")
            print(f"   返回的info: {step_info.get('info', {})}")
            
            # 步骤4: 分析为什么done=True
            if step_info['done']:
                print(f"步骤4: 分析为什么done=True")
                
                # 手动检查终止条件
                current_pos = step_info['next_state'][:3]
                distance_to_target = np.linalg.norm(current_pos - env.target_pos)
                print(f"   距离目标: {distance_to_target:.1f}m (阈值: 50m)")
                
                collision = env._check_collision()
                print(f"   碰撞检查: {collision}")
                
                from environments.environment_configs import FAST_TRAINING_CONFIG
                max_steps = FAST_TRAINING_CONFIG["max_steps_per_episode"]
                timeout = env.step_count >= max_steps
                print(f"   超时检查: {timeout} (步数: {env.step_count}/{max_steps})")
                
                stall = step_info['next_state'][3] < env.physics_config["V_min"]
                print(f"   失速检查: {stall} (速度: {step_info['next_state'][3]:.2f}m/s)")
                
                if timeout:
                    print(f"   🚨 发现问题：step_count={env.step_count} >= max_steps={max_steps}")
                    print(f"   这说明step_count在episode之间没有正确重置！")
            
            # 如果第一个episode有问题，立即停止
            if episode == 0 and step_info['done'] and env.step_count >= 1000:
                print(f"\n❌ 第一个episode就有step_count问题，停止调试")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_environment_reset():
    """专门调试环境reset方法"""
    print("\n🔄 专门调试环境reset方法")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        
        print(f"初始创建后step_count: {env.step_count}")
        
        # 模拟第一个episode
        print(f"\n--- 第一个episode ---")
        state = env.reset()
        print(f"reset后step_count: {env.step_count}")
        
        # 模拟运行多步
        for step in range(5):
            action = np.array([2.0, 5.0, 0.1])
            next_state, reward, done, info = env.step(action)
            print(f"步骤{step+1}: step_count={env.step_count}, done={done}")
            if done:
                break
        
        print(f"第一个episode结束时step_count: {env.step_count}")
        
        # 模拟第二个episode
        print(f"\n--- 第二个episode ---")
        state = env.reset()
        print(f"reset后step_count: {env.step_count}")
        
        # 检查第一步
        action = np.array([2.0, 5.0, 0.1])
        next_state, reward, done, info = env.step(action)
        print(f"第一步: step_count={env.step_count}, done={done}, info={info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境reset调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 step_count问题专项调试")
    
    # 调试step_count问题
    issue_success = debug_step_count_issue()
    
    # 调试环境reset
    reset_success = debug_environment_reset()
    
    if issue_success and reset_success:
        print("\n" + "=" * 80)
        print("🎯 step_count问题调试完成！")
        print("请查看上述输出，找出step_count不一致的具体原因")
    else:
        print("\n❌ step_count问题调试失败")

if __name__ == "__main__":
    main()
