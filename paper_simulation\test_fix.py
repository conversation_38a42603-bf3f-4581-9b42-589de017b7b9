#!/usr/bin/env python3
"""
测试修复后的代码
"""

def test_environment_state():
    """测试环境状态管理"""
    print("🧪 测试环境状态管理...")
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        print("   ✅ 环境创建成功")
        
        # 测试重置
        state = env.reset()
        print(f"   ✅ 环境重置成功，状态: {state}")
        
        # 测试步骤
        action = np.array([1.0, 0.5, 0.1])
        next_state, reward, done, info = env.step(action)
        print(f"   ✅ 环境步骤成功，奖励: {reward:.3f}")
        
        # 测试多步骤
        for i in range(3):
            action = np.random.uniform(-1, 1, 3)
            next_state, reward, done, info = env.step(action)
            print(f"   步骤 {i+1}: 奖励={reward:.3f}, 完成={done}")
            if done:
                break
        
        return True
        
    except Exception as e:
        print(f"   ❌ 环境测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_framework():
    """测试框架"""
    print("\n🤖 测试框架...")
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from algorithms.lightweight_dwa_rl import LightweightDWARL
        
        # 创建环境和框架
        env = PaperSimulationEnvironment("stage1_simple")
        config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": True
        }
        framework = LightweightDWARL(env, config)
        print("   ✅ 框架创建成功")
        
        # 测试train_step
        state = env.reset()
        step_info = framework.train_step(state)
        print(f"   ✅ train_step成功，奖励: {step_info['reward']:.3f}")
        
        # 测试多步骤
        for i in range(3):
            step_info = framework.train_step(step_info['next_state'])
            print(f"   步骤 {i+1}: 奖励={step_info['reward']:.3f}, 完成={step_info['done']}")
            if step_info['done']:
                break
        
        return True
        
    except Exception as e:
        print(f"   ❌ 框架测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_experiment():
    """测试简单实验"""
    print("\n🧪 测试简单实验...")
    
    try:
        from experiments.individual.experiment_2_resband_verification import ResBandVerificationExperiment
        from environments.paper_environment import PaperSimulationEnvironment
        
        # 创建实验
        experiment = ResBandVerificationExperiment("test_output")
        print("   ✅ 实验创建成功")
        
        # 测试单个方法创建
        env = PaperSimulationEnvironment("stage1_simple")
        agent = experiment._create_agent("resband_adaptive", env)
        print("   ✅ 智能体创建成功")
        
        # 测试训练一个episode
        episode_info = experiment._train_episode(agent, env, 0, "resband_adaptive")
        print(f"   ✅ Episode训练成功: 奖励={episode_info['reward']:.3f}, 步数={episode_info['steps']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 实验测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 测试修复后的代码")
    print("=" * 50)
    
    tests = [
        ("环境状态管理", test_environment_state),
        ("框架功能", test_framework),
        ("简单实验", test_simple_experiment)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功")
        print("\n🚀 可以运行完整实验:")
        print("   python experiments/individual/experiment_2_resband_verification.py --auto")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
