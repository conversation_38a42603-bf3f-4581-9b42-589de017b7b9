"""
纯TD3基线算法训练脚本
Pure TD3 Baseline Training Script

训练不带任何约束的纯TD3强化学习算法
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pickle

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from environments.paper_environment import PaperSimulationEnvironment
from algorithms.simplified_td3 import SimplifiedTD3

class PureTD3Trainer:
    """纯TD3训练器"""
    
    def __init__(self, config=None, output_dir="results/pure_td3"):
        """
        初始化训练器
        
        Args:
            config: 训练配置
            output_dir: 输出目录
        """
        # 默认配置
        default_config = {
            "total_episodes": 500,
            "max_steps_per_episode": 500,
            "save_interval": 50,
            "eval_interval": 25,
            "eval_episodes": 10,
            "learning_rate": 3e-4,
            "batch_size": 256,
            "buffer_size": 100000,
            "exploration_noise": 0.1,
            "policy_noise": 0.2,
            "noise_clip": 0.5,
            "policy_delay": 2,
            "environment_stages": ["stage1_simple", "stage2_complex", "stage3_dynamic"],
            "progressive_training": True
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"training_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 训练统计
        self.training_stats = {
            "episode_rewards": [],
            "episode_lengths": [],
            "success_rates": [],
            "constraint_violations": [],
            "critic_losses": [],
            "actor_losses": [],
            "eval_results": []
        }
        
        print(f"🚀 纯TD3训练器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 训练配置: {self.config}")
    
    def create_environment(self):
        """创建训练环境"""
        return PaperSimulationEnvironment(self.config["environment_stage"])
    
    def create_agent(self, state_dim, action_dim):
        """创建TD3智能体"""
        return SimplifiedTD3(
            state_dim=state_dim,
            action_dim=action_dim,
            learning_rate=self.config["learning_rate"],
            batch_size=self.config["batch_size"],
            buffer_size=self.config["buffer_size"],
            exploration_noise=self.config["exploration_noise"],
            policy_noise=self.config["policy_noise"],
            noise_clip=self.config["noise_clip"],
            policy_delay=self.config["policy_delay"]
        )
    
    def train(self):
        """渐进式训练TD3算法"""
        print("\n🎯 开始渐进式训练纯TD3算法")

        if self.config["progressive_training"]:
            return self._progressive_train()
        else:
            return self._single_stage_train()

    def _progressive_train(self):
        """渐进式训练"""
        print("📈 渐进式训练模式：简单→复杂→动态")

        # 创建智能体（只创建一次，在不同阶段复用）
        agent = self.create_agent(state_dim=6, action_dim=3)

        total_episodes = 0

        # 渐进式训练各阶段
        for stage_idx, stage in enumerate(self.config["environment_stages"]):
            print(f"\n🏗️ 阶段 {stage_idx + 1}: {stage}")

            # 为每个阶段分配episodes
            stage_episodes = self.config["total_episodes"] // len(self.config["environment_stages"])

            stage_stats = self._train_stage(agent, stage, stage_episodes, total_episodes)
            total_episodes += stage_episodes

            print(f"   ✅ 阶段 {stage_idx + 1} 完成: {stage_episodes} episodes")

        print(f"\n✅ 渐进式训练完成，总episodes: {total_episodes}")

        # 保存最终模型
        self.save_final_model(agent)

        return self.training_stats

    def _train_stage(self, agent, stage, episodes, episode_offset):
        """训练单个阶段"""
        # 创建该阶段的环境
        env = PaperSimulationEnvironment(stage)

        stage_stats = {
            "stage": stage,
            "episodes": episodes,
            "rewards": [],
            "lengths": [],
            "violations": []
        }

        for episode in range(episodes):
            episode_reward = 0
            episode_length = 0
            constraint_violations = 0

            # 重置环境
            state = env.reset()
            done = False

            while not done and episode_length < self.config["max_steps_per_episode"]:
                # 选择动作
                action = agent.select_action(state, add_noise=True)

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 检查约束违反
                if info.get("collision", False) or info.get("boundary_violation", False):
                    constraint_violations += 1
                    reward -= 100  # 约束违反惩罚
                
                # 存储经验
                agent.store_transition(state, action, reward, next_state, done)
                
                # 更新智能体
                if agent.can_update():
                    critic_loss, actor_loss = agent.update()
                    if critic_loss is not None:
                        self.training_stats["critic_losses"].append(critic_loss)
                    if actor_loss is not None:
                        self.training_stats["actor_losses"].append(actor_loss)
                
                state = next_state
                episode_reward += reward
                episode_length += 1
            
            # 记录episode统计
            self.training_stats["episode_rewards"].append(episode_reward)
            self.training_stats["episode_lengths"].append(episode_length)
            self.training_stats["constraint_violations"].append(constraint_violations)

            # 记录阶段统计
            stage_stats["rewards"].append(episode_reward)
            stage_stats["lengths"].append(episode_length)
            stage_stats["violations"].append(constraint_violations)

            # 定期评估
            global_episode = episode_offset + episode + 1
            if (episode + 1) % self.config["eval_interval"] == 0:
                eval_result = self.evaluate_agent(agent, env, global_episode)
                self.training_stats["success_rates"].append(eval_result["success_rate"])

                print(f"   Episode {episode + 1}/{episodes} (全局{global_episode}): "
                      f"奖励={episode_reward:.1f}, "
                      f"长度={episode_length}, "
                      f"成功率={eval_result['success_rate']:.2%}, "
                      f"违反={constraint_violations}")

            # 定期保存
            if (episode + 1) % self.config["save_interval"] == 0:
                self.save_checkpoint(agent, global_episode)

        return stage_stats

    def _single_stage_train(self):
        """单阶段训练（兼容旧版本）"""
        print("🏗️ 单阶段训练模式")

        # 创建环境和智能体
        env = self.create_environment()
        agent = self.create_agent(state_dim=6, action_dim=3)

        print(f"🏗️ 环境: {self.config.get('environment_stage', 'stage2_complex')}")
        print(f"🤖 智能体: TD3 (state_dim=6, action_dim=3)")

        # 使用原有的训练逻辑...
        # 这里可以保留原有的单阶段训练代码

        # 保存最终模型
        self.save_final_model(agent)

        return self.training_stats
    
    def evaluate_agent(self, agent, env, episode):
        """评估智能体性能"""
        eval_rewards = []
        eval_successes = []
        eval_violations = []
        eval_lengths = []
        
        for eval_ep in range(self.config["eval_episodes"]):
            eval_reward = 0
            eval_length = 0
            eval_constraint_violations = 0
            
            state = env.reset()
            done = False
            
            while not done and eval_length < self.config["max_steps_per_episode"]:
                # 评估时不添加噪声
                action = agent.select_action(state, add_noise=False)
                next_state, reward, done, info = env.step(action)
                
                if info.get("collision", False) or info.get("boundary_violation", False):
                    eval_constraint_violations += 1
                
                state = next_state
                eval_reward += reward
                eval_length += 1
            
            # 判断是否成功（到达目标且无碰撞）
            success = info.get("success", False) and eval_constraint_violations == 0
            
            eval_rewards.append(eval_reward)
            eval_successes.append(success)
            eval_violations.append(eval_constraint_violations)
            eval_lengths.append(eval_length)
        
        eval_result = {
            "episode": episode,
            "avg_reward": np.mean(eval_rewards),
            "success_rate": np.mean(eval_successes),
            "avg_violations": np.mean(eval_violations),
            "avg_length": np.mean(eval_lengths),
            "std_reward": np.std(eval_rewards)
        }
        
        self.training_stats["eval_results"].append(eval_result)
        return eval_result
    
    def save_checkpoint(self, agent, episode):
        """保存训练检查点"""
        checkpoint_path = os.path.join(self.output_dir, f"checkpoint_episode_{episode}.pkl")
        
        checkpoint_data = {
            "episode": episode,
            "agent_state": agent.get_state() if hasattr(agent, 'get_state') else None,
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)

    def save_final_model(self, agent):
        """保存最终训练模型"""
        model_path = os.path.join(self.output_dir, "model_final.pkl")

        model_data = {
            "agent_state": agent.get_state() if hasattr(agent, 'get_state') else None,
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat(),
            "model_type": "final"
        }

        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"   💾 保存最终模型: {model_path}")

    def save_final_results(self):
        """保存最终训练结果"""
        # 保存训练统计
        stats_path = os.path.join(self.output_dir, "training_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(self.training_stats, f, indent=2, default=str)
        
        # 保存配置
        config_path = os.path.join(self.output_dir, "training_config.json")
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # 生成训练曲线图
        self.plot_training_curves()
        
        print(f"📊 训练结果保存至: {self.output_dir}")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        
        # 奖励曲线
        if self.training_stats["episode_rewards"]:
            axes[0, 0].plot(self.training_stats["episode_rewards"])
            axes[0, 0].set_title("Episode Rewards")
            axes[0, 0].set_xlabel("Episode")
            axes[0, 0].set_ylabel("Reward")
        
        # 成功率曲线
        if self.training_stats["eval_results"]:
            eval_episodes = [r["episode"] for r in self.training_stats["eval_results"]]
            success_rates = [r["success_rate"] for r in self.training_stats["eval_results"]]
            axes[0, 1].plot(eval_episodes, success_rates)
            axes[0, 1].set_title("Success Rate")
            axes[0, 1].set_xlabel("Episode")
            axes[0, 1].set_ylabel("Success Rate")
        
        # 约束违反
        if self.training_stats["constraint_violations"]:
            axes[0, 2].plot(self.training_stats["constraint_violations"])
            axes[0, 2].set_title("Constraint Violations")
            axes[0, 2].set_xlabel("Episode")
            axes[0, 2].set_ylabel("Violations")
        
        # Episode长度
        if self.training_stats["episode_lengths"]:
            axes[1, 0].plot(self.training_stats["episode_lengths"])
            axes[1, 0].set_title("Episode Lengths")
            axes[1, 0].set_xlabel("Episode")
            axes[1, 0].set_ylabel("Steps")
        
        # Critic损失
        if self.training_stats["critic_losses"]:
            axes[1, 1].plot(self.training_stats["critic_losses"])
            axes[1, 1].set_title("Critic Loss")
            axes[1, 1].set_xlabel("Update Step")
            axes[1, 1].set_ylabel("Loss")
        
        # Actor损失
        if self.training_stats["actor_losses"]:
            axes[1, 2].plot(self.training_stats["actor_losses"])
            axes[1, 2].set_title("Actor Loss")
            axes[1, 2].set_xlabel("Update Step")
            axes[1, 2].set_ylabel("Loss")
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "training_curves.png"), dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    print("🚀 开始训练纯TD3基线算法")
    
    # 训练配置
    config = {
        "total_episodes": 500,
        "max_steps_per_episode": 500,
        "save_interval": 50,
        "eval_interval": 25,
        "eval_episodes": 10,
        "environment_stage": "stage2_complex"
    }
    
    # 创建训练器
    trainer = PureTD3Trainer(config)
    
    # 开始训练
    start_time = datetime.now()
    training_stats = trainer.train()
    end_time = datetime.now()
    training_duration = end_time - start_time
    
    # 保存结果
    trainer.save_final_results()
    
    print(f"\n✅ 纯TD3训练完成!")
    print(f"⏱️ 训练时间: {training_duration}")
    print(f"📊 最终统计:")
    
    if training_stats["episode_rewards"]:
        print(f"   平均奖励: {np.mean(training_stats['episode_rewards'][-50:]):.2f}")
    
    if training_stats["eval_results"]:
        final_success_rate = training_stats["eval_results"][-1]["success_rate"]
        print(f"   最终成功率: {final_success_rate:.2%}")
    
    if training_stats["constraint_violations"]:
        avg_violations = np.mean(training_stats["constraint_violations"][-50:])
        print(f"   平均约束违反: {avg_violations:.2f}")

if __name__ == "__main__":
    main()
