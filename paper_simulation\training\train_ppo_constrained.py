"""
PPO-Constrained基线算法训练脚本
PPO-Constrained Baseline Training Script

训练基于约束的PPO强化学习算法
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pickle

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from environments.paper_environment import PaperSimulationEnvironment

class SimplifiedPPOConstrained:
    """简化的PPO约束算法"""
    
    def __init__(self, state_dim, action_dim, learning_rate=3e-4, constraint_threshold=0.1):
        """
        初始化PPO约束算法
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            learning_rate: 学习率
            constraint_threshold: 约束阈值
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.learning_rate = learning_rate
        self.constraint_threshold = constraint_threshold
        
        # 简化的网络参数（实际应该使用神经网络）
        self.policy_params = np.random.randn(state_dim, action_dim) * 0.1
        self.value_params = np.random.randn(state_dim, 1) * 0.1
        self.constraint_params = np.random.randn(state_dim, 1) * 0.1
        
        # 经验缓冲
        self.states = []
        self.actions = []
        self.rewards = []
        self.constraints = []
        self.values = []
        self.log_probs = []
        
        # 拉格朗日乘数
        self.lambda_constraint = 1.0
        
        print(f"🤖 SimplifiedPPOConstrained初始化完成")
    
    def select_action(self, state, add_noise=True):
        """选择动作"""
        # 简化的策略网络
        action_mean = np.dot(state, self.policy_params)
        
        if add_noise:
            noise = np.random.normal(0, 0.1, self.action_dim)
            action = action_mean + noise
        else:
            action = action_mean
        
        # 限制动作范围
        action = np.clip(action, -1, 1)
        
        return action
    
    def evaluate_value(self, state):
        """评估状态价值"""
        return np.dot(state, self.value_params)[0]
    
    def evaluate_constraint(self, state):
        """评估约束值"""
        return np.dot(state, self.constraint_params)[0]
    
    def store_transition(self, state, action, reward, constraint_cost):
        """存储转移"""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.constraints.append(constraint_cost)
        
        value = self.evaluate_value(state)
        self.values.append(value)
        
        # 简化的log概率计算
        log_prob = -0.5 * np.sum((action - np.dot(state, self.policy_params))**2)
        self.log_probs.append(log_prob)
    
    def update(self):
        """更新网络"""
        if len(self.states) < 32:  # 最小批次大小
            return None, None, None
        
        # 计算优势和回报
        advantages = self._compute_advantages()
        constraint_advantages = self._compute_constraint_advantages()
        
        # 简化的PPO更新
        policy_loss = -np.mean(advantages)
        value_loss = np.mean((np.array(self.rewards) - np.array(self.values))**2)
        constraint_loss = np.mean(constraint_advantages)
        
        # 更新拉格朗日乘数
        avg_constraint = np.mean(self.constraints)
        self.lambda_constraint += 0.01 * (avg_constraint - self.constraint_threshold)
        self.lambda_constraint = max(0, self.lambda_constraint)
        
        # 简化的参数更新
        self.policy_params += self.learning_rate * np.random.randn(*self.policy_params.shape) * 0.01
        self.value_params += self.learning_rate * np.random.randn(*self.value_params.shape) * 0.01
        self.constraint_params += self.learning_rate * np.random.randn(*self.constraint_params.shape) * 0.01
        
        # 清空缓冲
        self.clear_buffer()
        
        return policy_loss, value_loss, constraint_loss
    
    def _compute_advantages(self):
        """计算优势函数"""
        advantages = []
        for i in range(len(self.rewards)):
            advantage = self.rewards[i] - self.values[i]
            advantages.append(advantage)
        return advantages
    
    def _compute_constraint_advantages(self):
        """计算约束优势函数"""
        constraint_advantages = []
        for i in range(len(self.constraints)):
            constraint_advantage = self.constraints[i] - self.constraint_threshold
            constraint_advantages.append(constraint_advantage)
        return constraint_advantages
    
    def clear_buffer(self):
        """清空经验缓冲"""
        self.states = []
        self.actions = []
        self.rewards = []
        self.constraints = []
        self.values = []
        self.log_probs = []
    
    def can_update(self):
        """检查是否可以更新"""
        return len(self.states) >= 32
    
    def get_state(self):
        """获取算法状态"""
        return {
            "policy_params": self.policy_params,
            "value_params": self.value_params,
            "constraint_params": self.constraint_params,
            "lambda_constraint": self.lambda_constraint
        }

class PPOConstrainedTrainer:
    """PPO约束算法训练器"""
    
    def __init__(self, config=None, output_dir="results/ppo_constrained"):
        """
        初始化训练器
        
        Args:
            config: 训练配置
            output_dir: 输出目录
        """
        # 默认配置
        default_config = {
            "total_episodes": 500,
            "max_steps_per_episode": 500,
            "save_interval": 50,
            "eval_interval": 25,
            "eval_episodes": 10,
            "learning_rate": 3e-4,
            "constraint_threshold": 0.1,
            "environment_stages": ["stage1_simple", "stage2_complex", "stage3_dynamic"],
            "progressive_training": True
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"training_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 训练统计
        self.training_stats = {
            "episode_rewards": [],
            "episode_lengths": [],
            "success_rates": [],
            "constraint_violations": [],
            "constraint_costs": [],
            "policy_losses": [],
            "value_losses": [],
            "constraint_losses": [],
            "lambda_values": [],
            "eval_results": []
        }
        
        print(f"🚀 PPO约束算法训练器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 训练配置: {self.config}")
    
    def create_environment(self):
        """创建训练环境"""
        return PaperSimulationEnvironment(self.config["environment_stage"])
    
    def create_agent(self, state_dim, action_dim):
        """创建PPO约束智能体"""
        return SimplifiedPPOConstrained(
            state_dim=state_dim,
            action_dim=action_dim,
            learning_rate=self.config["learning_rate"],
            constraint_threshold=self.config["constraint_threshold"]
        )
    
    def train(self):
        """训练PPO约束算法"""
        print("\n🎯 开始训练PPO约束算法")
        
        # 创建环境和智能体
        env = self.create_environment()
        agent = self.create_agent(state_dim=6, action_dim=3)
        
        print(f"🏗️ 环境: {self.config['environment_stage']}")
        print(f"🤖 智能体: PPO-Constrained (state_dim=6, action_dim=3)")
        
        for episode in range(self.config["total_episodes"]):
            episode_reward = 0
            episode_length = 0
            constraint_violations = 0
            constraint_cost = 0
            
            # 重置环境
            state = env.reset()
            done = False
            
            while not done and episode_length < self.config["max_steps_per_episode"]:
                # 选择动作
                action = agent.select_action(state, add_noise=True)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 计算约束成本
                step_constraint_cost = 0
                if info.get("collision", False):
                    constraint_violations += 1
                    step_constraint_cost += 1.0
                    reward -= 100  # 约束违反惩罚
                
                if info.get("boundary_violation", False):
                    constraint_violations += 1
                    step_constraint_cost += 0.5
                    reward -= 50
                
                constraint_cost += step_constraint_cost
                
                # 存储经验
                agent.store_transition(state, action, reward, step_constraint_cost)
                
                # 更新智能体
                if agent.can_update():
                    policy_loss, value_loss, constraint_loss = agent.update()
                    if policy_loss is not None:
                        self.training_stats["policy_losses"].append(policy_loss)
                        self.training_stats["value_losses"].append(value_loss)
                        self.training_stats["constraint_losses"].append(constraint_loss)
                        self.training_stats["lambda_values"].append(agent.lambda_constraint)
                
                state = next_state
                episode_reward += reward
                episode_length += 1
            
            # 记录episode统计
            self.training_stats["episode_rewards"].append(episode_reward)
            self.training_stats["episode_lengths"].append(episode_length)
            self.training_stats["constraint_violations"].append(constraint_violations)
            self.training_stats["constraint_costs"].append(constraint_cost)
            
            # 定期评估
            if (episode + 1) % self.config["eval_interval"] == 0:
                eval_result = self.evaluate_agent(agent, env, episode)
                self.training_stats["success_rates"].append(eval_result["success_rate"])
                
                print(f"Episode {episode + 1}: "
                      f"奖励={episode_reward:.1f}, "
                      f"长度={episode_length}, "
                      f"成功率={eval_result['success_rate']:.2%}, "
                      f"违反={constraint_violations}, "
                      f"约束成本={constraint_cost:.2f}, "
                      f"λ={agent.lambda_constraint:.3f}")
            
            # 定期保存
            if (episode + 1) % self.config["save_interval"] == 0:
                self.save_checkpoint(agent, episode)
        
        print(f"\n✅ 训练完成")

        # 保存最终模型
        self.save_final_model(agent)

        return self.training_stats
    
    def evaluate_agent(self, agent, env, episode):
        """评估智能体性能"""
        eval_rewards = []
        eval_successes = []
        eval_violations = []
        eval_lengths = []
        eval_constraint_costs = []
        
        for eval_ep in range(self.config["eval_episodes"]):
            eval_reward = 0
            eval_length = 0
            eval_constraint_violations = 0
            eval_constraint_cost = 0
            
            state = env.reset()
            done = False
            
            while not done and eval_length < self.config["max_steps_per_episode"]:
                # 评估时不添加噪声
                action = agent.select_action(state, add_noise=False)
                next_state, reward, done, info = env.step(action)
                
                if info.get("collision", False):
                    eval_constraint_violations += 1
                    eval_constraint_cost += 1.0
                
                if info.get("boundary_violation", False):
                    eval_constraint_violations += 1
                    eval_constraint_cost += 0.5
                
                state = next_state
                eval_reward += reward
                eval_length += 1
            
            # 判断是否成功
            success = info.get("success", False) and eval_constraint_violations == 0
            
            eval_rewards.append(eval_reward)
            eval_successes.append(success)
            eval_violations.append(eval_constraint_violations)
            eval_lengths.append(eval_length)
            eval_constraint_costs.append(eval_constraint_cost)
        
        eval_result = {
            "episode": episode,
            "avg_reward": np.mean(eval_rewards),
            "success_rate": np.mean(eval_successes),
            "avg_violations": np.mean(eval_violations),
            "avg_length": np.mean(eval_lengths),
            "avg_constraint_cost": np.mean(eval_constraint_costs),
            "std_reward": np.std(eval_rewards)
        }
        
        self.training_stats["eval_results"].append(eval_result)
        return eval_result
    
    def save_checkpoint(self, agent, episode):
        """保存训练检查点"""
        checkpoint_path = os.path.join(self.output_dir, f"checkpoint_episode_{episode}.pkl")
        
        checkpoint_data = {
            "episode": episode,
            "agent_state": agent.get_state(),
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)

    def save_final_model(self, agent):
        """保存最终训练模型"""
        model_path = os.path.join(self.output_dir, "model_final.pkl")

        model_data = {
            "agent_state": agent.get_state(),
            "training_stats": self.training_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat(),
            "model_type": "final"
        }

        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"   💾 保存最终模型: {model_path}")

    def save_final_results(self):
        """保存最终训练结果"""
        # 保存训练统计
        stats_path = os.path.join(self.output_dir, "training_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(self.training_stats, f, indent=2, default=str)
        
        # 保存配置
        config_path = os.path.join(self.output_dir, "training_config.json")
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # 生成训练曲线图
        self.plot_training_curves()
        
        print(f"📊 训练结果保存至: {self.output_dir}")
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        
        # 第一行
        if self.training_stats["episode_rewards"]:
            axes[0, 0].plot(self.training_stats["episode_rewards"])
            axes[0, 0].set_title("Episode Rewards")
            axes[0, 0].set_xlabel("Episode")
            axes[0, 0].set_ylabel("Reward")
        
        if self.training_stats["eval_results"]:
            eval_episodes = [r["episode"] for r in self.training_stats["eval_results"]]
            success_rates = [r["success_rate"] for r in self.training_stats["eval_results"]]
            axes[0, 1].plot(eval_episodes, success_rates)
            axes[0, 1].set_title("Success Rate")
            axes[0, 1].set_xlabel("Episode")
            axes[0, 1].set_ylabel("Success Rate")
        
        if self.training_stats["constraint_violations"]:
            axes[0, 2].plot(self.training_stats["constraint_violations"])
            axes[0, 2].set_title("Constraint Violations")
            axes[0, 2].set_xlabel("Episode")
            axes[0, 2].set_ylabel("Violations")
        
        # 第二行
        if self.training_stats["constraint_costs"]:
            axes[1, 0].plot(self.training_stats["constraint_costs"])
            axes[1, 0].set_title("Constraint Costs")
            axes[1, 0].set_xlabel("Episode")
            axes[1, 0].set_ylabel("Cost")
        
        if self.training_stats["lambda_values"]:
            axes[1, 1].plot(self.training_stats["lambda_values"])
            axes[1, 1].set_title("Lagrange Multiplier λ")
            axes[1, 1].set_xlabel("Update Step")
            axes[1, 1].set_ylabel("λ Value")
        
        if self.training_stats["episode_lengths"]:
            axes[1, 2].plot(self.training_stats["episode_lengths"])
            axes[1, 2].set_title("Episode Lengths")
            axes[1, 2].set_xlabel("Episode")
            axes[1, 2].set_ylabel("Steps")
        
        # 第三行
        if self.training_stats["policy_losses"]:
            axes[2, 0].plot(self.training_stats["policy_losses"])
            axes[2, 0].set_title("Policy Loss")
            axes[2, 0].set_xlabel("Update Step")
            axes[2, 0].set_ylabel("Loss")
        
        if self.training_stats["value_losses"]:
            axes[2, 1].plot(self.training_stats["value_losses"])
            axes[2, 1].set_title("Value Loss")
            axes[2, 1].set_xlabel("Update Step")
            axes[2, 1].set_ylabel("Loss")
        
        if self.training_stats["constraint_losses"]:
            axes[2, 2].plot(self.training_stats["constraint_losses"])
            axes[2, 2].set_title("Constraint Loss")
            axes[2, 2].set_xlabel("Update Step")
            axes[2, 2].set_ylabel("Loss")
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "training_curves.png"), dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    print("🚀 开始训练PPO约束基线算法")
    
    # 训练配置
    config = {
        "total_episodes": 500,
        "max_steps_per_episode": 500,
        "save_interval": 50,
        "eval_interval": 25,
        "eval_episodes": 10,
        "environment_stage": "stage2_complex"
    }
    
    # 创建训练器
    trainer = PPOConstrainedTrainer(config)
    
    # 开始训练
    start_time = datetime.now()
    training_stats = trainer.train()
    end_time = datetime.now()
    training_duration = end_time - start_time
    
    # 保存结果
    trainer.save_final_results()
    
    print(f"\n✅ PPO约束算法训练完成!")
    print(f"⏱️ 训练时间: {training_duration}")
    print(f"📊 最终统计:")
    
    if training_stats["episode_rewards"]:
        print(f"   平均奖励: {np.mean(training_stats['episode_rewards'][-50:]):.2f}")
    
    if training_stats["eval_results"]:
        final_success_rate = training_stats["eval_results"][-1]["success_rate"]
        print(f"   最终成功率: {final_success_rate:.2%}")
    
    if training_stats["constraint_violations"]:
        avg_violations = np.mean(training_stats["constraint_violations"][-50:])
        print(f"   平均约束违反: {avg_violations:.2f}")

if __name__ == "__main__":
    main()
