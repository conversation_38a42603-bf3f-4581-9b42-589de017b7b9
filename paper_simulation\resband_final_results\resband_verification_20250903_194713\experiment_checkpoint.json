{"completed_methods": ["resband_adaptive", "fixed_coarse", "fixed_fine", "heuristic_schedule"], "timestamp": "2025-09-03T19:54:07.883484", "config": {"training_episodes": 600, "evaluation_episodes": 50, "max_steps_per_episode": 2000, "test_scenarios": ["stage1_simple", "stage2_complex", "stage3_dynamic"], "progressive_training": true, "episodes_per_stage": 200, "comparison_methods": ["resband_adaptive", "fixed_coarse", "fixed_fine", "heuristic_schedule"], "bounds": [2000, 2000, 2000], "start_pos": [200.0, 200.0, 200.0], "goal_pos": [1800.0, 1800.0, 1800.0], "dt": 0.1, "V_cruise": 25.0, "a_T_max": 8.0, "a_N_max": 39.24, "gamma_max": 1.047}}