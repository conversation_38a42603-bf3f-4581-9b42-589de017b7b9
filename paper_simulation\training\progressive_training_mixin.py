"""
渐进式训练混入类
Progressive Training Mixin

为所有基线方法提供统一的渐进式训练功能
简单场景 → 复杂场景 → 复杂动态场景
"""

import os
import sys
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from environments.paper_environment import PaperSimulationEnvironment

class ProgressiveTrainingMixin:
    """渐进式训练混入类"""
    
    def progressive_train(self, agent, config):
        """
        渐进式训练主方法
        
        Args:
            agent: 训练的智能体
            config: 训练配置，必须包含environment_stages
        
        Returns:
            训练统计数据
        """
        print("📈 渐进式训练模式：简单→复杂→动态")
        
        if not config.get("progressive_training", False):
            return self._single_stage_train(agent, config)
        
        stages = config.get("environment_stages", ["stage1_simple", "stage2_complex", "stage3_dynamic"])
        total_episodes = config.get("total_episodes", 500)
        
        total_trained_episodes = 0
        
        # 渐进式训练各阶段
        for stage_idx, stage in enumerate(stages):
            print(f"\n🏗️ 阶段 {stage_idx + 1}/{len(stages)}: {stage}")
            
            # 为每个阶段分配episodes
            stage_episodes = total_episodes // len(stages)
            if stage_idx == len(stages) - 1:  # 最后一个阶段获得剩余的episodes
                stage_episodes = total_episodes - total_trained_episodes
            
            stage_stats = self._train_stage(
                agent=agent,
                stage=stage,
                episodes=stage_episodes,
                episode_offset=total_trained_episodes,
                config=config
            )
            
            total_trained_episodes += stage_episodes
            
            print(f"   ✅ 阶段 {stage_idx + 1} 完成: {stage_episodes} episodes")
            print(f"   📊 阶段统计: 平均奖励={stage_stats['avg_reward']:.1f}, "
                  f"平均长度={stage_stats['avg_length']:.1f}, "
                  f"平均违反={stage_stats['avg_violations']:.1f}")
        
        print(f"\n✅ 渐进式训练完成，总episodes: {total_trained_episodes}")
        return self.training_stats
    
    def _train_stage(self, agent, stage, episodes, episode_offset, config):
        """
        训练单个阶段
        
        Args:
            agent: 智能体
            stage: 环境阶段名称
            episodes: 该阶段的训练episodes
            episode_offset: episode偏移量（用于全局episode计数）
            config: 训练配置
        
        Returns:
            阶段统计数据
        """
        # 创建该阶段的环境
        env = PaperSimulationEnvironment(stage)
        
        stage_stats = {
            "stage": stage,
            "episodes": episodes,
            "rewards": [],
            "lengths": [],
            "violations": [],
            "avg_reward": 0,
            "avg_length": 0,
            "avg_violations": 0
        }
        
        max_steps = config.get("max_steps_per_episode", 500)
        eval_interval = config.get("eval_interval", 25)
        save_interval = config.get("save_interval", 50)
        
        for episode in range(episodes):
            episode_reward = 0
            episode_length = 0
            constraint_violations = 0
            
            # 重置环境
            state = env.reset()
            done = False
            
            while not done and episode_length < max_steps:
                # 选择动作（子类需要实现具体的动作选择逻辑）
                action = self._select_action_for_training(agent, state)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 存储经验（子类需要实现具体的经验存储逻辑）
                self._store_experience_for_training(agent, state, action, reward, next_state, done)
                
                # 更新智能体（子类需要实现具体的更新逻辑）
                self._update_agent_for_training(agent)
                
                # 检查约束违反
                if info.get("collision", False) or info.get("boundary_violation", False):
                    constraint_violations += 1
                
                state = next_state
                episode_reward += reward
                episode_length += 1
            
            # 记录统计
            self.training_stats["episode_rewards"].append(episode_reward)
            self.training_stats["episode_lengths"].append(episode_length)
            self.training_stats["constraint_violations"].append(constraint_violations)
            
            stage_stats["rewards"].append(episode_reward)
            stage_stats["lengths"].append(episode_length)
            stage_stats["violations"].append(constraint_violations)
            
            # 定期评估
            global_episode = episode_offset + episode + 1
            if (episode + 1) % eval_interval == 0:
                eval_result = self._evaluate_agent_for_training(agent, env, global_episode)
                if hasattr(self, 'training_stats') and "success_rates" in self.training_stats:
                    self.training_stats["success_rates"].append(eval_result["success_rate"])
                
                print(f"   Episode {episode + 1}/{episodes} (全局{global_episode}): "
                      f"奖励={episode_reward:.1f}, "
                      f"长度={episode_length}, "
                      f"成功率={eval_result['success_rate']:.2%}, "
                      f"违反={constraint_violations}")
            
            # 定期保存
            if (episode + 1) % save_interval == 0:
                self._save_checkpoint_for_training(agent, global_episode)
        
        # 计算阶段统计
        if stage_stats["rewards"]:
            stage_stats["avg_reward"] = sum(stage_stats["rewards"]) / len(stage_stats["rewards"])
            stage_stats["avg_length"] = sum(stage_stats["lengths"]) / len(stage_stats["lengths"])
            stage_stats["avg_violations"] = sum(stage_stats["violations"]) / len(stage_stats["violations"])
        
        return stage_stats
    
    def _single_stage_train(self, agent, config):
        """单阶段训练（兼容性方法）"""
        print("🏗️ 单阶段训练模式")
        
        stage = config.get("environment_stage", "stage2_complex")
        episodes = config.get("total_episodes", 500)
        
        return self._train_stage(
            agent=agent,
            stage=stage,
            episodes=episodes,
            episode_offset=0,
            config=config
        )
    
    # 以下方法需要在子类中实现
    def _select_action_for_training(self, agent, state):
        """选择动作（子类实现）"""
        if hasattr(agent, 'select_action'):
            return agent.select_action(state, add_noise=True)
        else:
            raise NotImplementedError("子类必须实现 _select_action_for_training 方法")
    
    def _store_experience_for_training(self, agent, state, action, reward, next_state, done):
        """存储经验（子类实现）"""
        if hasattr(agent, 'store_transition'):
            agent.store_transition(state, action, reward, next_state, done)
        # 如果智能体没有经验存储，则跳过
    
    def _update_agent_for_training(self, agent):
        """更新智能体（子类实现）"""
        if hasattr(agent, 'update') and hasattr(agent, 'can_update'):
            if agent.can_update():
                agent.update()
        # 如果智能体没有更新方法，则跳过
    
    def _evaluate_agent_for_training(self, agent, env, episode):
        """评估智能体（子类可重写）"""
        # 默认的简单评估
        eval_episodes = 5
        successes = 0
        
        for _ in range(eval_episodes):
            state = env.reset()
            done = False
            steps = 0
            
            while not done and steps < 200:
                action = self._select_action_for_training(agent, state)
                next_state, reward, done, info = env.step(action)
                state = next_state
                steps += 1
            
            if info.get("success", False):
                successes += 1
        
        return {
            "success_rate": successes / eval_episodes,
            "episode": episode
        }
    
    def _save_checkpoint_for_training(self, agent, episode):
        """保存检查点（子类可重写）"""
        if hasattr(self, 'save_checkpoint'):
            self.save_checkpoint(agent, episode)
        # 如果没有保存方法，则跳过

def main():
    """测试渐进式训练混入类"""
    print("🧪 渐进式训练混入类测试")
    
    # 这里可以添加测试代码
    print("✅ 混入类定义完成")

if __name__ == "__main__":
    main()
