"""
反直觉场景验证实验
Counterintuitive Scenario Validation Experiment

验证ResBand算法在复杂场景中发现最优分辨率的能力
特别是验证在看似复杂但实际简单的场景中，粗分辨率可能表现更好的反直觉现象
"""

import numpy as np
import json
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Tuple

# 添加路径以导入核心模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from core.loitering_munition_environment import PaperSimulationEnvironment
    from algorithms.simplified_resband import EnhancedResBand
    from algorithms.basic_mlacf import EnhancedMLACF
    from algorithms.lightweight_dwa_rl import LightweightDWARL
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在paper_simulation目录下运行此脚本")
    sys.exit(1)

class CounterIntuitiveScenarioValidator:
    """反直觉场景验证器"""
    
    def __init__(self, output_dir="results/counterintuitive_validation"):
        """
        初始化验证器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 实验配置
        self.config = {
            "episodes_per_scenario": 50,
            "num_counterintuitive_scenarios": 10,
            "resolution_test_episodes": 20,
            "validation_threshold": 0.05  # 性能差异阈值
        }
        
        # 结果存储
        self.validation_results = {
            "scenarios": [],
            "resolution_performance": {},
            "counterintuitive_discoveries": [],
            "statistical_analysis": {}
        }
        
        print(f"🔍 反直觉场景验证器初始化完成")
        print(f"   输出目录: {output_dir}")
        print(f"   每场景测试episodes: {self.config['episodes_per_scenario']}")
    
    def generate_counterintuitive_scenarios(self):
        """
        生成反直觉场景
        
        Returns:
            scenarios: 反直觉场景列表
        """
        scenarios = []
        
        # 场景类型1: 高密度但低路径干扰
        for i in range(3):
            scenario = {
                "name": f"高密度低干扰_{i+1}",
                "type": "high_density_low_interference",
                "description": "障碍物密度高但不影响直线路径",
                "static_obstacles": self._generate_high_density_low_interference_obstacles(),
                "dynamic_obstacles": [],
                "expected_optimal": "coarse",  # 预期粗分辨率最优
                "reasoning": "障碍物多但实际不影响路径，粗分辨率足够且更高效"
            }
            scenarios.append(scenario)
        
        # 场景类型2: 复杂分布但简单路径
        for i in range(3):
            scenario = {
                "name": f"复杂分布简单路径_{i+1}",
                "type": "complex_distribution_simple_path",
                "description": "障碍物分布复杂但路径相对简单",
                "static_obstacles": self._generate_complex_distribution_simple_path_obstacles(),
                "dynamic_obstacles": [],
                "expected_optimal": "coarse",
                "reasoning": "复杂分布但路径简单，粗分辨率可能更稳定"
            }
            scenarios.append(scenario)
        
        # 场景类型3: 高约束静态场景
        for i in range(2):
            scenario = {
                "name": f"高约束静态_{i+1}",
                "type": "high_constraint_static",
                "description": "空间约束高但全为静态障碍物",
                "static_obstacles": self._generate_high_constraint_static_obstacles(),
                "dynamic_obstacles": [],
                "expected_optimal": "coarse",
                "reasoning": "静态场景中粗分辨率可能更稳定，避免过度精细控制"
            }
            scenarios.append(scenario)
        
        # 场景类型4: 对照组 - 真正复杂场景
        for i in range(2):
            scenario = {
                "name": f"真复杂对照_{i+1}",
                "type": "truly_complex_control",
                "description": "真正需要精细控制的复杂场景",
                "static_obstacles": self._generate_truly_complex_obstacles(),
                "dynamic_obstacles": self._generate_dynamic_obstacles(),
                "expected_optimal": "fine",  # 预期精细分辨率最优
                "reasoning": "真正复杂场景需要精细控制"
            }
            scenarios.append(scenario)
        
        return scenarios
    
    def _generate_high_density_low_interference_obstacles(self):
        """生成高密度但低路径干扰的障碍物"""
        obstacles = []
        
        # 在路径两侧密集放置障碍物，但不阻挡直线路径
        for i in range(15):  # 高密度
            # 左侧障碍物
            x = np.random.uniform(500, 1500)
            y = np.random.uniform(-800, -200)  # 远离路径
            z = np.random.uniform(-200, 200)
            obstacles.append({
                "position": [x, y, z],
                "radius": np.random.uniform(30, 80),
                "type": "static"
            })
            
            # 右侧障碍物
            x = np.random.uniform(500, 1500)
            y = np.random.uniform(200, 800)  # 远离路径
            z = np.random.uniform(-200, 200)
            obstacles.append({
                "position": [x, y, z],
                "radius": np.random.uniform(30, 80),
                "type": "static"
            })
        
        return obstacles
    
    def _generate_complex_distribution_simple_path(self):
        """生成复杂分布但简单路径的障碍物"""
        obstacles = []
        
        # 创建复杂的障碍物分布，但在路径上留出通道
        for i in range(12):
            # 随机位置，但避开路径中心区域
            while True:
                x = np.random.uniform(200, 2800)
                y = np.random.uniform(-1000, 1000)
                z = np.random.uniform(-300, 300)
                
                # 确保不在路径中心区域 (y在-100到100之间)
                if abs(y) > 150:
                    break
            
            obstacles.append({
                "position": [x, y, z],
                "radius": np.random.uniform(40, 120),  # 大小变化大，增加复杂度
                "type": "static"
            })
        
        return obstacles
    
    def _generate_high_constraint_static_obstacles(self):
        """生成高约束静态障碍物"""
        obstacles = []
        
        # 在有限区域内密集放置障碍物
        for i in range(10):
            x = np.random.uniform(800, 1200)  # 集中在中间区域
            y = np.random.uniform(-300, 300)
            z = np.random.uniform(-150, 150)
            
            obstacles.append({
                "position": [x, y, z],
                "radius": np.random.uniform(60, 100),  # 较大的障碍物
                "type": "static"
            })
        
        return obstacles
    
    def _generate_truly_complex_obstacles(self):
        """生成真正复杂的障碍物"""
        obstacles = []
        
        # 在路径上放置需要精细避障的障碍物
        for i in range(8):
            x = np.random.uniform(500, 2500)
            y = np.random.uniform(-150, 150)  # 在路径附近
            z = np.random.uniform(-100, 100)
            
            obstacles.append({
                "position": [x, y, z],
                "radius": np.random.uniform(50, 90),
                "type": "static"
            })
        
        return obstacles
    
    def _generate_dynamic_obstacles(self):
        """生成动态障碍物"""
        obstacles = []
        
        for i in range(3):
            x = np.random.uniform(800, 2000)
            y = np.random.uniform(-200, 200)
            z = np.random.uniform(-100, 100)
            
            obstacles.append({
                "position": [x, y, z],
                "radius": np.random.uniform(40, 70),
                "motion_type": "circular",
                "motion_params": {
                    "center": [x, y, z],
                    "radius": 100,
                    "angular_velocity": 0.1
                }
            })
        
        return obstacles
    
    def run_validation_experiment(self):
        """
        运行反直觉场景验证实验
        
        Returns:
            results: 验证结果
        """
        print(f"\n🚀 开始反直觉场景验证实验")
        print(f"   时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 生成测试场景
        scenarios = self.generate_counterintuitive_scenarios()
        print(f"   生成场景数: {len(scenarios)}")
        
        # 对每个场景进行验证
        for i, scenario in enumerate(scenarios):
            print(f"\n📊 测试场景 {i+1}/{len(scenarios)}: {scenario['name']}")
            
            scenario_results = self._test_scenario_with_all_resolutions(scenario)
            scenario_results["scenario_info"] = scenario
            
            self.validation_results["scenarios"].append(scenario_results)
            
            # 分析是否发现反直觉现象
            counterintuitive_analysis = self._analyze_counterintuitive_discovery(scenario_results)
            if counterintuitive_analysis["is_counterintuitive"]:
                self.validation_results["counterintuitive_discoveries"].append(counterintuitive_analysis)
                print(f"   🎯 发现反直觉现象: {counterintuitive_analysis['description']}")
        
        # 统计分析
        self._perform_statistical_analysis()
        
        # 保存结果
        self._save_validation_results()
        
        print(f"\n✅ 反直觉场景验证实验完成")
        print(f"   发现反直觉场景: {len(self.validation_results['counterintuitive_discoveries'])}")
        print(f"   结果保存至: {self.output_dir}")
        
        return self.validation_results

    def _test_scenario_with_all_resolutions(self, scenario):
        """
        使用所有分辨率测试场景

        Args:
            scenario: 场景配置

        Returns:
            results: 测试结果
        """
        results = {
            "scenario_name": scenario["name"],
            "resolution_performance": {},
            "best_resolution": None,
            "performance_differences": {},
            "statistical_significance": {}
        }

        # 定义分辨率配置
        resolutions = {
            "coarse": {"delta_a_T": 8.0, "delta_a_N": 25.0, "delta_mu": 1.0},
            "medium": {"delta_a_T": 4.0, "delta_a_N": 15.0, "delta_mu": 0.5},
            "fine": {"delta_a_T": 2.0, "delta_a_N": 8.0, "delta_mu": 0.3}
        }

        # 测试每个分辨率
        for res_name, res_config in resolutions.items():
            print(f"     测试{res_name}分辨率...")

            performance_data = self._test_single_resolution(scenario, res_config, res_name)
            results["resolution_performance"][res_name] = performance_data

        # 找出最佳分辨率
        best_resolution = None
        best_avg_reward = -np.inf

        for res_name, perf_data in results["resolution_performance"].items():
            avg_reward = np.mean(perf_data["episode_rewards"])
            if avg_reward > best_avg_reward:
                best_avg_reward = avg_reward
                best_resolution = res_name

        results["best_resolution"] = best_resolution

        # 计算性能差异
        for res_name, perf_data in results["resolution_performance"].items():
            if res_name != best_resolution:
                best_rewards = results["resolution_performance"][best_resolution]["episode_rewards"]
                current_rewards = perf_data["episode_rewards"]

                # 计算平均差异
                avg_diff = np.mean(best_rewards) - np.mean(current_rewards)
                results["performance_differences"][res_name] = avg_diff

                # 简单的统计显著性检验（t检验）
                from scipy import stats
                try:
                    t_stat, p_value = stats.ttest_ind(best_rewards, current_rewards)
                    results["statistical_significance"][res_name] = {
                        "t_statistic": t_stat,
                        "p_value": p_value,
                        "significant": p_value < 0.05
                    }
                except:
                    results["statistical_significance"][res_name] = {
                        "t_statistic": 0,
                        "p_value": 1.0,
                        "significant": False
                    }

        return results

    def _test_single_resolution(self, scenario, resolution_config, resolution_name):
        """
        测试单个分辨率配置

        Args:
            scenario: 场景配置
            resolution_config: 分辨率配置
            resolution_name: 分辨率名称

        Returns:
            performance_data: 性能数据
        """
        # 创建环境
        env_config = {
            "static_obstacles": scenario["static_obstacles"],
            "dynamic_obstacles": scenario["dynamic_obstacles"],
            "space_size": 3000,
            "start_pos": [0, 0, 0],
            "target_pos": [3000, 0, 0]
        }

        environment = PaperSimulationEnvironment(env_config)

        # 创建DWA-RL系统（固定分辨率）
        dwa_rl_config = {
            "state_dim": 6,
            "action_dim": 3,
            "dwa_enabled": True,
            "resband_enabled": False,  # 禁用ResBand，使用固定分辨率
            "fixed_resolution": resolution_config
        }

        dwa_rl = LightweightDWARL(environment, dwa_rl_config)

        # 运行测试episodes
        episode_rewards = []
        episode_lengths = []
        success_count = 0
        constraint_violations = []

        for episode in range(self.config["resolution_test_episodes"]):
            episode_info = dwa_rl.train_episode(max_steps=500, training_progress=1.0)

            episode_rewards.append(episode_info["episode_reward"])
            episode_lengths.append(episode_info["episode_length"])

            if episode_info.get("success", False):
                success_count += 1

            constraint_violations.append(episode_info.get("constraint_violations", 0))

        performance_data = {
            "episode_rewards": episode_rewards,
            "episode_lengths": episode_lengths,
            "success_rate": success_count / len(episode_rewards),
            "avg_reward": np.mean(episode_rewards),
            "std_reward": np.std(episode_rewards),
            "avg_length": np.mean(episode_lengths),
            "total_violations": sum(constraint_violations),
            "avg_violations": np.mean(constraint_violations)
        }

        return performance_data

    def _analyze_counterintuitive_discovery(self, scenario_results):
        """
        分析是否发现反直觉现象

        Args:
            scenario_results: 场景测试结果

        Returns:
            analysis: 反直觉分析结果
        """
        scenario_info = scenario_results["scenario_info"]
        expected_optimal = scenario_info["expected_optimal"]
        actual_best = scenario_results["best_resolution"]

        analysis = {
            "scenario_name": scenario_info["name"],
            "scenario_type": scenario_info["type"],
            "expected_optimal": expected_optimal,
            "actual_best": actual_best,
            "is_counterintuitive": False,
            "description": "",
            "performance_data": scenario_results["resolution_performance"],
            "significance": {}
        }

        # 检查是否为反直觉现象
        if expected_optimal == "coarse" and actual_best == "coarse":
            # 符合预期的反直觉现象
            analysis["is_counterintuitive"] = True
            analysis["description"] = f"验证反直觉假设：{scenario_info['reasoning']}"

            # 检查与其他分辨率的性能差异是否显著
            coarse_performance = scenario_results["resolution_performance"]["coarse"]
            fine_performance = scenario_results["resolution_performance"]["fine"]

            performance_improvement = coarse_performance["avg_reward"] - fine_performance["avg_reward"]

            if performance_improvement > self.config["validation_threshold"]:
                analysis["description"] += f" (性能提升: {performance_improvement:.2f})"
                analysis["significance"]["performance_improvement"] = performance_improvement

                # 检查统计显著性
                if "fine" in scenario_results["statistical_significance"]:
                    sig_data = scenario_results["statistical_significance"]["fine"]
                    if sig_data["significant"]:
                        analysis["description"] += " [统计显著]"
                        analysis["significance"]["statistically_significant"] = True

        elif expected_optimal == "fine" and actual_best != "coarse":
            # 对照组：真正复杂场景应该需要精细分辨率
            analysis["is_counterintuitive"] = False
            analysis["description"] = "对照组验证：复杂场景确实需要精细控制"

        elif expected_optimal == "coarse" and actual_best != "coarse":
            # 预期反直觉但实际不是
            analysis["is_counterintuitive"] = False
            analysis["description"] = f"预期反直觉但实际{actual_best}分辨率最优"

        return analysis

    def _perform_statistical_analysis(self):
        """执行统计分析"""
        analysis = {
            "total_scenarios": len(self.validation_results["scenarios"]),
            "counterintuitive_count": len(self.validation_results["counterintuitive_discoveries"]),
            "counterintuitive_rate": 0,
            "resolution_distribution": {"coarse": 0, "medium": 0, "fine": 0},
            "average_performance_improvements": {},
            "statistical_significance_rate": 0
        }

        if analysis["total_scenarios"] > 0:
            analysis["counterintuitive_rate"] = analysis["counterintuitive_count"] / analysis["total_scenarios"]

        # 统计最优分辨率分布
        for scenario_result in self.validation_results["scenarios"]:
            best_res = scenario_result["best_resolution"]
            if best_res in analysis["resolution_distribution"]:
                analysis["resolution_distribution"][best_res] += 1

        # 统计性能改善
        performance_improvements = []
        significant_cases = 0

        for discovery in self.validation_results["counterintuitive_discoveries"]:
            if "performance_improvement" in discovery["significance"]:
                performance_improvements.append(discovery["significance"]["performance_improvement"])

            if discovery["significance"].get("statistically_significant", False):
                significant_cases += 1

        if performance_improvements:
            analysis["average_performance_improvements"] = {
                "mean": np.mean(performance_improvements),
                "std": np.std(performance_improvements),
                "min": np.min(performance_improvements),
                "max": np.max(performance_improvements)
            }

        if analysis["counterintuitive_count"] > 0:
            analysis["statistical_significance_rate"] = significant_cases / analysis["counterintuitive_count"]

        self.validation_results["statistical_analysis"] = analysis

    def _save_validation_results(self):
        """保存验证结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存详细结果
        results_file = os.path.join(self.output_dir, f"counterintuitive_validation_{timestamp}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, indent=2, ensure_ascii=False, default=str)

        # 保存简化报告
        report_file = os.path.join(self.output_dir, f"validation_report_{timestamp}.md")
        self._generate_validation_report(report_file)

        print(f"   📄 详细结果: {results_file}")
        print(f"   📋 验证报告: {report_file}")

    def _generate_validation_report(self, report_file):
        """生成验证报告"""
        analysis = self.validation_results["statistical_analysis"]

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 反直觉场景验证实验报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## 实验概述\n\n")
            f.write(f"- 总测试场景数: {analysis['total_scenarios']}\n")
            f.write(f"- 发现反直觉场景数: {analysis['counterintuitive_count']}\n")
            f.write(f"- 反直觉发现率: {analysis['counterintuitive_rate']:.2%}\n")
            f.write(f"- 统计显著性率: {analysis['statistical_significance_rate']:.2%}\n\n")

            f.write("## 最优分辨率分布\n\n")
            for res_type, count in analysis['resolution_distribution'].items():
                percentage = count / analysis['total_scenarios'] * 100 if analysis['total_scenarios'] > 0 else 0
                f.write(f"- {res_type}分辨率: {count}次 ({percentage:.1f}%)\n")

            f.write("\n## 反直觉场景发现\n\n")
            for i, discovery in enumerate(self.validation_results["counterintuitive_discoveries"]):
                f.write(f"### 场景 {i+1}: {discovery['scenario_name']}\n\n")
                f.write(f"- **场景类型**: {discovery['scenario_type']}\n")
                f.write(f"- **预期最优**: {discovery['expected_optimal']}\n")
                f.write(f"- **实际最优**: {discovery['actual_best']}\n")
                f.write(f"- **发现描述**: {discovery['description']}\n\n")

            if analysis.get("average_performance_improvements"):
                perf_data = analysis["average_performance_improvements"]
                f.write("## 性能改善统计\n\n")
                f.write(f"- 平均性能改善: {perf_data['mean']:.2f}\n")
                f.write(f"- 标准差: {perf_data['std']:.2f}\n")
                f.write(f"- 最大改善: {perf_data['max']:.2f}\n")
                f.write(f"- 最小改善: {perf_data['min']:.2f}\n\n")

            f.write("## 结论\n\n")
            if analysis['counterintuitive_count'] > 0:
                f.write("✅ **成功验证ResBand算法的反直觉场景发现能力**\n\n")
                f.write("ResBand算法能够在看似复杂但实际简单的场景中发现粗分辨率的优势，")
                f.write("证明了算法的智能学习能力，避免了基于直觉的错误分辨率选择。\n")
            else:
                f.write("⚠️ **未发现明显的反直觉现象**\n\n")
                f.write("可能需要调整场景设计或增加测试样本数量。\n")

def main():
    """主函数"""
    print("🔍 反直觉场景验证实验")

    # 创建验证器
    validator = CounterIntuitiveScenarioValidator()

    # 运行验证实验
    results = validator.run_validation_experiment()

    # 输出关键结果
    analysis = results["statistical_analysis"]
    print(f"\n📊 实验结果摘要:")
    print(f"   反直觉发现率: {analysis['counterintuitive_rate']:.2%}")
    print(f"   统计显著性率: {analysis['statistical_significance_rate']:.2%}")

    if analysis['counterintuitive_count'] > 0:
        print(f"   ✅ 成功验证ResBand的反直觉场景发现能力")
    else:
        print(f"   ⚠️ 未发现明显反直觉现象，建议调整实验设计")

if __name__ == "__main__":
    main()
