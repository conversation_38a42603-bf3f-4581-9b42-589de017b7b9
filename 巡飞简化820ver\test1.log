This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.11.28)  3 SEP 2025 21:46
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**e:/basic_dwa_rl_framework_717ver/巡飞简化820ver/test1.tex
(e:/basic_dwa_rl_framework_717ver/巡飞简化820ver/test1.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexart.cls (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (e:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count188
\l__pdf_internal_box=\box51
))
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (CTEX)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (e:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count189
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen140
\g__ctex_section_depth_int=\count190
\g__ctex_font_size_int=\count191
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(e:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-pdftex.def
File: ctex-engine-pdftex.def 2022/07/14 v2.5.10 (pdf)LaTeX adapter (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJKutf8.sty
Package: CJKutf8 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
 (e:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)) (e:/texlive/2024/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.sty
Package: CJK 2021/10/16 4.8.5
 (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/mule/MULEenc.sty
Package: MULEenc 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJK.enc
File: CJK.enc 2021/10/16 4.8.5
Now handling font encoding C00 ...
... no UTF-8 mapping file for font encoding C00
Now handling font encoding C05 ...
... no UTF-8 mapping file for font encoding C05
Now handling font encoding C09 ...
... no UTF-8 mapping file for font encoding C09
Now handling font encoding C10 ...
... no UTF-8 mapping file for font encoding C10
Now handling font encoding C20 ...
... no UTF-8 mapping file for font encoding C20
Now handling font encoding C19 ...
... no UTF-8 mapping file for font encoding C19
Now handling font encoding C40 ...
... no UTF-8 mapping file for font encoding C40
Now handling font encoding C42 ...
... no UTF-8 mapping file for font encoding C42
Now handling font encoding C43 ...
... no UTF-8 mapping file for font encoding C43
Now handling font encoding C50 ...
... no UTF-8 mapping file for font encoding C50
Now handling font encoding C52 ...
... no UTF-8 mapping file for font encoding C52
Now handling font encoding C49 ...
... no UTF-8 mapping file for font encoding C49
Now handling font encoding C60 ...
... no UTF-8 mapping file for font encoding C60
Now handling font encoding C61 ...
... no UTF-8 mapping file for font encoding C61
Now handling font encoding C63 ...
... no UTF-8 mapping file for font encoding C63
Now handling font encoding C64 ...
... no UTF-8 mapping file for font encoding C64
Now handling font encoding C65 ...
... no UTF-8 mapping file for font encoding C65
Now handling font encoding C70 ...
... no UTF-8 mapping file for font encoding C70
Now handling font encoding C31 ...
... no UTF-8 mapping file for font encoding C31
Now handling font encoding C32 ...
... no UTF-8 mapping file for font encoding C32
Now handling font encoding C33 ...
... no UTF-8 mapping file for font encoding C33
Now handling font encoding C34 ...
... no UTF-8 mapping file for font encoding C34
Now handling font encoding C35 ...
... no UTF-8 mapping file for font encoding C35
Now handling font encoding C36 ...
... no UTF-8 mapping file for font encoding C36
Now handling font encoding C37 ...
... no UTF-8 mapping file for font encoding C37
Now handling font encoding C80 ...
... no UTF-8 mapping file for font encoding C80
Now handling font encoding C81 ...
... no UTF-8 mapping file for font encoding C81
Now handling font encoding C01 ...
... no UTF-8 mapping file for font encoding C01
Now handling font encoding C11 ...
... no UTF-8 mapping file for font encoding C11
Now handling font encoding C21 ...
... no UTF-8 mapping file for font encoding C21
Now handling font encoding C41 ...
... no UTF-8 mapping file for font encoding C41
Now handling font encoding C62 ...
... no UTF-8 mapping file for font encoding C62
)
\CJK@indent=\box53
) (e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)) (e:/texlive/2024/texmf-dist/tex/latex/cjkpunct/CJKpunct.sty
Package: CJKpunct 2016/05/14 4.8.4
\CJKpunct@cnta=\count266
\CJKpunct@cntb=\count267
\CJKpunct@cntc=\count268
\CJKpunct@cntd=\count269
\CJKpunct@cnte=\count270
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2026 (decimal 8230)
 (e:/texlive/2024/texmf-dist/tex/latex/cjkpunct/CJKpunct.spa)) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/CJKspace.sty
Package: CJKspace 2021/10/16 3.8.0
) (e:/texlive/2024/texmf-dist/tex/latex/cjk/texinput/UTF8/UTF8.bdg
File: UTF8.bdg 2021/10/16 4.8.5
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexspa.def
File: ctexspa.def 2022/07/14 v2.5.10 Space info for CJKpunct (CTEX)
)
\ccwd=\dimen142
\l__ctex_ccglue_skip=\skip50
)
\l__ctex_ziju_dim=\dimen143
 (e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count271
\l__zhnum_tmp_int=\count272
 (e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
\l__ctex_heading_skip=\skip51
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese-article.def
File: ctex-scheme-chinese-article.def 2022/07/14 v2.5.10 Chinese scheme for article (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (e:/texlive/2024/texmf-dist/tex/latex/ctex/ctex-c5size.clo
File: ctex-c5size.clo 2022/07/14 v2.5.10 c5size option (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)
)) (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty (e:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX


e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty:95: Fatal Package fontspec Error: The fontspec package requires either XeTeX or
(fontspec)                      LuaTeX.
(fontspec)                      
(fontspec)                      You must change your typesetting engine to,
(fontspec)                      e.g., "xelatex" or "lualatex" instead of
(fontspec)                      "latex" or "pdflatex".

Type <return> to continue.
 ...                                              
                                                  
l.95 \msg_fatal:nn {fontspec} {cannot-use-pdftex}
                                                 

LaTeX does not know anything more about this error, sorry.

Try typing <return> to proceed.
If that doesn't work, type X <return> to quit.

This is a fatal error: LaTeX will abort.



e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty:95: Emergency stop.
<read *> 
         
l.95 \msg_fatal:nn {fontspec} {cannot-use-pdftex}
                                                 
*** (cannot \read from terminal in nonstop modes)

 
Here is how much of TeX's memory you used:
 4486 strings out of 474116
 96233 string characters out of 5747716
 1925190 words of memory out of 5000000
 26828 multiletter control sequences out of 15000+600000
 560320 words of font info for 41 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 80i,0n,83p,742b,348s stack positions out of 10000i,1000n,20000p,200000b,200000s
e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty:95:  ==> Fatal error occurred, no output PDF file produced!
