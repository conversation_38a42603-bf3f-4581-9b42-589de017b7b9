#!/usr/bin/env python3
"""
调试step_count问题
"""

def debug_step_count():
    """调试step_count问题"""
    print("🔍 调试step_count问题")
    print("=" * 60)
    
    try:
        from environments.paper_environment import PaperSimulationEnvironment
        from environments.environment_configs import FAST_TRAINING_CONFIG
        import numpy as np
        
        # 创建环境
        env = PaperSimulationEnvironment("stage1_simple")
        
        print(f"✅ 环境创建成功")
        print(f"📋 FAST_TRAINING_CONFIG: {FAST_TRAINING_CONFIG}")
        print(f"   max_steps_per_episode: {FAST_TRAINING_CONFIG['max_steps_per_episode']}")
        
        # 重置环境
        print(f"\n🔄 重置环境...")
        state = env.reset()
        print(f"   重置后step_count: {env.step_count}")
        print(f"   重置后状态: {state[:3]}")
        
        # 检查初始终止条件
        done, info = env._check_termination()
        print(f"   初始终止检查: done={done}, info={info}")
        
        if done:
            print(f"   ❌ 环境在重置后就被标记为完成！")
            return False
        
        # 执行第一步
        print(f"\n🚀 执行第一步...")
        action = np.array([2.0, 5.0, 0.1])
        print(f"   动作: {action}")
        print(f"   执行前step_count: {env.step_count}")
        
        next_state, reward, done, info = env.step(action)
        
        print(f"   执行后step_count: {env.step_count}")
        print(f"   新状态: {next_state[:3]}")
        print(f"   奖励: {reward:.3f}")
        print(f"   完成: {done}")
        print(f"   信息: {info}")
        
        # 详细检查终止条件
        if done:
            print(f"\n🔍 详细分析第1步终止原因:")
            
            # 手动检查各个终止条件
            current_pos = next_state[:3]
            distance_to_target = np.linalg.norm(current_pos - env.target_pos)
            print(f"   距离目标: {distance_to_target:.1f}m (阈值: 50m)")
            
            collision = env._check_collision()
            print(f"   碰撞检查: {collision}")
            
            max_steps = FAST_TRAINING_CONFIG["max_steps_per_episode"]
            timeout = env.step_count >= max_steps
            print(f"   超时检查: {timeout} (步数: {env.step_count}/{max_steps})")
            
            stall = next_state[3] < env.physics_config["V_min"]
            print(f"   失速检查: {stall} (速度: {next_state[3]:.2f}m/s, 最小: {env.physics_config['V_min']}m/s)")
            
            # 检查info中的具体原因
            if info.get('success'):
                print(f"   🎯 原因: 成功到达目标")
            elif info.get('collision'):
                print(f"   💥 原因: 碰撞")
            elif info.get('timeout'):
                print(f"   ⏰ 原因: 超时 - 这不应该在第1步发生！")
            elif info.get('stall'):
                print(f"   🛑 原因: 失速")
            else:
                print(f"   ❓ 原因: 未知")
        
        # 测试多次重置
        print(f"\n🔄 测试多次重置...")
        for i in range(3):
            state = env.reset()
            print(f"   重置 {i+1}: step_count={env.step_count}")
            
            # 执行一步
            next_state, reward, done, info = env.step(action)
            print(f"   执行后: step_count={env.step_count}, done={done}, info={info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 step_count问题调试")
    
    success = debug_step_count()
    
    if success:
        print("\n🎯 step_count调试完成！")
        print("请查看上述输出，找出step_count异常的原因")
    else:
        print("\n❌ step_count调试失败")

if __name__ == "__main__":
    main()
