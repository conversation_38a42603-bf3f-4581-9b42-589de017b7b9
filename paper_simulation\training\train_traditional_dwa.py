"""
传统DWA基线算法训练脚本
Traditional DWA Baseline Training Script

训练固定参数的传统动态窗口法
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import pickle

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from environments.paper_environment import PaperSimulationEnvironment
from core.loitering_munition_dwa import LoiteringMunitionDWA

class TraditionalDWATrainer:
    """传统DWA训练器（实际上是测试器，因为DWA不需要训练）"""
    
    def __init__(self, config=None, output_dir="results/traditional_dwa"):
        """
        初始化训练器
        
        Args:
            config: 测试配置
            output_dir: 输出目录
        """
        # 默认配置
        default_config = {
            "total_episodes": 500,
            "max_steps_per_episode": 500,
            "save_interval": 50,
            "eval_interval": 25,
            "eval_episodes": 10,
            "environment_stage": "stage2_complex",
            # DWA参数
            "dwa_config": {
                "delta_a_T": 4.0,      # 固定切向加速度分辨率
                "delta_a_N": 15.0,     # 固定法向加速度分辨率
                "delta_mu": 0.5,       # 固定倾斜角分辨率
                "prediction_time": 2.0, # 预测时间
                "safety_margin": 50.0,  # 安全边距
                "goal_weight": 1.0,     # 目标权重
                "obstacle_weight": 2.0, # 障碍物权重
                "velocity_weight": 0.5  # 速度权重
            }
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = os.path.join(output_dir, f"testing_{self.timestamp}")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 测试统计
        self.testing_stats = {
            "episode_rewards": [],
            "episode_lengths": [],
            "success_rates": [],
            "constraint_violations": [],
            "computation_times": [],
            "path_lengths": [],
            "eval_results": []
        }
        
        print(f"🚀 传统DWA测试器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎯 测试配置: {self.config}")
    
    def create_environment(self):
        """创建测试环境"""
        return PaperSimulationEnvironment(self.config["environment_stage"])
    
    def create_dwa_controller(self, environment):
        """创建DWA控制器"""
        return LoiteringMunitionDWA(environment, self.config["dwa_config"])
    
    def test(self):
        """测试传统DWA算法"""
        print("\n🎯 开始测试传统DWA算法")
        
        # 创建环境和控制器
        env = self.create_environment()
        dwa = self.create_dwa_controller(env)
        
        print(f"🏗️ 环境: {self.config['environment_stage']}")
        print(f"🤖 控制器: 传统DWA (固定参数)")
        print(f"   参数: {self.config['dwa_config']}")
        
        for episode in range(self.config["total_episodes"]):
            episode_reward = 0
            episode_length = 0
            constraint_violations = 0
            computation_times = []
            path_positions = []
            
            # 重置环境
            state = env.reset()
            done = False
            
            while not done and episode_length < self.config["max_steps_per_episode"]:
                # 记录计算时间
                start_time = datetime.now()
                
                # DWA选择动作
                action = dwa.select_action(state)
                
                end_time = datetime.now()
                computation_time = (end_time - start_time).total_seconds() * 1000  # 毫秒
                computation_times.append(computation_time)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 记录路径
                path_positions.append(state[:3].copy())
                
                # 检查约束违反
                if info.get("collision", False) or info.get("boundary_violation", False):
                    constraint_violations += 1
                    reward -= 100  # 约束违反惩罚
                
                state = next_state
                episode_reward += reward
                episode_length += 1
            
            # 计算路径长度
            path_length = 0
            if len(path_positions) > 1:
                for i in range(1, len(path_positions)):
                    path_length += np.linalg.norm(np.array(path_positions[i]) - np.array(path_positions[i-1]))
            
            # 记录episode统计
            self.testing_stats["episode_rewards"].append(episode_reward)
            self.testing_stats["episode_lengths"].append(episode_length)
            self.testing_stats["constraint_violations"].append(constraint_violations)
            self.testing_stats["computation_times"].append(np.mean(computation_times))
            self.testing_stats["path_lengths"].append(path_length)
            
            # 定期评估
            if (episode + 1) % self.config["eval_interval"] == 0:
                eval_result = self.evaluate_controller(dwa, env, episode)
                self.testing_stats["success_rates"].append(eval_result["success_rate"])
                
                print(f"Episode {episode + 1}: "
                      f"奖励={episode_reward:.1f}, "
                      f"长度={episode_length}, "
                      f"成功率={eval_result['success_rate']:.2%}, "
                      f"违反={constraint_violations}, "
                      f"计算时间={np.mean(computation_times):.2f}ms")
            
            # 定期保存
            if (episode + 1) % self.config["save_interval"] == 0:
                self.save_checkpoint(episode)
        
        print(f"\n✅ 测试完成")
        return self.testing_stats
    
    def evaluate_controller(self, dwa, env, episode):
        """评估控制器性能"""
        eval_rewards = []
        eval_successes = []
        eval_violations = []
        eval_lengths = []
        eval_computation_times = []
        
        for eval_ep in range(self.config["eval_episodes"]):
            eval_reward = 0
            eval_length = 0
            eval_constraint_violations = 0
            eval_comp_times = []
            
            state = env.reset()
            done = False
            
            while not done and eval_length < self.config["max_steps_per_episode"]:
                start_time = datetime.now()
                action = dwa.select_action(state)
                end_time = datetime.now()
                
                comp_time = (end_time - start_time).total_seconds() * 1000
                eval_comp_times.append(comp_time)
                
                next_state, reward, done, info = env.step(action)
                
                if info.get("collision", False) or info.get("boundary_violation", False):
                    eval_constraint_violations += 1
                
                state = next_state
                eval_reward += reward
                eval_length += 1
            
            # 判断是否成功
            success = info.get("success", False) and eval_constraint_violations == 0
            
            eval_rewards.append(eval_reward)
            eval_successes.append(success)
            eval_violations.append(eval_constraint_violations)
            eval_lengths.append(eval_length)
            eval_computation_times.append(np.mean(eval_comp_times))
        
        eval_result = {
            "episode": episode,
            "avg_reward": np.mean(eval_rewards),
            "success_rate": np.mean(eval_successes),
            "avg_violations": np.mean(eval_violations),
            "avg_length": np.mean(eval_lengths),
            "avg_computation_time": np.mean(eval_computation_times),
            "std_reward": np.std(eval_rewards)
        }
        
        self.testing_stats["eval_results"].append(eval_result)
        return eval_result
    
    def save_checkpoint(self, episode):
        """保存测试检查点"""
        checkpoint_path = os.path.join(self.output_dir, f"checkpoint_episode_{episode}.pkl")
        
        checkpoint_data = {
            "episode": episode,
            "testing_stats": self.testing_stats,
            "config": self.config,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)
    
    def save_final_results(self):
        """保存最终测试结果"""
        # 保存测试统计
        stats_path = os.path.join(self.output_dir, "testing_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(self.testing_stats, f, indent=2, default=str)
        
        # 保存配置
        config_path = os.path.join(self.output_dir, "testing_config.json")
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # 生成测试曲线图
        self.plot_testing_curves()
        
        print(f"📊 测试结果保存至: {self.output_dir}")
    
    def plot_testing_curves(self):
        """绘制测试曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        
        # 奖励曲线
        if self.testing_stats["episode_rewards"]:
            axes[0, 0].plot(self.testing_stats["episode_rewards"])
            axes[0, 0].set_title("Episode Rewards")
            axes[0, 0].set_xlabel("Episode")
            axes[0, 0].set_ylabel("Reward")
        
        # 成功率曲线
        if self.testing_stats["eval_results"]:
            eval_episodes = [r["episode"] for r in self.testing_stats["eval_results"]]
            success_rates = [r["success_rate"] for r in self.testing_stats["eval_results"]]
            axes[0, 1].plot(eval_episodes, success_rates)
            axes[0, 1].set_title("Success Rate")
            axes[0, 1].set_xlabel("Episode")
            axes[0, 1].set_ylabel("Success Rate")
        
        # 约束违反
        if self.testing_stats["constraint_violations"]:
            axes[0, 2].plot(self.testing_stats["constraint_violations"])
            axes[0, 2].set_title("Constraint Violations")
            axes[0, 2].set_xlabel("Episode")
            axes[0, 2].set_ylabel("Violations")
        
        # Episode长度
        if self.testing_stats["episode_lengths"]:
            axes[1, 0].plot(self.testing_stats["episode_lengths"])
            axes[1, 0].set_title("Episode Lengths")
            axes[1, 0].set_xlabel("Episode")
            axes[1, 0].set_ylabel("Steps")
        
        # 计算时间
        if self.testing_stats["computation_times"]:
            axes[1, 1].plot(self.testing_stats["computation_times"])
            axes[1, 1].set_title("Computation Time")
            axes[1, 1].set_xlabel("Episode")
            axes[1, 1].set_ylabel("Time (ms)")
        
        # 路径长度
        if self.testing_stats["path_lengths"]:
            axes[1, 2].plot(self.testing_stats["path_lengths"])
            axes[1, 2].set_title("Path Length")
            axes[1, 2].set_xlabel("Episode")
            axes[1, 2].set_ylabel("Length (m)")
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "testing_curves.png"), dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    print("🚀 开始测试传统DWA基线算法")
    
    # 测试配置
    config = {
        "total_episodes": 500,
        "max_steps_per_episode": 500,
        "save_interval": 50,
        "eval_interval": 25,
        "eval_episodes": 10,
        "environment_stage": "stage2_complex"
    }
    
    # 创建测试器
    trainer = TraditionalDWATrainer(config)
    
    # 开始测试
    start_time = datetime.now()
    testing_stats = trainer.test()
    end_time = datetime.now()
    testing_duration = end_time - start_time
    
    # 保存结果
    trainer.save_final_results()
    
    print(f"\n✅ 传统DWA测试完成!")
    print(f"⏱️ 测试时间: {testing_duration}")
    print(f"📊 最终统计:")
    
    if testing_stats["episode_rewards"]:
        print(f"   平均奖励: {np.mean(testing_stats['episode_rewards'][-50:]):.2f}")
    
    if testing_stats["eval_results"]:
        final_success_rate = testing_stats["eval_results"][-1]["success_rate"]
        print(f"   最终成功率: {final_success_rate:.2%}")
    
    if testing_stats["constraint_violations"]:
        avg_violations = np.mean(testing_stats["constraint_violations"][-50:])
        print(f"   平均约束违反: {avg_violations:.2f}")
    
    if testing_stats["computation_times"]:
        avg_comp_time = np.mean(testing_stats["computation_times"][-50:])
        print(f"   平均计算时间: {avg_comp_time:.2f}ms")

if __name__ == "__main__":
    main()
